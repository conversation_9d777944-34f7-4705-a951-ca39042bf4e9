# BahinLink Vercel Configuration Guide

## ⚠️ CRITICAL REQUIREMENT: REAL PRODUCTION SERVICES
**NO MOCK DATA**: All Vercel deployments must connect to real production databases, authentication services, and external APIs.

## Project Structure for Vercel Deployment

### API Project Structure
```
bahinlink-api/
├── api/
│   ├── auth/
│   │   ├── profile.js
│   │   └── setup.js
│   ├── agents/
│   │   ├── index.js
│   │   ├── [id].js
│   │   ├── location.js
│   │   └── nearby.js
│   ├── sites/
│   │   ├── index.js
│   │   ├── [id].js
│   │   └── qr-generate.js
│   ├── shifts/
│   │   ├── index.js
│   │   ├── [id].js
│   │   └── me.js
│   ├── time/
│   │   ├── clock-in.js
│   │   ├── clock-out.js
│   │   └── entries.js
│   ├── reports/
│   │   ├── index.js
│   │   ├── [id].js
│   │   ├── approve.js
│   │   └── signature.js
│   ├── notifications/
│   │   ├── index.js
│   │   └── [id].js
│   ├── communications/
│   │   ├── index.js
│   │   └── threads.js
│   ├── upload/
│   │   ├── photo.js
│   │   ├── video.js
│   │   └── document.js
│   ├── analytics/
│   │   ├── dashboard.js
│   │   └── performance.js
│   ├── geofence/
│   │   ├── check.js
│   │   └── violations.js
│   └── webhooks/
│       └── clerk.js
├── lib/
│   ├── prisma.js
│   ├── auth.js
│   ├── redis.js
│   ├── storage.js
│   ├── geofence.js
│   └── utils.js
├── prisma/
│   ├── schema.prisma
│   └── seed.js
├── package.json
├── vercel.json
└── .env.example
```

## Vercel Configuration Files

### 1. vercel.json (API Project)
```json
{
  "version": 2,
  "name": "bahinlink-api",
  "builds": [
    {
      "src": "api/**/*.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  },
  "functions": {
    "api/**/*.js": {
      "maxDuration": 30,
      "memory": 1024
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "https://admin.bahinlink.com, https://client.bahinlink.com"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Authorization, Content-Type"
        }
      ]
    }
  ]
}
```

### 2. vercel.json (Web Admin)
```json
{
  "version": 2,
  "name": "bahinlink-admin",
  "builds": [
    {
      "src": "build/**",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/build/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
```

### 3. vercel.json (Client Portal)
```json
{
  "version": 2,
  "name": "bahinlink-client",
  "builds": [
    {
      "src": "build/**",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/build/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

## Environment Variables Configuration

### Production Environment Variables (Vercel Dashboard)
```bash
# Database (Real Neon PostgreSQL)
DATABASE_URL="postgresql://bahinlink:<EMAIL>/bahinlink?sslmode=require"

# Clerk Authentication (Real Production)
CLERK_PUBLISHABLE_KEY="pk_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
CLERK_SECRET_KEY="sk_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
CLERK_WEBHOOK_SECRET="whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Vercel Blob Storage (Real Production)
BLOB_READ_WRITE_TOKEN="vercel_blob_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Upstash Redis (Real Production)
UPSTASH_REDIS_REST_URL="https://xyz.upstash.io"
UPSTASH_REDIS_REST_TOKEN="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Google Maps API (Real Production)
GOOGLE_MAPS_API_KEY="AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Firebase (Real Production)
FIREBASE_SERVER_KEY="AAAAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
FIREBASE_PROJECT_ID="bahinlink-production"

# Communication Services (Real Production)
SENDGRID_API_KEY="SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_ACCOUNT_SID="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_AUTH_TOKEN="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Sentry (Real Production)
SENTRY_DSN="https://<EMAIL>/xxxxxxx"

# Application URLs
API_BASE_URL="https://api.bahinlink.com"
WEB_BASE_URL="https://admin.bahinlink.com"
CLIENT_BASE_URL="https://client.bahinlink.com"
```

## Deployment Commands

### 1. Initial Setup
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Link projects to Vercel
cd bahinlink-api && vercel link
cd bahinlink-admin && vercel link
cd bahinlink-client && vercel link
```

### 2. Environment Variables Setup
```bash
# Set environment variables for API
cd bahinlink-api
vercel env add DATABASE_URL production
vercel env add CLERK_SECRET_KEY production
vercel env add UPSTASH_REDIS_REST_URL production
vercel env add GOOGLE_MAPS_API_KEY production
vercel env add FIREBASE_SERVER_KEY production
vercel env add SENDGRID_API_KEY production
vercel env add TWILIO_ACCOUNT_SID production
vercel env add BLOB_READ_WRITE_TOKEN production

# Set environment variables for frontend apps
cd bahinlink-admin
vercel env add REACT_APP_CLERK_PUBLISHABLE_KEY production
vercel env add REACT_APP_API_BASE_URL production

cd bahinlink-client
vercel env add REACT_APP_CLERK_PUBLISHABLE_KEY production
vercel env add REACT_APP_API_BASE_URL production
```

### 3. Production Deployment
```bash
# Deploy API
cd bahinlink-api
vercel --prod

# Deploy Web Admin
cd bahinlink-admin
npm run build
vercel --prod

# Deploy Client Portal
cd bahinlink-client
npm run build
vercel --prod
```

### 4. Custom Domains
```bash
# Add custom domains
vercel domains add api.bahinlink.com --scope=bahinlink-api
vercel domains add admin.bahinlink.com --scope=bahinlink-admin
vercel domains add client.bahinlink.com --scope=bahinlink-client

# Verify domain ownership and configure DNS
```

## Vercel-Specific Code Examples

### 1. API Function Example (api/agents/location.js)
```javascript
import { PrismaClient } from '@prisma/client';
import { requireAuth } from '../../lib/auth';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const user = await requireAuth(req);
    const { latitude, longitude, accuracy } = req.body;

    // Update agent location in real database
    await prisma.agent.update({
      where: { userId: user.id },
      data: {
        currentLatitude: latitude,
        currentLongitude: longitude,
        lastLocationUpdate: new Date()
      }
    });

    res.json({ success: true });
  } catch (error) {
    console.error('Location update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  } finally {
    await prisma.$disconnect();
  }
}
```

### 2. File Upload with Vercel Blob (api/upload/photo.js)
```javascript
import { put } from '@vercel/blob';
import { requireAuth } from '../../lib/auth';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await requireAuth(req);
    
    const { file } = req.body;
    const filename = `photos/${Date.now()}-${file.name}`;
    
    // Upload to Vercel Blob (real cloud storage)
    const blob = await put(filename, file, {
      access: 'public',
    });

    res.json({ 
      success: true, 
      data: { url: blob.url } 
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
}
```

### 3. Redis Cache Integration (lib/redis.js)
```javascript
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL,
  token: process.env.UPSTASH_REDIS_REST_TOKEN,
});

export async function getCached(key) {
  try {
    return await redis.get(key);
  } catch (error) {
    console.error('Redis get error:', error);
    return null;
  }
}

export async function setCached(key, value, ttl = 3600) {
  try {
    return await redis.setex(key, ttl, JSON.stringify(value));
  } catch (error) {
    console.error('Redis set error:', error);
    return false;
  }
}
```

## Performance Optimization

### 1. Edge Functions for Location Services
```javascript
// api/geofence/check.js - Edge function for fast geofence checking
export const config = {
  runtime: 'edge',
};

export default async function handler(req) {
  const { siteId, latitude, longitude } = await req.json();
  
  // Fast geofence calculation at the edge
  const site = await getSiteFromCache(siteId);
  const distance = calculateDistance(
    latitude, longitude,
    site.latitude, site.longitude
  );
  
  return new Response(JSON.stringify({
    withinGeofence: distance <= site.geofenceRadius,
    distance
  }), {
    headers: { 'content-type': 'application/json' },
  });
}
```

### 2. Database Connection Optimization
```javascript
// lib/prisma.js - Optimized for serverless
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis;

export const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}
```

## Monitoring and Analytics

### 1. Vercel Analytics Integration
```javascript
// Add to React apps
import { Analytics } from '@vercel/analytics/react';

function App() {
  return (
    <>
      <YourApp />
      <Analytics />
    </>
  );
}
```

### 2. Performance Monitoring
```javascript
// api/_middleware.js - Global middleware for monitoring
import { NextResponse } from 'next/server';

export function middleware(request) {
  const start = Date.now();
  
  const response = NextResponse.next();
  
  // Add performance headers
  response.headers.set('X-Response-Time', `${Date.now() - start}ms`);
  
  return response;
}
```

This configuration ensures optimal performance, security, and scalability on Vercel while maintaining real production data integration throughout the system.
