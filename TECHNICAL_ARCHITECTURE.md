# BahinLink Technical Architecture Documentation

## Project Overview
BahinLink is a mobile-first workforce management solution for Bahin SARL security company. This document outlines the technical architecture for building a production-ready system within a 2-hour timeline.

## ⚠️ CRITICAL REQUIREMENT: REAL DATA ONLY
**NO MOCK DATA ALLOWED**: All implementations must use real, production-ready data sources and integrations. Every component, API, and feature must connect to actual databases, external APIs, and live data sources.

## Architecture Overview

### System Architecture Pattern
- **Frontend**: React Native (Android primary, web admin secondary)
- **Backend**: Node.js with Express.js
- **Database**: PostgreSQL with real-time capabilities
- **Real-time Communication**: Socket.io
- **Authentication**: Clerk (handles JWT, sessions, user management)
- **File Storage**: AWS S3 or similar cloud storage
- **Maps & Location**: Google Maps API with real GPS tracking
- **Push Notifications**: Firebase Cloud Messaging (FCM)

### High-Level Architecture Diagram
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web Admin     │    │  Client Portal  │
│   (React Native)│    │   (React)       │    │   (React)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     API Gateway         │
                    │  (Express.js + Clerk)   │
                    └─────────────┬───────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │   Business Logic Layer  │
                    │   (Node.js Services)    │
                    └─────────────┬───────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────┴───────┐    ┌─────────┴───────┐    ┌─────────┴───────┐
│   PostgreSQL    │    │   Redis Cache   │    │   File Storage  │
│   (Primary DB)  │    │   (Sessions)    │    │   (AWS S3)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Technology Stack

### Frontend Technologies
1. **Mobile App (Primary)**
   - React Native 0.72+
   - React Navigation 6
   - React Native Maps (Google Maps)
   - React Native Geolocation
   - React Native Camera
   - React Native QR Code Scanner
   - React Native Push Notifications
   - AsyncStorage for offline data

2. **Web Admin Dashboard**
   - React 18+
   - Material-UI or Ant Design
   - React Router
   - Socket.io client
   - Chart.js for analytics

3. **Client Portal**
   - React 18+
   - Responsive design
   - Real-time updates via Socket.io

### Backend Technologies
1. **API Server**
   - Node.js 18+
   - Express.js 4.18+
   - Socket.io for real-time features
   - Clerk for authentication & user management
   - Prisma ORM for database operations
   - multer for file uploads

2. **Database & Storage**
   - PostgreSQL 15+ (primary database)
   - Redis 7+ (caching and sessions)
   - AWS S3 (file storage)
   - Prisma for database schema & migrations

3. **External Integrations**
   - Clerk (authentication, user management, webhooks)
   - Google Maps API (geocoding, directions)
   - Firebase Cloud Messaging (push notifications)
   - Twilio (SMS notifications)
   - SendGrid (email notifications)

## Database Schema Design

### Core Tables
```sql
-- Users table (all user types)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'supervisor', 'agent', 'client')),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Agents table (security personnel)
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) UNIQUE,
    photo_url VARCHAR(500),
    certifications JSONB,
    skills JSONB,
    availability JSONB,
    performance_stats JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    billing_address TEXT,
    service_level VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sites table (client locations)
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    geofence_radius INTEGER DEFAULT 100, -- meters
    qr_code VARCHAR(255) UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shifts table
CREATE TABLE shifts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
    supervisor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    actual_start_time TIMESTAMP,
    actual_end_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Time tracking table
CREATE TABLE time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shift_id UUID REFERENCES shifts(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    clock_in_time TIMESTAMP,
    clock_out_time TIMESTAMP,
    clock_in_location POINT,
    clock_out_location POINT,
    clock_in_method VARCHAR(20) CHECK (clock_in_method IN ('gps', 'qr_code', 'manual')),
    clock_out_method VARCHAR(20) CHECK (clock_out_method IN ('gps', 'qr_code', 'manual')),
    total_hours DECIMAL(5,2),
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reports table
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('patrol', 'incident', 'inspection')),
    shift_id UUID REFERENCES shifts(id) ON DELETE CASCADE,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    supervisor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    observations TEXT,
    incidents TEXT,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved', 'rejected')),
    client_signature JSONB,
    photos JSONB, -- array of photo URLs
    videos JSONB, -- array of video URLs
    location POINT,
    submitted_at TIMESTAMP,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_read BOOLEAN DEFAULT false,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP
);

-- Communication logs table
CREATE TABLE communications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file')),
    content TEXT NOT NULL,
    attachments JSONB,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP
);

-- Audit logs table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client requests table
CREATE TABLE client_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    request_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Indexes for Performance
```sql
-- Performance indexes
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_shifts_date_agent ON shifts(shift_date, agent_id);
CREATE INDEX idx_shifts_site_date ON shifts(site_id, shift_date);
CREATE INDEX idx_reports_agent_date ON reports(agent_id, created_at);
CREATE INDEX idx_notifications_recipient_unread ON notifications(recipient_id, is_read);
CREATE INDEX idx_time_entries_shift ON time_entries(shift_id);
CREATE INDEX idx_audit_logs_user_date ON audit_logs(user_id, created_at);
```

## API Specifications

### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/forgot-password
POST /api/auth/reset-password
```

### User Management
```
GET /api/users
POST /api/users
GET /api/users/:id
PUT /api/users/:id
DELETE /api/users/:id
```

### Agent Management
```
GET /api/agents
POST /api/agents
GET /api/agents/:id
PUT /api/agents/:id
GET /api/agents/:id/performance
GET /api/agents/:id/location
```

### Scheduling
```
GET /api/shifts
POST /api/shifts
GET /api/shifts/:id
PUT /api/shifts/:id
DELETE /api/shifts/:id
GET /api/shifts/agent/:agentId
GET /api/shifts/site/:siteId
```

### Time Tracking
```
POST /api/time/clock-in
POST /api/time/clock-out
GET /api/time/entries/:shiftId
PUT /api/time/entries/:id/verify
```

### Reporting
```
GET /api/reports
POST /api/reports
GET /api/reports/:id
PUT /api/reports/:id
POST /api/reports/:id/approve
POST /api/reports/:id/reject
GET /api/reports/site/:siteId
```

### Real-time Features (Socket.io)
```
- location-update
- notification-new
- shift-status-change
- report-submitted
- emergency-alert
```

## Implementation Roadmap (2-Hour Timeline)

### Phase 1: Foundation (30 minutes)
1. Set up project structure
2. Initialize database with real PostgreSQL instance
3. Create basic Express.js API server
4. Implement JWT authentication
5. Set up React Native project structure

### Phase 2: Core Features (60 minutes)
1. User management and authentication
2. Real GPS tracking implementation
3. Time tracking with geofencing
4. Basic reporting system
5. Real-time notifications

### Phase 3: Advanced Features (25 minutes)
1. Client portal
2. File upload for photos/videos
3. QR code scanning
4. Dashboard analytics
5. Offline functionality

### Phase 4: Testing & Deployment (5 minutes)
1. Integration testing
2. Production deployment setup
3. Final verification

## Deployment Strategy

### Production Environment
- **Cloud Provider**: AWS or Google Cloud
- **Database**: Managed PostgreSQL (AWS RDS/Google Cloud SQL)
- **API Server**: Docker containers on ECS/GKE
- **File Storage**: AWS S3/Google Cloud Storage
- **CDN**: CloudFront/Cloud CDN
- **Monitoring**: CloudWatch/Stackdriver

### Environment Configuration
```
Production:
- Database: Real PostgreSQL with SSL
- Redis: Real Redis cluster
- File Storage: Real AWS S3 bucket
- Maps API: Real Google Maps API key
- Push Notifications: Real Firebase project
- Email: Real SendGrid account
- SMS: Real Twilio account
```

### Security Measures
- HTTPS/TLS encryption
- JWT with short expiration
- Password hashing with bcrypt
- Input validation and sanitization
- Rate limiting
- CORS configuration
- SQL injection prevention
- XSS protection

## Performance Considerations
- Database connection pooling
- Redis caching for frequently accessed data
- Image compression and optimization
- Lazy loading for mobile app
- Pagination for large datasets
- Background job processing for heavy tasks

## Offline Functionality
- Local SQLite database for mobile app
- Sync queue for offline actions
- Conflict resolution strategies
- Priority-based sync
- Background sync when connection restored

This architecture ensures a production-ready system with real data integration, scalability, and security while meeting the 2-hour delivery timeline.
