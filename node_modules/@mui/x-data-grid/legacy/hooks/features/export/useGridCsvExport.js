import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _typeof from "@babel/runtime/helpers/esm/typeof";
import * as React from 'react';
import { useGridApiMethod } from '../../utils/useGridApiMethod';
import { useGridLogger } from '../../utils/useGridLogger';
import { exportAs } from '../../../utils/exportAs';
import { buildCSV } from './serializers/csvSerializer';
import { getColumnsToExport, defaultGetRowsToExport } from './utils';
import { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';
import { GridCsvExportMenuItem } from '../../../components/toolbar/GridToolbarExport';
import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @requires useGridColumns (state)
 * @requires useGridFilter (state)
 * @requires useGridSorting (state)
 * @requires useGridSelection (state)
 * @requires useGridParamsApi (method)
 */
export var useGridCsvExport = function useGridCsvExport(apiRef, props) {
  var logger = useGridLogger(apiRef, 'useGridCsvExport');
  var ignoreValueFormatterProp = props.unstable_ignoreValueFormatterDuringExport;
  var ignoreValueFormatter = (_typeof(ignoreValueFormatterProp) === 'object' ? ignoreValueFormatterProp == null ? void 0 : ignoreValueFormatterProp.csvExport : ignoreValueFormatterProp) || false;
  var getDataAsCsv = React.useCallback(function () {
    var _options$getRowsToExp, _options$shouldAppend, _options$includeHeade, _options$includeColum, _options$escapeFormul;
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    logger.debug("Get data as CSV");
    var exportedColumns = getColumnsToExport({
      apiRef: apiRef,
      options: options
    });
    var getRowsToExport = (_options$getRowsToExp = options.getRowsToExport) != null ? _options$getRowsToExp : defaultGetRowsToExport;
    var exportedRowIds = getRowsToExport({
      apiRef: apiRef
    });
    return buildCSV({
      columns: exportedColumns,
      rowIds: exportedRowIds,
      csvOptions: {
        delimiter: options.delimiter || ',',
        shouldAppendQuotes: (_options$shouldAppend = options.shouldAppendQuotes) != null ? _options$shouldAppend : true,
        includeHeaders: (_options$includeHeade = options.includeHeaders) != null ? _options$includeHeade : true,
        includeColumnGroupsHeaders: (_options$includeColum = options.includeColumnGroupsHeaders) != null ? _options$includeColum : true,
        escapeFormulas: (_options$escapeFormul = options.escapeFormulas) != null ? _options$escapeFormul : true
      },
      ignoreValueFormatter: ignoreValueFormatter,
      apiRef: apiRef
    });
  }, [logger, apiRef, ignoreValueFormatter]);
  var exportDataAsCsv = React.useCallback(function (options) {
    logger.debug("Export data as CSV");
    var csv = getDataAsCsv(options);
    var blob = new Blob([options != null && options.utf8WithBom ? new Uint8Array([0xef, 0xbb, 0xbf]) : '', csv], {
      type: 'text/csv'
    });
    exportAs(blob, 'csv', options == null ? void 0 : options.fileName);
  }, [logger, getDataAsCsv]);
  var csvExportApi = {
    getDataAsCsv: getDataAsCsv,
    exportDataAsCsv: exportDataAsCsv
  };
  useGridApiMethod(apiRef, csvExportApi, 'public');

  /**
   * PRE-PROCESSING
   */
  var addExportMenuButtons = React.useCallback(function (initialValue, options) {
    var _options$csvOptions;
    if ((_options$csvOptions = options.csvOptions) != null && _options$csvOptions.disableToolbarButton) {
      return initialValue;
    }
    return [].concat(_toConsumableArray(initialValue), [{
      component: /*#__PURE__*/_jsx(GridCsvExportMenuItem, {
        options: options.csvOptions
      }),
      componentName: 'csvExport'
    }]);
  }, []);
  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);
};