"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomPage = exports.RoomListInstance = exports.RoomInstance = exports.RoomContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const participant_1 = require("./room/participant");
const recordingRules_1 = require("./room/recordingRules");
const roomRecording_1 = require("./room/roomRecording");
class RoomContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Rooms/${sid}`;
    }
    get participants() {
        this._participants =
            this._participants ||
                (0, participant_1.ParticipantListInstance)(this._version, this._solution.sid);
        return this._participants;
    }
    get recordingRules() {
        this._recordingRules =
            this._recordingRules ||
                (0, recordingRules_1.RecordingRulesListInstance)(this._version, this._solution.sid);
        return this._recordingRules;
    }
    get recordings() {
        this._recordings =
            this._recordings ||
                (0, roomRecording_1.RoomRecordingListInstance)(this._version, this._solution.sid);
        return this._recordings;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new RoomInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["status"] === null || params["status"] === undefined) {
            throw new Error("Required parameter \"params['status']\" missing.");
        }
        let data = {};
        data["Status"] = params["status"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RoomInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoomContextImpl = RoomContextImpl;
class RoomInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.status = payload.status;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.accountSid = payload.account_sid;
        this.enableTurn = payload.enable_turn;
        this.uniqueName = payload.unique_name;
        this.statusCallback = payload.status_callback;
        this.statusCallbackMethod = payload.status_callback_method;
        this.endTime = deserialize.iso8601DateTime(payload.end_time);
        this.duration = deserialize.integer(payload.duration);
        this.type = payload.type;
        this.maxParticipants = deserialize.integer(payload.max_participants);
        this.maxParticipantDuration = deserialize.integer(payload.max_participant_duration);
        this.maxConcurrentPublishedTracks = deserialize.integer(payload.max_concurrent_published_tracks);
        this.recordParticipantsOnConnect = payload.record_participants_on_connect;
        this.videoCodecs = payload.video_codecs;
        this.mediaRegion = payload.media_region;
        this.audioOnly = payload.audio_only;
        this.emptyRoomTimeout = deserialize.integer(payload.empty_room_timeout);
        this.unusedRoomTimeout = deserialize.integer(payload.unused_room_timeout);
        this.largeRoom = payload.large_room;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context || new RoomContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a RoomInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed RoomInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the participants.
     */
    participants() {
        return this._proxy.participants;
    }
    /**
     * Access the recordingRules.
     */
    recordingRules() {
        return this._proxy.recordingRules;
    }
    /**
     * Access the recordings.
     */
    recordings() {
        return this._proxy.recordings;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            status: this.status,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            accountSid: this.accountSid,
            enableTurn: this.enableTurn,
            uniqueName: this.uniqueName,
            statusCallback: this.statusCallback,
            statusCallbackMethod: this.statusCallbackMethod,
            endTime: this.endTime,
            duration: this.duration,
            type: this.type,
            maxParticipants: this.maxParticipants,
            maxParticipantDuration: this.maxParticipantDuration,
            maxConcurrentPublishedTracks: this.maxConcurrentPublishedTracks,
            recordParticipantsOnConnect: this.recordParticipantsOnConnect,
            videoCodecs: this.videoCodecs,
            mediaRegion: this.mediaRegion,
            audioOnly: this.audioOnly,
            emptyRoomTimeout: this.emptyRoomTimeout,
            unusedRoomTimeout: this.unusedRoomTimeout,
            largeRoom: this.largeRoom,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoomInstance = RoomInstance;
function RoomListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new RoomContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Rooms`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["enableTurn"] !== undefined)
            data["EnableTurn"] = serialize.bool(params["enableTurn"]);
        if (params["type"] !== undefined)
            data["Type"] = params["type"];
        if (params["uniqueName"] !== undefined)
            data["UniqueName"] = params["uniqueName"];
        if (params["statusCallback"] !== undefined)
            data["StatusCallback"] = params["statusCallback"];
        if (params["statusCallbackMethod"] !== undefined)
            data["StatusCallbackMethod"] = params["statusCallbackMethod"];
        if (params["maxParticipants"] !== undefined)
            data["MaxParticipants"] = params["maxParticipants"];
        if (params["recordParticipantsOnConnect"] !== undefined)
            data["RecordParticipantsOnConnect"] = serialize.bool(params["recordParticipantsOnConnect"]);
        if (params["videoCodecs"] !== undefined)
            data["VideoCodecs"] = serialize.map(params["videoCodecs"], (e) => e);
        if (params["mediaRegion"] !== undefined)
            data["MediaRegion"] = params["mediaRegion"];
        if (params["recordingRules"] !== undefined)
            data["RecordingRules"] = serialize.object(params["recordingRules"]);
        if (params["audioOnly"] !== undefined)
            data["AudioOnly"] = serialize.bool(params["audioOnly"]);
        if (params["maxParticipantDuration"] !== undefined)
            data["MaxParticipantDuration"] = params["maxParticipantDuration"];
        if (params["emptyRoomTimeout"] !== undefined)
            data["EmptyRoomTimeout"] = params["emptyRoomTimeout"];
        if (params["unusedRoomTimeout"] !== undefined)
            data["UnusedRoomTimeout"] = params["unusedRoomTimeout"];
        if (params["largeRoom"] !== undefined)
            data["LargeRoom"] = serialize.bool(params["largeRoom"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RoomInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["uniqueName"] !== undefined)
            data["UniqueName"] = params["uniqueName"];
        if (params["dateCreatedAfter"] !== undefined)
            data["DateCreatedAfter"] = serialize.iso8601DateTime(params["dateCreatedAfter"]);
        if (params["dateCreatedBefore"] !== undefined)
            data["DateCreatedBefore"] = serialize.iso8601DateTime(params["dateCreatedBefore"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RoomPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new RoomPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.RoomListInstance = RoomListInstance;
class RoomPage extends Page_1.default {
    /**
     * Initialize the RoomPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of RoomInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new RoomInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoomPage = RoomPage;
