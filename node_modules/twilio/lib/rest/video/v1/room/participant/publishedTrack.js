"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublishedTrackPage = exports.PublishedTrackListInstance = exports.PublishedTrackInstance = exports.PublishedTrackContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class PublishedTrackContextImpl {
    constructor(_version, roomSid, participantSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(roomSid)) {
            throw new Error("Parameter 'roomSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(participantSid)) {
            throw new Error("Parameter 'participantSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { roomSid, participantSid, sid };
        this._uri = `/Rooms/${roomSid}/Participants/${participantSid}/PublishedTracks/${sid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new PublishedTrackInstance(operationVersion, payload, instance._solution.roomSid, instance._solution.participantSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PublishedTrackContextImpl = PublishedTrackContextImpl;
class PublishedTrackInstance {
    constructor(_version, payload, roomSid, participantSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.participantSid = payload.participant_sid;
        this.roomSid = payload.room_sid;
        this.name = payload.name;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.enabled = payload.enabled;
        this.kind = payload.kind;
        this.url = payload.url;
        this._solution = { roomSid, participantSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new PublishedTrackContextImpl(this._version, this._solution.roomSid, this._solution.participantSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a PublishedTrackInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed PublishedTrackInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            participantSid: this.participantSid,
            roomSid: this.roomSid,
            name: this.name,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            enabled: this.enabled,
            kind: this.kind,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PublishedTrackInstance = PublishedTrackInstance;
function PublishedTrackListInstance(version, roomSid, participantSid) {
    if (!(0, utility_1.isValidPathParam)(roomSid)) {
        throw new Error("Parameter 'roomSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(participantSid)) {
        throw new Error("Parameter 'participantSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new PublishedTrackContextImpl(version, roomSid, participantSid, sid);
    };
    instance._version = version;
    instance._solution = { roomSid, participantSid };
    instance._uri = `/Rooms/${roomSid}/Participants/${participantSid}/PublishedTracks`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new PublishedTrackPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new PublishedTrackPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.PublishedTrackListInstance = PublishedTrackListInstance;
class PublishedTrackPage extends Page_1.default {
    /**
     * Initialize the PublishedTrackPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of PublishedTrackInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new PublishedTrackInstance(this._version, payload, this._solution.roomSid, this._solution.participantSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PublishedTrackPage = PublishedTrackPage;
