"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Supersim
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FleetPage = exports.FleetListInstance = exports.FleetInstance = exports.FleetContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class FleetContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Fleets/${sid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new FleetInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["uniqueName"] !== undefined)
            data["UniqueName"] = params["uniqueName"];
        if (params["networkAccessProfile"] !== undefined)
            data["NetworkAccessProfile"] = params["networkAccessProfile"];
        if (params["ipCommandsUrl"] !== undefined)
            data["IpCommandsUrl"] = params["ipCommandsUrl"];
        if (params["ipCommandsMethod"] !== undefined)
            data["IpCommandsMethod"] = params["ipCommandsMethod"];
        if (params["smsCommandsUrl"] !== undefined)
            data["SmsCommandsUrl"] = params["smsCommandsUrl"];
        if (params["smsCommandsMethod"] !== undefined)
            data["SmsCommandsMethod"] = params["smsCommandsMethod"];
        if (params["dataLimit"] !== undefined)
            data["DataLimit"] = params["dataLimit"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new FleetInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.FleetContextImpl = FleetContextImpl;
class FleetInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.sid = payload.sid;
        this.uniqueName = payload.unique_name;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.dataEnabled = payload.data_enabled;
        this.dataLimit = deserialize.integer(payload.data_limit);
        this.dataMetering = payload.data_metering;
        this.smsCommandsEnabled = payload.sms_commands_enabled;
        this.smsCommandsUrl = payload.sms_commands_url;
        this.smsCommandsMethod = payload.sms_commands_method;
        this.networkAccessProfileSid = payload.network_access_profile_sid;
        this.ipCommandsUrl = payload.ip_commands_url;
        this.ipCommandsMethod = payload.ip_commands_method;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context || new FleetContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a FleetInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed FleetInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            sid: this.sid,
            uniqueName: this.uniqueName,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            dataEnabled: this.dataEnabled,
            dataLimit: this.dataLimit,
            dataMetering: this.dataMetering,
            smsCommandsEnabled: this.smsCommandsEnabled,
            smsCommandsUrl: this.smsCommandsUrl,
            smsCommandsMethod: this.smsCommandsMethod,
            networkAccessProfileSid: this.networkAccessProfileSid,
            ipCommandsUrl: this.ipCommandsUrl,
            ipCommandsMethod: this.ipCommandsMethod,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.FleetInstance = FleetInstance;
function FleetListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new FleetContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Fleets`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["networkAccessProfile"] === null ||
            params["networkAccessProfile"] === undefined) {
            throw new Error("Required parameter \"params['networkAccessProfile']\" missing.");
        }
        let data = {};
        data["NetworkAccessProfile"] = params["networkAccessProfile"];
        if (params["uniqueName"] !== undefined)
            data["UniqueName"] = params["uniqueName"];
        if (params["dataEnabled"] !== undefined)
            data["DataEnabled"] = serialize.bool(params["dataEnabled"]);
        if (params["dataLimit"] !== undefined)
            data["DataLimit"] = params["dataLimit"];
        if (params["ipCommandsUrl"] !== undefined)
            data["IpCommandsUrl"] = params["ipCommandsUrl"];
        if (params["ipCommandsMethod"] !== undefined)
            data["IpCommandsMethod"] = params["ipCommandsMethod"];
        if (params["smsCommandsEnabled"] !== undefined)
            data["SmsCommandsEnabled"] = serialize.bool(params["smsCommandsEnabled"]);
        if (params["smsCommandsUrl"] !== undefined)
            data["SmsCommandsUrl"] = params["smsCommandsUrl"];
        if (params["smsCommandsMethod"] !== undefined)
            data["SmsCommandsMethod"] = params["smsCommandsMethod"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new FleetInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["networkAccessProfile"] !== undefined)
            data["NetworkAccessProfile"] = params["networkAccessProfile"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new FleetPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new FleetPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.FleetListInstance = FleetListInstance;
class FleetPage extends Page_1.default {
    /**
     * Initialize the FleetPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of FleetInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new FleetInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.FleetPage = FleetPage;
