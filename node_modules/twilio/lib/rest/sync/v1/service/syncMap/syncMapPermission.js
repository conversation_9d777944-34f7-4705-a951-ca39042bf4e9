"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Sync
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncMapPermissionPage = exports.SyncMapPermissionListInstance = exports.SyncMapPermissionInstance = exports.SyncMapPermissionContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class SyncMapPermissionContextImpl {
    constructor(_version, serviceSid, mapSid, identity) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(serviceSid)) {
            throw new Error("Parameter 'serviceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(mapSid)) {
            throw new Error("Parameter 'mapSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(identity)) {
            throw new Error("Parameter 'identity' is not valid.");
        }
        this._solution = { serviceSid, mapSid, identity };
        this._uri = `/Services/${serviceSid}/Maps/${mapSid}/Permissions/${identity}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new SyncMapPermissionInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.mapSid, instance._solution.identity));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["read"] === null || params["read"] === undefined) {
            throw new Error("Required parameter \"params['read']\" missing.");
        }
        if (params["write"] === null || params["write"] === undefined) {
            throw new Error("Required parameter \"params['write']\" missing.");
        }
        if (params["manage"] === null || params["manage"] === undefined) {
            throw new Error("Required parameter \"params['manage']\" missing.");
        }
        let data = {};
        data["Read"] = serialize.bool(params["read"]);
        data["Write"] = serialize.bool(params["write"]);
        data["Manage"] = serialize.bool(params["manage"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SyncMapPermissionInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.mapSid, instance._solution.identity));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SyncMapPermissionContextImpl = SyncMapPermissionContextImpl;
class SyncMapPermissionInstance {
    constructor(_version, payload, serviceSid, mapSid, identity) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.serviceSid = payload.service_sid;
        this.mapSid = payload.map_sid;
        this.identity = payload.identity;
        this.read = payload.read;
        this.write = payload.write;
        this.manage = payload.manage;
        this.url = payload.url;
        this._solution = {
            serviceSid,
            mapSid,
            identity: identity || this.identity,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new SyncMapPermissionContextImpl(this._version, this._solution.serviceSid, this._solution.mapSid, this._solution.identity);
        return this._context;
    }
    /**
     * Remove a SyncMapPermissionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a SyncMapPermissionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed SyncMapPermissionInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            serviceSid: this.serviceSid,
            mapSid: this.mapSid,
            identity: this.identity,
            read: this.read,
            write: this.write,
            manage: this.manage,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SyncMapPermissionInstance = SyncMapPermissionInstance;
function SyncMapPermissionListInstance(version, serviceSid, mapSid) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(mapSid)) {
        throw new Error("Parameter 'mapSid' is not valid.");
    }
    const instance = ((identity) => instance.get(identity));
    instance.get = function get(identity) {
        return new SyncMapPermissionContextImpl(version, serviceSid, mapSid, identity);
    };
    instance._version = version;
    instance._solution = { serviceSid, mapSid };
    instance._uri = `/Services/${serviceSid}/Maps/${mapSid}/Permissions`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SyncMapPermissionPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new SyncMapPermissionPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.SyncMapPermissionListInstance = SyncMapPermissionListInstance;
class SyncMapPermissionPage extends Page_1.default {
    /**
     * Initialize the SyncMapPermissionPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of SyncMapPermissionInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new SyncMapPermissionInstance(this._version, payload, this._solution.serviceSid, this._solution.mapSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SyncMapPermissionPage = SyncMapPermissionPage;
