"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowPage = exports.WorkflowListInstance = exports.WorkflowInstance = exports.WorkflowContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const workflowCumulativeStatistics_1 = require("./workflow/workflowCumulativeStatistics");
const workflowRealTimeStatistics_1 = require("./workflow/workflowRealTimeStatistics");
const workflowStatistics_1 = require("./workflow/workflowStatistics");
class WorkflowContextImpl {
    constructor(_version, workspaceSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(workspaceSid)) {
            throw new Error("Parameter 'workspaceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { workspaceSid, sid };
        this._uri = `/Workspaces/${workspaceSid}/Workflows/${sid}`;
    }
    get cumulativeStatistics() {
        this._cumulativeStatistics =
            this._cumulativeStatistics ||
                (0, workflowCumulativeStatistics_1.WorkflowCumulativeStatisticsListInstance)(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._cumulativeStatistics;
    }
    get realTimeStatistics() {
        this._realTimeStatistics =
            this._realTimeStatistics ||
                (0, workflowRealTimeStatistics_1.WorkflowRealTimeStatisticsListInstance)(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._realTimeStatistics;
    }
    get statistics() {
        this._statistics =
            this._statistics ||
                (0, workflowStatistics_1.WorkflowStatisticsListInstance)(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._statistics;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new WorkflowInstance(operationVersion, payload, instance._solution.workspaceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["assignmentCallbackUrl"] !== undefined)
            data["AssignmentCallbackUrl"] = params["assignmentCallbackUrl"];
        if (params["fallbackAssignmentCallbackUrl"] !== undefined)
            data["FallbackAssignmentCallbackUrl"] =
                params["fallbackAssignmentCallbackUrl"];
        if (params["configuration"] !== undefined)
            data["Configuration"] = params["configuration"];
        if (params["taskReservationTimeout"] !== undefined)
            data["TaskReservationTimeout"] = params["taskReservationTimeout"];
        if (params["reEvaluateTasks"] !== undefined)
            data["ReEvaluateTasks"] = params["reEvaluateTasks"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkflowInstance(operationVersion, payload, instance._solution.workspaceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkflowContextImpl = WorkflowContextImpl;
class WorkflowInstance {
    constructor(_version, payload, workspaceSid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.assignmentCallbackUrl = payload.assignment_callback_url;
        this.configuration = payload.configuration;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.documentContentType = payload.document_content_type;
        this.fallbackAssignmentCallbackUrl =
            payload.fallback_assignment_callback_url;
        this.friendlyName = payload.friendly_name;
        this.sid = payload.sid;
        this.taskReservationTimeout = deserialize.integer(payload.task_reservation_timeout);
        this.workspaceSid = payload.workspace_sid;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { workspaceSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new WorkflowContextImpl(this._version, this._solution.workspaceSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a WorkflowInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a WorkflowInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed WorkflowInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the cumulativeStatistics.
     */
    cumulativeStatistics() {
        return this._proxy.cumulativeStatistics;
    }
    /**
     * Access the realTimeStatistics.
     */
    realTimeStatistics() {
        return this._proxy.realTimeStatistics;
    }
    /**
     * Access the statistics.
     */
    statistics() {
        return this._proxy.statistics;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            assignmentCallbackUrl: this.assignmentCallbackUrl,
            configuration: this.configuration,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            documentContentType: this.documentContentType,
            fallbackAssignmentCallbackUrl: this.fallbackAssignmentCallbackUrl,
            friendlyName: this.friendlyName,
            sid: this.sid,
            taskReservationTimeout: this.taskReservationTimeout,
            workspaceSid: this.workspaceSid,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkflowInstance = WorkflowInstance;
function WorkflowListInstance(version, workspaceSid) {
    if (!(0, utility_1.isValidPathParam)(workspaceSid)) {
        throw new Error("Parameter 'workspaceSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new WorkflowContextImpl(version, workspaceSid, sid);
    };
    instance._version = version;
    instance._solution = { workspaceSid };
    instance._uri = `/Workspaces/${workspaceSid}/Workflows`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["friendlyName"] === null ||
            params["friendlyName"] === undefined) {
            throw new Error("Required parameter \"params['friendlyName']\" missing.");
        }
        if (params["configuration"] === null ||
            params["configuration"] === undefined) {
            throw new Error("Required parameter \"params['configuration']\" missing.");
        }
        let data = {};
        data["FriendlyName"] = params["friendlyName"];
        data["Configuration"] = params["configuration"];
        if (params["assignmentCallbackUrl"] !== undefined)
            data["AssignmentCallbackUrl"] = params["assignmentCallbackUrl"];
        if (params["fallbackAssignmentCallbackUrl"] !== undefined)
            data["FallbackAssignmentCallbackUrl"] =
                params["fallbackAssignmentCallbackUrl"];
        if (params["taskReservationTimeout"] !== undefined)
            data["TaskReservationTimeout"] = params["taskReservationTimeout"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkflowInstance(operationVersion, payload, instance._solution.workspaceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkflowPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new WorkflowPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.WorkflowListInstance = WorkflowListInstance;
class WorkflowPage extends Page_1.default {
    /**
     * Initialize the WorkflowPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of WorkflowInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new WorkflowInstance(this._version, payload, this._solution.workspaceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkflowPage = WorkflowPage;
