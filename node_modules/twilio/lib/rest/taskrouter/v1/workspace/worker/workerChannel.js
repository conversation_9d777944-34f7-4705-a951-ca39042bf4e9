"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkerChannelPage = exports.WorkerChannelListInstance = exports.WorkerChannelInstance = exports.WorkerChannelContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class WorkerChannelContextImpl {
    constructor(_version, workspaceSid, workerSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(workspaceSid)) {
            throw new Error("Parameter 'workspaceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(workerSid)) {
            throw new Error("Parameter 'workerSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { workspaceSid, workerSid, sid };
        this._uri = `/Workspaces/${workspaceSid}/Workers/${workerSid}/Channels/${sid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new WorkerChannelInstance(operationVersion, payload, instance._solution.workspaceSid, instance._solution.workerSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["capacity"] !== undefined)
            data["Capacity"] = params["capacity"];
        if (params["available"] !== undefined)
            data["Available"] = serialize.bool(params["available"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkerChannelInstance(operationVersion, payload, instance._solution.workspaceSid, instance._solution.workerSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkerChannelContextImpl = WorkerChannelContextImpl;
class WorkerChannelInstance {
    constructor(_version, payload, workspaceSid, workerSid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.assignedTasks = deserialize.integer(payload.assigned_tasks);
        this.available = payload.available;
        this.availableCapacityPercentage = deserialize.integer(payload.available_capacity_percentage);
        this.configuredCapacity = deserialize.integer(payload.configured_capacity);
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.sid = payload.sid;
        this.taskChannelSid = payload.task_channel_sid;
        this.taskChannelUniqueName = payload.task_channel_unique_name;
        this.workerSid = payload.worker_sid;
        this.workspaceSid = payload.workspace_sid;
        this.url = payload.url;
        this._solution = { workspaceSid, workerSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new WorkerChannelContextImpl(this._version, this._solution.workspaceSid, this._solution.workerSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a WorkerChannelInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed WorkerChannelInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            assignedTasks: this.assignedTasks,
            available: this.available,
            availableCapacityPercentage: this.availableCapacityPercentage,
            configuredCapacity: this.configuredCapacity,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            sid: this.sid,
            taskChannelSid: this.taskChannelSid,
            taskChannelUniqueName: this.taskChannelUniqueName,
            workerSid: this.workerSid,
            workspaceSid: this.workspaceSid,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkerChannelInstance = WorkerChannelInstance;
function WorkerChannelListInstance(version, workspaceSid, workerSid) {
    if (!(0, utility_1.isValidPathParam)(workspaceSid)) {
        throw new Error("Parameter 'workspaceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(workerSid)) {
        throw new Error("Parameter 'workerSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new WorkerChannelContextImpl(version, workspaceSid, workerSid, sid);
    };
    instance._version = version;
    instance._solution = { workspaceSid, workerSid };
    instance._uri = `/Workspaces/${workspaceSid}/Workers/${workerSid}/Channels`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkerChannelPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new WorkerChannelPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.WorkerChannelListInstance = WorkerChannelListInstance;
class WorkerChannelPage extends Page_1.default {
    /**
     * Initialize the WorkerChannelPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of WorkerChannelInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new WorkerChannelInstance(this._version, payload, this._solution.workspaceSid, this._solution.workerSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkerChannelPage = WorkerChannelPage;
