"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationCheckInstance = exports.VerificationCheckListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
function VerificationCheckListInstance(version, serviceSid) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { serviceSid };
    instance._uri = `/Services/${serviceSid}/VerificationCheck`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["code"] !== undefined)
            data["Code"] = params["code"];
        if (params["to"] !== undefined)
            data["To"] = params["to"];
        if (params["verificationSid"] !== undefined)
            data["VerificationSid"] = params["verificationSid"];
        if (params["amount"] !== undefined)
            data["Amount"] = params["amount"];
        if (params["payee"] !== undefined)
            data["Payee"] = params["payee"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationCheckInstance(operationVersion, payload, instance._solution.serviceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.VerificationCheckListInstance = VerificationCheckListInstance;
class VerificationCheckInstance {
    constructor(_version, payload, serviceSid) {
        this._version = _version;
        this.sid = payload.sid;
        this.serviceSid = payload.service_sid;
        this.accountSid = payload.account_sid;
        this.to = payload.to;
        this.channel = payload.channel;
        this.status = payload.status;
        this.valid = payload.valid;
        this.amount = payload.amount;
        this.payee = payload.payee;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.snaAttemptsErrorCodes = payload.sna_attempts_error_codes;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            serviceSid: this.serviceSid,
            accountSid: this.accountSid,
            to: this.to,
            channel: this.channel,
            status: this.status,
            valid: this.valid,
            amount: this.amount,
            payee: this.payee,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            snaAttemptsErrorCodes: this.snaAttemptsErrorCodes,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationCheckInstance = VerificationCheckInstance;
