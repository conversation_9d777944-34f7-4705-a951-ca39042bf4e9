/// <reference types="node" />
import { inspect, InspectOptions } from "util";
import V2 from "../../V2";
export type AccessTokenFactorTypes = "push";
/**
 * Options to pass to create a AccessTokenInstance
 */
export interface AccessTokenListInstanceCreateOptions {
    /** The unique external identifier for the Entity of the Service. This identifier should be immutable, not PII, and generated by your external system, such as your user\\\'s UUID, GUID, or SID. */
    identity: string;
    /**  */
    factorType: AccessTokenFactorTypes;
    /** The friendly name of the factor that is going to be created with this access token */
    factorFriendlyName?: string;
    /** How long, in seconds, the access token is valid. Can be an integer between 60 and 300. Default is 60. */
    ttl?: number;
}
export interface AccessTokenContext {
    /**
     * Fetch a AccessTokenInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AccessTokenInstance
     */
    fetch(callback?: (error: Error | null, item?: AccessTokenInstance) => any): Promise<AccessTokenInstance>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export interface AccessTokenContextSolution {
    serviceSid: string;
    sid: string;
}
export declare class AccessTokenContextImpl implements AccessTokenContext {
    protected _version: V2;
    protected _solution: AccessTokenContextSolution;
    protected _uri: string;
    constructor(_version: V2, serviceSid: string, sid: string);
    fetch(callback?: (error: Error | null, item?: AccessTokenInstance) => any): Promise<AccessTokenInstance>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): AccessTokenContextSolution;
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
interface AccessTokenResource {
    sid: string;
    account_sid: string;
    service_sid: string;
    entity_identity: string;
    factor_type: AccessTokenFactorTypes;
    factor_friendly_name: string;
    token: string;
    url: string;
    ttl: number;
    date_created: Date;
}
export declare class AccessTokenInstance {
    protected _version: V2;
    protected _solution: AccessTokenContextSolution;
    protected _context?: AccessTokenContext;
    constructor(_version: V2, payload: AccessTokenResource, serviceSid: string, sid?: string);
    /**
     * A 34 character string that uniquely identifies this Access Token.
     */
    sid: string;
    /**
     * The unique SID identifier of the Account.
     */
    accountSid: string;
    /**
     * The unique SID identifier of the Verify Service.
     */
    serviceSid: string;
    /**
     * The unique external identifier for the Entity of the Service.
     */
    entityIdentity: string;
    factorType: AccessTokenFactorTypes;
    /**
     * A human readable description of this factor, up to 64 characters. For a push factor, this can be the device\'s name.
     */
    factorFriendlyName: string;
    /**
     * The access token generated for enrollment, this is an encrypted json web token.
     */
    token: string;
    /**
     * The URL of this resource.
     */
    url: string;
    /**
     * How long, in seconds, the access token is valid. Max: 5 minutes
     */
    ttl: number;
    /**
     * The date that this access token was created, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     */
    dateCreated: Date;
    private get _proxy();
    /**
     * Fetch a AccessTokenInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AccessTokenInstance
     */
    fetch(callback?: (error: Error | null, item?: AccessTokenInstance) => any): Promise<AccessTokenInstance>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): {
        sid: string;
        accountSid: string;
        serviceSid: string;
        entityIdentity: string;
        factorType: "push";
        factorFriendlyName: string;
        token: string;
        url: string;
        ttl: number;
        dateCreated: Date;
    };
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
export interface AccessTokenSolution {
    serviceSid: string;
}
export interface AccessTokenListInstance {
    _version: V2;
    _solution: AccessTokenSolution;
    _uri: string;
    (sid: string): AccessTokenContext;
    get(sid: string): AccessTokenContext;
    /**
     * Create a AccessTokenInstance
     *
     * @param params - Parameter for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AccessTokenInstance
     */
    create(params: AccessTokenListInstanceCreateOptions, callback?: (error: Error | null, item?: AccessTokenInstance) => any): Promise<AccessTokenInstance>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export declare function AccessTokenListInstance(version: V2, serviceSid: string): AccessTokenListInstance;
export {};
