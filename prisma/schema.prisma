// BahinLink Prisma Schema
// ⚠️ CRITICAL: This schema is designed for REAL PRODUCTION DATA ONLY
// NO MOCK DATA - All models and relationships must handle actual business data

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Users table - integrates with Clerk for authentication
model User {
  id        String   @id @default(cuid())
  clerkId   String   @unique // Clerk user ID
  email     String   @unique
  role      UserRole
  firstName String
  lastName  String
  phone     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  agent              Agent?
  client             Client?
  supervisedShifts   Shift[]              @relation("SupervisorShifts")
  sentNotifications  Notification[]       @relation("SentNotifications")
  notifications      Notification[]       @relation("ReceivedNotifications")
  sentMessages       Communication[]      @relation("SentMessages")
  receivedMessages   Communication[]      @relation("ReceivedMessages")
  auditLogs          AuditLog[]
  assignedRequests   ClientRequest[]      @relation("AssignedRequests")
  verifiedTimeEntries TimeEntry[]        @relation("VerifiedBy")

  @@map("users")
}

enum UserRole {
  ADMIN
  SUPERVISOR
  AGENT
  CLIENT
}

// Agents table (security personnel details)
model Agent {
  id                     String    @id @default(cuid())
  userId                 String    @unique
  employeeId             String    @unique
  photoUrl               String?
  certifications         Json      @default("[]")
  skills                 Json      @default("[]")
  availability           Json      @default("{}")
  performanceStats       Json      @default("{}")
  emergencyContactName   String?
  emergencyContactPhone  String?
  hireDate               DateTime?
  hourlyRate             Decimal?  @db.Decimal(10, 2)
  isAvailable            Boolean   @default(true)
  currentLatitude        Float?
  currentLongitude       Float?
  lastLocationUpdate     DateTime?
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt

  // Relationships
  user                User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  shifts              Shift[]
  timeEntries         TimeEntry[]
  reports             Report[]
  geofenceViolations  GeofenceViolation[]

  @@map("agents")
}

// Clients table (companies using security services)
model Client {
  id                String    @id @default(cuid())
  userId            String    @unique
  companyName       String
  contactPerson     String?
  billingAddress    String?
  serviceLevel      String    @default("standard")
  contractStartDate DateTime?
  contractEndDate   DateTime?
  billingCycle      String    @default("monthly")
  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relationships
  user     User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  sites    Site[]
  requests ClientRequest[]

  @@map("clients")
}

// Sites table (client locations requiring security)
model Site {
  id                  String  @id @default(cuid())
  clientId            String
  name                String
  address             String
  latitude            Float
  longitude           Float
  geofenceRadius      Int     @default(100) // meters
  qrCode              String? @unique
  siteType            String?
  specialInstructions String?
  accessCodes         Json    @default("{}")
  emergencyContacts   Json    @default("[]")
  isActive            Boolean @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relationships
  client             Client              @relation(fields: [clientId], references: [id], onDelete: Cascade)
  shifts             Shift[]
  reports            Report[]
  clientRequests     ClientRequest[]
  geofenceViolations GeofenceViolation[]

  @@map("sites")
}

// Shifts table (scheduled work periods)
model Shift {
  id              String      @id @default(cuid())
  siteId          String
  agentId         String?
  supervisorId    String?
  shiftDate       DateTime    @db.Date
  startTime       DateTime    @db.Time
  endTime         DateTime    @db.Time
  status          ShiftStatus @default(SCHEDULED)
  actualStartTime DateTime?
  actualEndTime   DateTime?
  breakDuration   Int         @default(0) // minutes
  overtimeHours   Decimal     @default(0) @db.Decimal(5, 2)
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relationships
  site       Site        @relation(fields: [siteId], references: [id], onDelete: Cascade)
  agent      Agent?      @relation(fields: [agentId], references: [id], onDelete: SetNull)
  supervisor User?       @relation("SupervisorShifts", fields: [supervisorId], references: [id], onDelete: SetNull)
  timeEntries TimeEntry[]
  reports    Report[]

  @@map("shifts")
}

enum ShiftStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

// Time tracking table (clock in/out records)
model TimeEntry {
  id                String            @id @default(cuid())
  shiftId           String
  agentId           String
  clockInTime       DateTime?
  clockOutTime      DateTime?
  clockInLatitude   Float?
  clockInLongitude  Float?
  clockOutLatitude  Float?
  clockOutLongitude Float?
  clockInMethod     ClockMethod?
  clockOutMethod    ClockMethod?
  clockInAccuracy   Float? // GPS accuracy in meters
  clockOutAccuracy  Float?
  totalHours        Decimal?          @db.Decimal(5, 2)
  isVerified        Boolean           @default(false)
  verifiedBy        String?
  verifiedAt        DateTime?
  discrepancyNotes  String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relationships
  shift     Shift  @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  agent     Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  verifier  User?  @relation("VerifiedBy", fields: [verifiedBy], references: [id])

  @@map("time_entries")
}

enum ClockMethod {
  GPS
  QR_CODE
  MANUAL
  NFC
}

// Reports table (patrol and incident reports)
model Report {
  id                String       @id @default(cuid())
  type              ReportType
  shiftId           String?
  siteId            String
  agentId           String
  supervisorId      String?
  title             String
  description       String?
  observations      String?
  incidents         String?
  actionsTaken      String?
  recommendations   String?
  status            ReportStatus @default(DRAFT)
  priority          Priority     @default(NORMAL)
  clientSignature   Json?
  clientFeedback    String?
  photos            Json         @default("[]") // array of photo URLs
  videos            Json         @default("[]") // array of video URLs
  attachments       Json         @default("[]")
  latitude          Float?
  longitude         Float?
  weatherConditions String?
  temperature       Float?
  submittedAt       DateTime?
  approvedAt        DateTime?
  rejectedAt        DateTime?
  rejectionReason   String?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  // Relationships
  shift      Shift? @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  site       Site   @relation(fields: [siteId], references: [id], onDelete: Cascade)
  agent      Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  supervisor User?  @relation("SupervisorShifts", fields: [supervisorId], references: [id], onDelete: SetNull)

  @@map("reports")
}

enum ReportType {
  PATROL
  INCIDENT
  INSPECTION
  MAINTENANCE
}

enum ReportStatus {
  DRAFT
  SUBMITTED
  UNDER_REVIEW
  APPROVED
  REJECTED
  ARCHIVED
}

enum Priority {
  LOW
  NORMAL
  HIGH
  CRITICAL
  EMERGENCY
}

// Notifications table (system notifications)
model Notification {
  id             String            @id @default(cuid())
  recipientId    String
  senderId       String?
  type           String
  title          String
  message        String
  data           Json              @default("{}")
  isRead         Boolean           @default(false)
  priority       Priority          @default(NORMAL)
  deliveryMethod DeliveryMethod    @default(APP)
  scheduledFor   DateTime?
  deliveredAt    DateTime?
  createdAt      DateTime          @default(now())
  readAt         DateTime?

  // Relationships
  recipient User  @relation("ReceivedNotifications", fields: [recipientId], references: [id], onDelete: Cascade)
  sender    User? @relation("SentNotifications", fields: [senderId], references: [id], onDelete: SetNull)

  @@map("notifications")
}

enum DeliveryMethod {
  APP
  EMAIL
  SMS
  PUSH
}

// Communication logs table (internal messaging)
model Communication {
  id          String          @id @default(cuid())
  senderId    String
  recipientId String
  threadId    String?
  messageType MessageType     @default(TEXT)
  content     String
  attachments Json            @default("[]")
  isRead      Boolean         @default(false)
  isUrgent    Boolean         @default(false)
  replyToId   String?
  createdAt   DateTime        @default(now())
  readAt      DateTime?
  editedAt    DateTime?

  // Relationships
  sender    User            @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  recipient User            @relation("ReceivedMessages", fields: [recipientId], references: [id], onDelete: Cascade)
  replyTo   Communication? @relation("MessageReplies", fields: [replyToId], references: [id])
  replies   Communication[] @relation("MessageReplies")

  @@map("communications")
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  VOICE
  VIDEO
}

// Audit logs table (system activity tracking)
model AuditLog {
  id             String   @id @default(cuid())
  userId         String?
  action         String
  tableName      String?
  recordId       String?
  oldValues      Json?
  newValues      Json?
  ipAddress      String?
  userAgent      String?
  sessionId      String?
  apiEndpoint    String?
  httpMethod     String?
  responseStatus Int?
  createdAt      DateTime @default(now())

  // Relationships
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}

// Client requests table (service requests from clients)
model ClientRequest {
  id                        String              @id @default(cuid())
  clientId                  String
  siteId                    String
  requestType               String
  priority                  Priority            @default(NORMAL)
  title                     String
  description               String
  status                    ClientRequestStatus @default(OPEN)
  assignedToId              String?
  estimatedCompletion       DateTime?
  actualCompletion          DateTime?
  clientSatisfactionRating  Int?                @db.SmallInt
  clientFeedback            String?
  internalNotes             String?
  costEstimate              Decimal?            @db.Decimal(10, 2)
  actualCost                Decimal?            @db.Decimal(10, 2)
  createdAt                 DateTime            @default(now())
  updatedAt                 DateTime            @updatedAt
  resolvedAt                DateTime?

  // Relationships
  client     Client @relation(fields: [clientId], references: [id], onDelete: Cascade)
  site       Site   @relation(fields: [siteId], references: [id], onDelete: Cascade)
  assignedTo User?  @relation("AssignedRequests", fields: [assignedToId], references: [id], onDelete: SetNull)

  @@map("client_requests")
}

enum ClientRequestStatus {
  OPEN
  ACKNOWLEDGED
  IN_PROGRESS
  RESOLVED
  CLOSED
  CANCELLED
}

// Geofence violations table (tracking location violations)
model GeofenceViolation {
  id               String    @id @default(cuid())
  agentId          String
  siteId           String
  shiftId          String?
  violationType    String
  agentLatitude    Float
  agentLongitude   Float
  distanceFromSite Float
  durationMinutes  Int?
  isResolved       Boolean   @default(false)
  resolutionNotes  String?
  createdAt        DateTime  @default(now())
  resolvedAt       DateTime?

  // Relationships
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  site  Site  @relation(fields: [siteId], references: [id], onDelete: Cascade)

  @@map("geofence_violations")
}
