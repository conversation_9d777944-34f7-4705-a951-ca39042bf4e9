#!/bin/bash

# BahinLink Production Deployment Script
# ⚠️ CRITICAL: Deploys to REAL PRODUCTION ENVIRONMENT with live data

set -e

echo "🚀 Starting BahinLink Production Deployment..."
echo "⚠️  WARNING: This will deploy to REAL PRODUCTION ENVIRONMENT"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if required environment variables are set
check_env_vars() {
    echo -e "${BLUE}🔍 Checking environment variables...${NC}"
    
    required_vars=(
        "DATABASE_URL"
        "CLERK_PUBLISHABLE_KEY"
        "CLERK_SECRET_KEY"
        "UPSTASH_REDIS_REST_URL"
        "UPSTASH_REDIS_REST_TOKEN"
        "BLOB_READ_WRITE_TOKEN"
        "GOOGLE_MAPS_API_KEY"
        "FIREBASE_SERVER_KEY"
        "SENDGRID_API_KEY"
        "TWILIO_ACCOUNT_SID"
        "TWILIO_AUTH_TOKEN"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        echo -e "${RED}❌ Missing required environment variables:${NC}"
        printf '%s\n' "${missing_vars[@]}"
        echo ""
        echo "Please set these variables before deploying:"
        echo "export DATABASE_URL=\"postgresql://...\""
        echo "export CLERK_PUBLISHABLE_KEY=\"pk_live_...\""
        echo "# ... etc"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All required environment variables are set${NC}"
}

# Install dependencies
install_dependencies() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    # Root dependencies
    npm install
    
    # API dependencies
    cd packages/api
    npm install
    cd ../..
    
    # Web Admin dependencies
    cd packages/web-admin
    npm install
    cd ../..
    
    # Client Portal dependencies
    cd packages/client-portal
    npm install
    cd ../..
    
    # Shared package
    cd packages/shared
    npm install
    npm run build
    cd ../..
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Setup database
setup_database() {
    echo -e "${BLUE}🗄️  Setting up production database...${NC}"
    
    cd packages/api
    
    # Generate Prisma client
    npx prisma generate
    
    # Run database migrations
    echo "Running database migrations..."
    npx prisma migrate deploy
    
    # Seed production data
    echo "Seeding production data..."
    npx prisma db seed
    
    cd ../..
    
    echo -e "${GREEN}✅ Database setup complete${NC}"
}

# Build applications
build_applications() {
    echo -e "${BLUE}🔨 Building applications...${NC}"
    
    # Build Web Admin
    echo "Building Web Admin..."
    cd packages/web-admin
    npm run build
    cd ../..
    
    # Build Client Portal
    echo "Building Client Portal..."
    cd packages/client-portal
    npm run build
    cd ../..
    
    echo -e "${GREEN}✅ Applications built${NC}"
}

# Deploy to Vercel
deploy_to_vercel() {
    echo -e "${BLUE}🌐 Deploying to Vercel...${NC}"
    
    # Check if Vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        echo "Installing Vercel CLI..."
        npm install -g vercel
    fi
    
    # Deploy API
    echo "Deploying API..."
    cd packages/api
    
    # Set environment variables for API
    vercel env add DATABASE_URL production <<< "$DATABASE_URL"
    vercel env add CLERK_SECRET_KEY production <<< "$CLERK_SECRET_KEY"
    vercel env add UPSTASH_REDIS_REST_URL production <<< "$UPSTASH_REDIS_REST_URL"
    vercel env add UPSTASH_REDIS_REST_TOKEN production <<< "$UPSTASH_REDIS_REST_TOKEN"
    vercel env add BLOB_READ_WRITE_TOKEN production <<< "$BLOB_READ_WRITE_TOKEN"
    vercel env add GOOGLE_MAPS_API_KEY production <<< "$GOOGLE_MAPS_API_KEY"
    vercel env add FIREBASE_SERVER_KEY production <<< "$FIREBASE_SERVER_KEY"
    vercel env add SENDGRID_API_KEY production <<< "$SENDGRID_API_KEY"
    vercel env add TWILIO_ACCOUNT_SID production <<< "$TWILIO_ACCOUNT_SID"
    vercel env add TWILIO_AUTH_TOKEN production <<< "$TWILIO_AUTH_TOKEN"
    
    # Deploy API to production
    vercel --prod --yes
    
    # Get API URL
    API_URL=$(vercel --prod --yes 2>&1 | grep -o 'https://[^[:space:]]*')
    echo "API deployed to: $API_URL"
    
    cd ../..
    
    # Deploy Web Admin
    echo "Deploying Web Admin..."
    cd packages/web-admin
    
    # Set environment variables for Web Admin
    vercel env add NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY production <<< "$CLERK_PUBLISHABLE_KEY"
    vercel env add NEXT_PUBLIC_API_BASE_URL production <<< "$API_URL"
    
    # Deploy to production
    vercel --prod --yes
    
    # Get Web Admin URL
    ADMIN_URL=$(vercel --prod --yes 2>&1 | grep -o 'https://[^[:space:]]*')
    echo "Web Admin deployed to: $ADMIN_URL"
    
    cd ../..
    
    # Deploy Client Portal
    echo "Deploying Client Portal..."
    cd packages/client-portal
    
    # Set environment variables for Client Portal
    vercel env add NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY production <<< "$CLERK_PUBLISHABLE_KEY"
    vercel env add NEXT_PUBLIC_API_BASE_URL production <<< "$API_URL"
    
    # Deploy to production
    vercel --prod --yes
    
    # Get Client Portal URL
    CLIENT_URL=$(vercel --prod --yes 2>&1 | grep -o 'https://[^[:space:]]*')
    echo "Client Portal deployed to: $CLIENT_URL"
    
    cd ../..
    
    echo -e "${GREEN}✅ All applications deployed to Vercel${NC}"
    
    # Store URLs for final summary
    echo "$API_URL" > .api_url
    echo "$ADMIN_URL" > .admin_url
    echo "$CLIENT_URL" > .client_url
}

# Configure custom domains (optional)
configure_domains() {
    echo -e "${BLUE}🌍 Configuring custom domains...${NC}"
    
    read -p "Do you want to configure custom domains? (y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Configuring custom domains..."
        
        # API domain
        cd packages/api
        read -p "Enter API domain (e.g., api.bahinlink.com): " api_domain
        if [ ! -z "$api_domain" ]; then
            vercel domains add "$api_domain"
        fi
        cd ../..
        
        # Web Admin domain
        cd packages/web-admin
        read -p "Enter Admin domain (e.g., admin.bahinlink.com): " admin_domain
        if [ ! -z "$admin_domain" ]; then
            vercel domains add "$admin_domain"
        fi
        cd ../..
        
        # Client Portal domain
        cd packages/client-portal
        read -p "Enter Client domain (e.g., client.bahinlink.com): " client_domain
        if [ ! -z "$client_domain" ]; then
            vercel domains add "$client_domain"
        fi
        cd ../..
        
        echo -e "${GREEN}✅ Custom domains configured${NC}"
    else
        echo "Skipping custom domain configuration"
    fi
}

# Run health checks
run_health_checks() {
    echo -e "${BLUE}🏥 Running health checks...${NC}"
    
    # Read deployed URLs
    API_URL=$(cat .api_url 2>/dev/null || echo "")
    ADMIN_URL=$(cat .admin_url 2>/dev/null || echo "")
    CLIENT_URL=$(cat .client_url 2>/dev/null || echo "")
    
    # Check API health
    if [ ! -z "$API_URL" ]; then
        echo "Checking API health..."
        if curl -f -s "$API_URL/health" > /dev/null; then
            echo -e "${GREEN}✅ API is healthy${NC}"
        else
            echo -e "${YELLOW}⚠️  API health check failed${NC}"
        fi
    fi
    
    # Check Web Admin
    if [ ! -z "$ADMIN_URL" ]; then
        echo "Checking Web Admin..."
        if curl -f -s "$ADMIN_URL" > /dev/null; then
            echo -e "${GREEN}✅ Web Admin is accessible${NC}"
        else
            echo -e "${YELLOW}⚠️  Web Admin check failed${NC}"
        fi
    fi
    
    # Check Client Portal
    if [ ! -z "$CLIENT_URL" ]; then
        echo "Checking Client Portal..."
        if curl -f -s "$CLIENT_URL" > /dev/null; then
            echo -e "${GREEN}✅ Client Portal is accessible${NC}"
        else
            echo -e "${YELLOW}⚠️  Client Portal check failed${NC}"
        fi
    fi
}

# Display final summary
display_summary() {
    echo ""
    echo -e "${GREEN}🎉 BahinLink Production Deployment Complete!${NC}"
    echo ""
    echo -e "${BLUE}📋 Deployment Summary:${NC}"
    echo "=================================="
    
    # Read deployed URLs
    API_URL=$(cat .api_url 2>/dev/null || echo "Not deployed")
    ADMIN_URL=$(cat .admin_url 2>/dev/null || echo "Not deployed")
    CLIENT_URL=$(cat .client_url 2>/dev/null || echo "Not deployed")
    
    echo "🔗 API URL:           $API_URL"
    echo "🔗 Web Admin URL:     $ADMIN_URL"
    echo "🔗 Client Portal URL: $CLIENT_URL"
    echo ""
    
    echo -e "${BLUE}👥 Default Users:${NC}"
    echo "=================================="
    echo "Admin:      <EMAIL>"
    echo "Supervisor: <EMAIL>"
    echo "Agent 1:    <EMAIL>"
    echo "Agent 2:    <EMAIL>"
    echo "Client:     <EMAIL>"
    echo ""
    
    echo -e "${BLUE}🏢 Real Business Data:${NC}"
    echo "=================================="
    echo "Company:    Bahin SARL (Security Services)"
    echo "Location:   Dakar, Senegal"
    echo "Client:     Sonatel Senegal"
    echo "Sites:      Sonatel HQ, Sonatel Data Center"
    echo "Agents:     Amadou Ba (BSL001), Fatou Sow (BSL002)"
    echo ""
    
    echo -e "${BLUE}🔧 Production Services:${NC}"
    echo "=================================="
    echo "Database:   Neon PostgreSQL (Real)"
    echo "Auth:       Clerk (Real)"
    echo "Storage:    Vercel Blob (Real)"
    echo "Cache:      Upstash Redis (Real)"
    echo "Maps:       Google Maps API (Real)"
    echo "Push:       Firebase (Real)"
    echo "Email:      SendGrid (Real)"
    echo "SMS:        Twilio (Real)"
    echo ""
    
    echo -e "${YELLOW}⚠️  Important Notes:${NC}"
    echo "=================================="
    echo "• All services use REAL production data"
    echo "• GPS tracking is live and functional"
    echo "• Authentication is handled by Clerk"
    echo "• File uploads go to Vercel Blob storage"
    echo "• Real-time updates via Socket.io"
    echo "• Mobile app needs to be built separately"
    echo ""
    
    echo -e "${GREEN}✅ Ready for production use!${NC}"
    
    # Clean up temporary files
    rm -f .api_url .admin_url .client_url
}

# Main deployment flow
main() {
    echo -e "${BLUE}BahinLink Production Deployment${NC}"
    echo "=================================="
    echo ""
    
    check_env_vars
    install_dependencies
    setup_database
    build_applications
    deploy_to_vercel
    configure_domains
    run_health_checks
    display_summary
}

# Run main function
main "$@"
