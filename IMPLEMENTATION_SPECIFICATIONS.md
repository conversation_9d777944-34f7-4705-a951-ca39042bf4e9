# BahinLink Implementation Specifications

**⚠️ CRITICAL: ALL IMPLEMENTATIONS MUST USE REAL PRODUCTION DATA ONLY**

This document provides detailed specifications for implementing missing components identified in the technical audit.

## 🔧 Task 1: API Backend - Authentication & User Management

### Priority: CRITICAL
### Estimated Time: 8 hours

#### Files to Create:
```
packages/api/api/auth/
├── login.js
├── logout.js
├── refresh.js
├── forgot-password.js
└── reset-password.js

packages/api/api/users/
├── index.js
├── [id].js
└── profile.js
```

#### Implementation Requirements:

**1. Authentication Endpoints**
- `POST /api/auth/login` - Clerk session validation
- `POST /api/auth/logout` - Session cleanup
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/forgot-password` - Password reset initiation
- `POST /api/auth/reset-password` - Password reset completion

**2. User Management Endpoints**
- `GET /api/users` - List users with role filtering
- `POST /api/users` - Create new user (admin only)
- `GET /api/users/:id` - Get user details
- `PUT /api/users/:id` - Update user profile
- `DELETE /api/users/:id` - Deactivate user (soft delete)

**3. Data Schema Requirements**
```typescript
interface UserResponse {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'SUPERVISOR' | 'AGENT' | 'CLIENT';
  phone?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

**4. Security Requirements**
- Clerk JWT validation on all endpoints
- Role-based access control (RBAC)
- Input validation and sanitization
- Rate limiting (100 requests/minute per user)
- Audit logging for all user operations

## 🔧 Task 2: API Backend - Agent Management System

### Priority: CRITICAL
### Estimated Time: 10 hours

#### Files to Create:
```
packages/api/api/agents/
├── index.js
├── [id].js
├── performance.js
├── nearby.js
└── schedule.js
```

#### Implementation Requirements:

**1. Agent CRUD Operations**
- `GET /api/agents` - List agents with filtering/pagination
- `POST /api/agents` - Create new agent profile
- `GET /api/agents/:id` - Get agent details
- `PUT /api/agents/:id` - Update agent profile
- `GET /api/agents/:id/performance` - Get performance metrics

**2. Data Table Specifications**
```typescript
interface AgentTableData {
  id: string;
  employeeId: string;
  name: string;
  phone: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ON_SHIFT' | 'BREAK';
  currentLocation?: {
    latitude: number;
    longitude: number;
    lastUpdate: string;
  };
  currentShift?: {
    siteId: string;
    siteName: string;
    startTime: string;
    endTime: string;
  };
  performanceScore: number;
  totalShifts: number;
  onTimePercentage: number;
}
```

**3. Performance Metrics**
- Total shifts completed
- On-time percentage
- Client satisfaction score
- Report quality score
- Geofence compliance rate

**4. Location Tracking**
- Real-time GPS coordinates
- Location history (last 24 hours)
- Geofence violation tracking
- Distance calculations from assigned sites

## 🔧 Task 3: API Backend - Site & Shift Management

### Priority: HIGH
### Estimated Time: 12 hours

#### Files to Create:
```
packages/api/api/sites/
├── index.js
├── [id].js
├── qr-generate.js
└── geofence.js

packages/api/api/shifts/
├── index.js
├── [id].js
├── agent/[agentId].js
├── site/[siteId].js
└── schedule.js
```

#### Implementation Requirements:

**1. Site Management**
- `GET /api/sites` - List sites with client filtering
- `POST /api/sites` - Create new site
- `GET /api/sites/:id` - Get site details
- `PUT /api/sites/:id` - Update site information
- `POST /api/sites/:id/generate-qr` - Generate QR code for site

**2. Shift Management**
- `GET /api/shifts` - List shifts with date/agent filtering
- `POST /api/shifts` - Create new shift
- `GET /api/shifts/:id` - Get shift details
- `PUT /api/shifts/:id` - Update shift (assign agent, change times)
- `DELETE /api/shifts/:id` - Cancel shift

**3. Site Data Schema**
```typescript
interface SiteData {
  id: string;
  clientId: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  geofenceRadius: number;
  qrCode: string;
  accessCodes: string[];
  emergencyContacts: Contact[];
  specialInstructions: string;
  isActive: boolean;
}
```

**4. Shift Scheduling Features**
- Recurring shift templates
- Agent availability checking
- Conflict detection
- Automatic notifications
- Shift swapping capabilities

## 🔧 Task 4: Mobile App - Core Screens Implementation

### Priority: HIGH
### Estimated Time: 16 hours

#### Files to Create:
```
packages/mobile/src/screens/
├── LoginScreen.js
├── DashboardScreen.js
├── ReportsScreen.js
├── LocationScreen.js
├── ProfileScreen.js
├── ShiftsScreen.js
├── CameraScreen.js
├── QRScannerScreen.js
├── NotificationsScreen.js
├── MessagesScreen.js
├── SettingsScreen.js
└── EmergencyScreen.js

packages/mobile/src/services/
├── NotificationService.js
├── OfflineService.js
├── CameraService.js
└── QRService.js
```

#### Implementation Requirements:

**1. Core Screen Features**
- **LoginScreen**: Clerk authentication integration
- **DashboardScreen**: Real-time status overview
- **TimeTrackingScreen**: ✅ Already implemented
- **ReportsScreen**: Create/edit patrol and incident reports
- **LocationScreen**: GPS tracking with map view
- **ShiftsScreen**: View assigned shifts and schedule

**2. Camera & QR Integration**
- **CameraScreen**: Photo/video capture for reports
- **QRScannerScreen**: Site check-in verification
- Real-time image compression
- Offline media storage with sync

**3. Offline Capabilities**
- Local SQLite database
- Sync queue for offline actions
- Background sync when connected
- Conflict resolution strategies

**4. Real-time Features**
- Push notifications
- Live location updates
- Emergency alert system
- In-app messaging

## 🔧 Task 5: Web Admin - Management Pages & Data Tables

### Priority: HIGH
### Estimated Time: 14 hours

#### Files to Create:
```
packages/web-admin/src/app/
├── agents/
│   ├── page.js
│   └── [id]/page.js
├── sites/
│   ├── page.js
│   └── [id]/page.js
├── shifts/
│   ├── page.js
│   └── [id]/page.js
├── reports/
│   ├── page.js
│   └── [id]/page.js
└── users/
    ├── page.js
    └── [id]/page.js

packages/web-admin/src/components/
├── AgentTable.js
├── SiteTable.js
├── ShiftTable.js
├── ReportTable.js
├── UserTable.js
└── AnalyticsCharts.js
```

#### Implementation Requirements:

**1. Data Table Features**
- Server-side pagination (50 items per page)
- Multi-column sorting
- Advanced filtering (date ranges, status, location)
- Bulk operations (approve, assign, delete)
- Export functionality (CSV, PDF)
- Real-time updates via Socket.io

**2. Agent Management Table**
```typescript
interface AgentTableColumns {
  employeeId: string;
  name: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ON_SHIFT';
  currentLocation: string;
  currentShift: string;
  performance: number;
  actions: JSX.Element;
}
```

**3. CRUD Operations**
- Create new records with form validation
- Edit existing records with optimistic updates
- Delete with confirmation dialogs
- Bulk operations with progress indicators

**4. Analytics Dashboard**
- Real-time metrics charts
- Performance trends
- Geographic heat maps
- Custom date range filtering

## 🔧 Task 6: Client Portal - Complete Feature Set

### Priority: MEDIUM
### Estimated Time: 10 hours

#### Files to Create:
```
packages/client-portal/src/app/
├── tracking/page.js
├── reports/
│   ├── page.js
│   └── [id]/page.js
├── requests/
│   ├── page.js
│   └── [id]/page.js
├── profile/page.js
└── settings/page.js

packages/client-portal/src/components/
├── ServiceRequestForm.js
├── ReportViewer.js
└── SLADashboard.js
```

#### Implementation Requirements:

**1. Live Tracking Page**
- Real-time agent locations on map
- Site status indicators
- Geofence visualization
- Historical tracking data

**2. Reports Management**
- View all security reports
- Filter by date, type, site
- Download PDF reports
- Client feedback system

**3. Service Requests**
- Submit new service requests
- Track request status
- Priority level assignment
- Communication thread

**4. SLA Dashboard**
- Service level metrics
- Performance indicators
- Compliance tracking
- Trend analysis

## 🔧 Task 7: Real-time Features & Notifications

### Priority: HIGH
### Estimated Time: 8 hours

#### Files to Create:
```
packages/api/lib/
├── socket.js
├── notifications.js
└── push-service.js

packages/mobile/src/services/
├── SocketService.js
└── PushNotificationService.js
```

#### Implementation Requirements:

**1. Socket.io Events**
- `location-update` - Real-time GPS tracking
- `shift-status-change` - Shift state updates
- `report-submitted` - New report notifications
- `emergency-alert` - Critical alerts
- `message-new` - In-app messaging

**2. Push Notifications**
- Firebase Cloud Messaging integration
- Device token management
- Notification scheduling
- Deep linking to app screens

**3. Real-time Data Sync**
- Optimistic updates
- Conflict resolution
- Connection state management
- Automatic reconnection

This specification provides the foundation for completing the BahinLink system according to the technical architecture requirements.
