{"name": "bahinlink", "version": "1.0.0", "description": "BahinLink - Mobile-first workforce management for Bahin SARL security company", "private": true, "workspaces": ["packages/api", "packages/mobile", "packages/web-admin", "packages/client-portal", "packages/shared"], "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:admin\" \"npm run dev:client\"", "dev:api": "cd packages/api && npm run dev", "dev:admin": "cd packages/web-admin && npm run dev", "dev:client": "cd packages/client-portal && npm run dev", "dev:mobile": "cd packages/mobile && npm run android", "build": "npm run build:api && npm run build:admin && npm run build:client", "build:api": "cd packages/api && npm run build", "build:admin": "cd packages/web-admin && npm run build", "build:client": "cd packages/client-portal && npm run build", "deploy": "npm run deploy:api && npm run deploy:admin && npm run deploy:client", "deploy:api": "cd packages/api && vercel --prod", "deploy:admin": "cd packages/web-admin && vercel --prod", "deploy:client": "cd packages/client-portal && vercel --prod", "db:migrate": "cd packages/api && npx prisma migrate deploy", "db:generate": "cd packages/api && npx prisma generate", "db:seed": "cd packages/api && npx prisma db seed", "db:studio": "cd packages/api && npx prisma studio"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}