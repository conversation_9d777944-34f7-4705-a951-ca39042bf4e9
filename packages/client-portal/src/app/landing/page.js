// BahinLink Client Portal Landing Page
// ⚠️ CRITICAL: Real client branding and production data ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Divider,
  IconButton
} from '@mui/material';
import {
  Security,
  LocationOn,
  Assignment,
  Schedule,
  CheckCircle,
  Warning,
  TrendingUp,
  Notifications,
  Settings,
  Dashboard,
  Map,
  Assessment,
  Support
} from '@mui/icons-material';

import ApiService from '../../services/ApiService';
import { formatTime, formatDate } from '@bahinlink/shared';

export default function ClientLanding() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [clientData, setClientData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadClientData();
    }
  }, [isLoaded, isSignedIn]);

  const loadClientData = async () => {
    try {
      setError(null);
      
      // Get client profile and dashboard data
      const [profileResponse, dashboardResponse] = await Promise.all([
        ApiService.get('/client/profile'),
        ApiService.get('/client/dashboard-summary')
      ]);
      
      setClientData({
        profile: profileResponse.data,
        dashboard: dashboardResponse.data
      });
    } catch (error) {
      console.error('Client data error:', error);
      setError(error.message || 'Failed to load client data');
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Security Dashboard',
      description: 'Real-time monitoring of all security services',
      icon: <Dashboard />,
      color: '#1976d2',
      path: '/dashboard'
    },
    {
      title: 'Live Tracking',
      description: 'View agent locations and site status',
      icon: <Map />,
      color: '#388e3c',
      path: '/tracking'
    },
    {
      title: 'Security Reports',
      description: 'Access patrol and incident reports',
      icon: <Assessment />,
      color: '#f57c00',
      path: '/reports'
    },
    {
      title: 'Service Requests',
      description: 'Submit new requests or track existing ones',
      icon: <Support />,
      color: '#7b1fa2',
      path: '/requests'
    }
  ];

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography variant="h6">Loading your security portal...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  const { profile = {}, dashboard = {} } = clientData || {};

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section with Client Branding */}
      <Paper sx={{ p: 3, mb: 3, background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)' }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Box display="flex" alignItems="center" mb={2}>
              <Avatar
                sx={{ 
                  width: 80, 
                  height: 80, 
                  mr: 3,
                  backgroundColor: 'white',
                  color: '#1976d2',
                  fontSize: '2rem'
                }}
              >
                {profile.companyName?.charAt(0) || 'C'}
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" color="white" fontWeight="bold">
                  Welcome to BahinLink
                </Typography>
                <Typography variant="h6" color="rgba(255,255,255,0.9)">
                  {profile.companyName || 'Client Portal'}
                </Typography>
                <Typography variant="body1" color="rgba(255,255,255,0.8)">
                  Secure. Reliable. Professional Security Services
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box textAlign="right">
              <Typography variant="body2" color="rgba(255,255,255,0.9)">
                Welcome back, {user?.firstName}
              </Typography>
              <Typography variant="caption" color="rgba(255,255,255,0.7)">
                Last login: {formatTime(new Date())}
              </Typography>
              <Box mt={1}>
                <IconButton color="inherit" sx={{ color: 'white' }}>
                  <Notifications />
                </IconButton>
                <IconButton color="inherit" sx={{ color: 'white' }}>
                  <Settings />
                </IconButton>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Service Overview Dashboard */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Protected Sites
                  </Typography>
                  <Typography variant="h4" component="div">
                    {dashboard.totalSites || 0}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    All Secured
                  </Typography>
                </Box>
                <Security color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Guards
                  </Typography>
                  <Typography variant="h4" component="div">
                    {dashboard.activeAgents || 0}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    On Duty Now
                  </Typography>
                </Box>
                <CheckCircle color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Service Level
                  </Typography>
                  <Typography variant="h4" component="div">
                    {dashboard.serviceLevel || '98'}%
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    Excellent
                  </Typography>
                </Box>
                <TrendingUp color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Response Time
                  </Typography>
                  <Typography variant="h4" component="div">
                    {dashboard.responseTime || '2.3'}m
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    Under Target
                  </Typography>
                </Box>
                <Schedule color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Access Navigation */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12}>
          <Typography variant="h5" gutterBottom fontWeight="bold">
            Quick Access
          </Typography>
        </Grid>
        {quickActions.map((action, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
              onClick={() => window.location.href = action.path}
            >
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar sx={{ backgroundColor: action.color, mr: 2 }}>
                    {action.icon}
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    {action.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {action.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Real-time Security Status */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Real-time Security Status
              </Typography>
              <List>
                {dashboard.siteStatuses?.map((site, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <LocationOn color={site.status === 'protected' ? 'success' : 'warning'} />
                    </ListItemIcon>
                    <ListItemText
                      primary={site.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {site.address}
                          </Typography>
                          <Chip
                            label={site.status === 'protected' ? 'Protected' : 'Alert'}
                            size="small"
                            color={site.status === 'protected' ? 'success' : 'warning'}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText primary="Loading site statuses..." />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List dense>
                {dashboard.recentActivity?.slice(0, 5).map((activity, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Assignment color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.message}
                      secondary={formatTime(activity.timestamp)}
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText primary="No recent activity" />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Service Level Agreement Progress */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Service Level Agreement Performance
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">Response Time</Typography>
                      <Typography variant="body2">95% (Target: 90%)</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={95} color="success" />
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">Coverage Hours</Typography>
                      <Typography variant="body2">98% (Target: 95%)</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={98} color="success" />
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">Report Quality</Typography>
                      <Typography variant="body2">92% (Target: 85%)</Typography>
                    </Box>
                    <LinearProgress variant="determinate" value={92} color="success" />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
