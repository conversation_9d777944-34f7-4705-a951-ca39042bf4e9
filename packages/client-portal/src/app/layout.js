// BahinLink Client Portal Layout
// ⚠️ CRITICAL: Real Clerk authentication and client-specific data ONLY

import { <PERSON><PERSON>rov<PERSON> } from '@clerk/nextjs';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

// Real Clerk publishable key - MUST be production key
const CLERK_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

export const metadata = {
  title: 'BahinLink Client Portal - Security Service Monitoring',
  description: 'Real-time security service monitoring and reporting for clients',
  keywords: 'security, monitoring, reports, real-time, Senegal, Bahin SARL',
  authors: [{ name: 'Bahin SARL' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({ children }) {
  return (
    <ClerkProvider publishableKey={CLERK_PUBLISHABLE_KEY}>
      <html lang="en">
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </head>
        <body className={inter.className}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
