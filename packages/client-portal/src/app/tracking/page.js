// BahinLink Client Portal - Live Tracking Page
// ⚠️ CRITICAL: Real-time agent tracking for clients ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  LocationOn,
  Security,
  Schedule,
  Refresh,
  Fullscreen,
  FilterList,
  CheckCircle,
  Warning,
  Error
} from '@mui/icons-material';

import ApiService from '../../services/ApiService';
import ClientMap from '../../components/ClientMap';
import { formatTime, formatDate } from '@bahinlink/shared';

export default function TrackingPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [trackingData, setTrackingData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  
  // Filters
  const [filters, setFilters] = useState({
    siteId: '',
    status: '',
    showGeofence: true,
    showHistory: false
  });

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadTrackingData();
      
      // Auto-refresh every 30 seconds if enabled
      let interval;
      if (autoRefresh) {
        interval = setInterval(loadTrackingData, 30000);
      }
      
      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [isLoaded, isSignedIn, autoRefresh]);

  const loadTrackingData = async () => {
    try {
      setError(null);
      
      const [sitesResponse, agentsResponse] = await Promise.all([
        ApiService.get('/client/sites'),
        ApiService.get('/client/agents', {
          includeLocation: 'true',
          includeCurrentShift: 'true'
        })
      ]);
      
      if (sitesResponse.success && agentsResponse.success) {
        setTrackingData({
          sites: sitesResponse.data,
          agents: agentsResponse.data,
          summary: {
            totalSites: sitesResponse.data.length,
            protectedSites: sitesResponse.data.filter(s => s.securityStatus === 'protected').length,
            activeAgents: agentsResponse.data.filter(a => a.status === 'ON_SHIFT').length,
            agentsWithGPS: agentsResponse.data.filter(a => a.location).length
          }
        });
      } else {
        throw new Error('Failed to load tracking data');
      }
    } catch (error) {
      console.error('Load tracking data error:', error);
      setError(error.message || 'Failed to load tracking data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTrackingData();
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getFilteredAgents = () => {
    if (!trackingData?.agents) return [];
    
    return trackingData.agents.filter(agent => {
      if (filters.siteId && agent.currentShift?.site?.id !== filters.siteId) {
        return false;
      }
      
      if (filters.status && agent.status !== filters.status) {
        return false;
      }
      
      return true;
    });
  };

  const getSecurityStatusColor = (status) => {
    switch (status) {
      case 'protected': return 'success';
      case 'alert': return 'warning';
      case 'unprotected': return 'error';
      default: return 'default';
    }
  };

  const getSecurityStatusIcon = (status) => {
    switch (status) {
      case 'protected': return <CheckCircle />;
      case 'alert': return <Warning />;
      case 'unprotected': return <Error />;
      default: return <Security />;
    }
  };

  const getAgentStatusColor = (status) => {
    switch (status) {
      case 'ON_SHIFT': return 'success';
      case 'AVAILABLE': return 'info';
      case 'OFFLINE': return 'default';
      default: return 'default';
    }
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography variant="h6">Loading tracking data...</Typography>
      </Box>
    );
  }

  const filteredAgents = getFilteredAgents();

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Live Security Tracking
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                color="primary"
              />
            }
            label="Auto Refresh"
          />
          <IconButton
            onClick={handleRefresh}
            disabled={refreshing}
            color="primary"
          >
            <Refresh />
          </IconButton>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Protected Sites
                  </Typography>
                  <Typography variant="h4" component="div">
                    {trackingData?.summary?.protectedSites || 0}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    of {trackingData?.summary?.totalSites || 0} total
                  </Typography>
                </Box>
                <Security color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Guards
                  </Typography>
                  <Typography variant="h4" component="div">
                    {trackingData?.summary?.activeAgents || 0}
                  </Typography>
                  <Typography variant="body2" color="info.main">
                    On duty now
                  </Typography>
                </Box>
                <CheckCircle color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    GPS Tracking
                  </Typography>
                  <Typography variant="h4" component="div">
                    {trackingData?.summary?.agentsWithGPS || 0}
                  </Typography>
                  <Typography variant="body2" color="primary.main">
                    Live locations
                  </Typography>
                </Box>
                <LocationOn color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Last Update
                  </Typography>
                  <Typography variant="h6" component="div">
                    {formatTime(new Date())}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {refreshing ? 'Updating...' : 'Real-time'}
                  </Typography>
                </Box>
                <Schedule color="action" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Filter by Site</InputLabel>
                <Select
                  value={filters.siteId}
                  onChange={(e) => handleFilterChange('siteId', e.target.value)}
                  label="Filter by Site"
                >
                  <MenuItem value="">All Sites</MenuItem>
                  {trackingData?.sites?.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Agent Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Agent Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="ON_SHIFT">On Shift</MenuItem>
                  <MenuItem value="AVAILABLE">Available</MenuItem>
                  <MenuItem value="OFFLINE">Offline</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={filters.showGeofence}
                    onChange={(e) => handleFilterChange('showGeofence', e.target.checked)}
                    color="primary"
                  />
                }
                label="Show Geofences"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={filters.showHistory}
                    onChange={(e) => handleFilterChange('showHistory', e.target.checked)}
                    color="primary"
                  />
                }
                label="Show History"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Map */}
        <Grid item xs={12} lg={8}>
          <Card sx={{ height: 600 }}>
            <CardContent sx={{ height: '100%', p: 0 }}>
              <ClientMap
                sites={trackingData?.sites || []}
                agents={filteredAgents}
                showGeofence={filters.showGeofence}
                showHistory={filters.showHistory}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Agent List */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: 600, overflow: 'auto' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Security Personnel ({filteredAgents.length})
              </Typography>
              
              {filteredAgents.map((agent) => (
                <Card key={agent.id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent sx={{ pb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {agent.user.name}
                      </Typography>
                      <Chip
                        label={agent.status.replace('_', ' ')}
                        color={getAgentStatusColor(agent.status)}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      ID: {agent.employeeId}
                    </Typography>
                    
                    {agent.currentShift && (
                      <Box mb={1}>
                        <Typography variant="body2" fontWeight="bold">
                          {agent.currentShift.site.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTime(agent.currentShift.startTime)} - {formatTime(agent.currentShift.endTime)}
                        </Typography>
                      </Box>
                    )}
                    
                    {agent.location ? (
                      <Box display="flex" alignItems="center" mt={1}>
                        <LocationOn sx={{ fontSize: 16, mr: 1, color: 'success.main' }} />
                        <Typography variant="caption" color="text.secondary">
                          GPS: {agent.location.latitude.toFixed(4)}, {agent.location.longitude.toFixed(4)}
                        </Typography>
                        {agent.location.withinGeofence !== undefined && (
                          <Chip
                            label={agent.location.withinGeofence ? 'In Zone' : 'Out of Zone'}
                            color={agent.location.withinGeofence ? 'success' : 'warning'}
                            size="small"
                            sx={{ ml: 1, height: 16 }}
                          />
                        )}
                      </Box>
                    ) : (
                      <Box display="flex" alignItems="center" mt={1}>
                        <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          No GPS signal
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              ))}
              
              {filteredAgents.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Security sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No agents match the current filters
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Site Status Cards */}
      <Grid container spacing={3} mt={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Site Security Status
          </Typography>
        </Grid>
        {trackingData?.sites?.map((site) => (
          <Grid item xs={12} sm={6} md={4} key={site.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" component="div">
                    {site.name}
                  </Typography>
                  <Chip
                    icon={getSecurityStatusIcon(site.securityStatus)}
                    label={site.securityStatus?.toUpperCase() || 'UNKNOWN'}
                    color={getSecurityStatusColor(site.securityStatus)}
                    size="small"
                  />
                </Box>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {site.address}
                </Typography>
                
                <Box display="flex" justifyContent="space-between" mt={2}>
                  <Typography variant="body2">
                    Active Guards: {site.activeAgents || 0}
                  </Typography>
                  <Typography variant="body2">
                    Reports: {site.recentReports || 0}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
