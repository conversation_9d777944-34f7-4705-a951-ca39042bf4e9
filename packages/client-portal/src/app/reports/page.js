// BahinLink Client Portal - Reports Page
// ⚠️ CRITICAL: Real security reports for client access ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Assignment,
  Security,
  Warning,
  CheckCircle,
  Download,
  Refresh,
  FilterList,
  Visibility,
  DateRange,
  AttachFile,
  LocationOn
} from '@mui/icons-material';

import ApiService from '../../services/ApiService';
import { formatTime, formatDate } from '@bahinlink/shared';

const REPORT_TYPES = [
  { value: 'PATROL', label: 'Patrol Report', icon: 'security', color: '#2196F3' },
  { value: 'INCIDENT', label: 'Incident Report', icon: 'warning', color: '#F44336' },
  { value: 'MAINTENANCE', label: 'Maintenance Issue', icon: 'build', color: '#FF9800' },
  { value: 'VISITOR', label: 'Visitor Log', icon: 'person', color: '#4CAF50' },
  { value: 'OTHER', label: 'Other', icon: 'description', color: '#9C27B0' }
];

const PRIORITY_LEVELS = [
  { value: 'LOW', label: 'Low', color: '#4CAF50' },
  { value: 'MEDIUM', label: 'Medium', color: '#FF9800' },
  { value: 'HIGH', label: 'High', color: '#F44336' }
];

export default function ReportsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState([]);
  const [sites, setSites] = useState([]);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filters
  const [filters, setFilters] = useState({
    siteId: '',
    type: '',
    priority: '',
    status: '',
    startDate: '',
    endDate: '',
    search: ''
  });
  
  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  });

  // Dialog states
  const [selectedReport, setSelectedReport] = useState(null);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadReports();
      loadSites();
    }
  }, [isLoaded, isSignedIn, filters, pagination.page]);

  const loadReports = async () => {
    try {
      setError(null);
      
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      };

      const response = await ApiService.get('/client/reports', params);
      
      if (response.success) {
        setReports(response.data);
        setPagination(prev => ({
          ...prev,
          total: response.pagination.total
        }));
      } else {
        throw new Error(response.error?.message || 'Failed to load reports');
      }
    } catch (error) {
      console.error('Load reports error:', error);
      setError(error.message || 'Failed to load reports');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadSites = async () => {
    try {
      const response = await ApiService.get('/client/sites');
      if (response.success) {
        setSites(response.data);
      }
    } catch (error) {
      console.error('Load sites error:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadReports();
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleViewReport = async (reportId) => {
    try {
      const response = await ApiService.get(`/reports/${reportId}`);
      if (response.success) {
        setSelectedReport(response.data);
        setReportDialogOpen(true);
      }
    } catch (error) {
      console.error('Load report details error:', error);
      setError('Failed to load report details');
    }
  };

  const handleDownloadReport = async (reportId) => {
    try {
      const response = await ApiService.get(`/reports/${reportId}/download`, {
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `report_${reportId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download report error:', error);
      setError('Failed to download report');
    }
  };

  const getReportTypeInfo = (type) => {
    return REPORT_TYPES.find(t => t.value === type) || REPORT_TYPES[0];
  };

  const getPriorityInfo = (priority) => {
    return PRIORITY_LEVELS.find(p => p.value === priority) || PRIORITY_LEVELS[1];
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'SUBMITTED': return 'warning';
      case 'REJECTED': return 'error';
      case 'DRAFT': return 'default';
      default: return 'default';
    }
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography variant="h6">Loading reports...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Security Reports
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => {/* Export all reports */}}
          >
            Export All
          </Button>
          <IconButton
            onClick={handleRefresh}
            disabled={refreshing}
            color="primary"
          >
            <Refresh />
          </IconButton>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Reports
              </Typography>
              <Typography variant="h4" component="div">
                {pagination.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                This Month
              </Typography>
              <Typography variant="h4" component="div" color="primary.main">
                {reports.filter(r => 
                  new Date(r.createdAt).getMonth() === new Date().getMonth()
                ).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                High Priority
              </Typography>
              <Typography variant="h4" component="div" color="error.main">
                {reports.filter(r => r.priority === 'HIGH').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Incidents
              </Typography>
              <Typography variant="h4" component="div" color="warning.main">
                {reports.filter(r => r.type === 'INCIDENT').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search reports..."
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Site</InputLabel>
                <Select
                  value={filters.siteId}
                  onChange={(e) => handleFilterChange('siteId', e.target.value)}
                  label="Site"
                >
                  <MenuItem value="">All Sites</MenuItem>
                  {sites.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={filters.type}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  label="Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {REPORT_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  onChange={(e) => handleFilterChange('priority', e.target.value)}
                  label="Priority"
                >
                  <MenuItem value="">All Priorities</MenuItem>
                  {PRIORITY_LEVELS.map((priority) => (
                    <MenuItem key={priority.value} value={priority.value}>
                      {priority.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                size="small"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Reports List */}
      <Grid container spacing={3}>
        {reports.map((report) => {
          const typeInfo = getReportTypeInfo(report.type);
          const priorityInfo = getPriorityInfo(report.priority);
          
          return (
            <Grid item xs={12} md={6} lg={4} key={report.id}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Chip
                      label={typeInfo.label}
                      size="small"
                      sx={{ backgroundColor: typeInfo.color, color: 'white' }}
                    />
                    <Chip
                      label={report.status}
                      color={getStatusColor(report.status)}
                      size="small"
                    />
                  </Box>
                  
                  <Typography variant="h6" component="div" gutterBottom>
                    {report.title}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {report.description.length > 100 
                      ? `${report.description.substring(0, 100)}...`
                      : report.description
                    }
                  </Typography>
                  
                  <Box display="flex" alignItems="center" mb={1}>
                    <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {report.site?.name}
                    </Typography>
                  </Box>
                  
                  <Box display="flex" alignItems="center" mb={1}>
                    <Security sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {report.agent?.name}
                    </Typography>
                  </Box>
                  
                  <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                    <Typography variant="caption" color="text.secondary">
                      {formatDate(report.createdAt)}
                    </Typography>
                    <Box>
                      <Chip
                        label={priorityInfo.label}
                        size="small"
                        sx={{ 
                          backgroundColor: priorityInfo.color, 
                          color: 'white',
                          mr: 1
                        }}
                      />
                      {report.attachments?.length > 0 && (
                        <Chip
                          icon={<AttachFile />}
                          label={report.attachments.length}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </Box>
                  
                  <Box display="flex" justifyContent="space-between" mt={2}>
                    <Button
                      size="small"
                      startIcon={<Visibility />}
                      onClick={() => handleViewReport(report.id)}
                    >
                      View
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Download />}
                      onClick={() => handleDownloadReport(report.id)}
                    >
                      Download
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Empty State */}
      {reports.length === 0 && !loading && (
        <Box textAlign="center" py={8}>
          <Assignment sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No reports found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your filters or check back later
          </Typography>
        </Box>
      )}

      {/* Pagination */}
      {pagination.total > pagination.limit && (
        <Box display="flex" justifyContent="center" mt={4}>
          <Button
            disabled={pagination.page === 1}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
          >
            Previous
          </Button>
          <Typography sx={{ mx: 2, alignSelf: 'center' }}>
            Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
          </Typography>
          <Button
            disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
          >
            Next
          </Button>
        </Box>
      )}

      {/* Report Details Dialog */}
      <Dialog 
        open={reportDialogOpen} 
        onClose={() => setReportDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedReport && (
          <>
            <DialogTitle>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{selectedReport.title}</Typography>
                <Chip
                  label={selectedReport.status}
                  color={getStatusColor(selectedReport.status)}
                  size="small"
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Type</Typography>
                  <Typography variant="body2" gutterBottom>
                    {getReportTypeInfo(selectedReport.type).label}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Priority</Typography>
                  <Typography variant="body2" gutterBottom>
                    {getPriorityInfo(selectedReport.priority).label}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Site</Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedReport.site?.name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Agent</Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedReport.agent?.name}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>Description</Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedReport.description}
                  </Typography>
                </Grid>
                {selectedReport.location && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Location</Typography>
                    <Typography variant="body2" gutterBottom>
                      {selectedReport.location.latitude.toFixed(6)}, {selectedReport.location.longitude.toFixed(6)}
                    </Typography>
                  </Grid>
                )}
                {selectedReport.attachments?.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Attachments</Typography>
                    <List dense>
                      {selectedReport.attachments.map((attachment, index) => (
                        <ListItem key={index} button>
                          <ListItemIcon>
                            <AttachFile />
                          </ListItemIcon>
                          <ListItemText 
                            primary={attachment.fileName}
                            secondary={`${(attachment.fileSize / 1024 / 1024).toFixed(2)} MB`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setReportDialogOpen(false)}>Close</Button>
              <Button 
                variant="contained"
                startIcon={<Download />}
                onClick={() => handleDownloadReport(selectedReport.id)}
              >
                Download PDF
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
}
