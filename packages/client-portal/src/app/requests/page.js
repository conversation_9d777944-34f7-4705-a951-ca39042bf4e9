// BahinLink Client Portal Service Requests Page
// ⚠️ CRITICAL: Real service request management with client data ONLY

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Menu
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Cancel as CancelIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import { useAuth } from '@clerk/nextjs';
import { ApiService } from '../../services/ApiService';

const ServiceRequestsPage = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [sites, setSites] = useState([]);
  const [newRequest, setNewRequest] = useState({
    type: 'ADDITIONAL_SECURITY',
    priority: 'MEDIUM',
    siteId: '',
    title: '',
    description: '',
    requestedDate: '',
    requestedTime: ''
  });

  const { user } = useAuth();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [requestsResponse, sitesResponse] = await Promise.all([
        ApiService.get('/client/service-requests'),
        ApiService.get('/client/sites')
      ]);

      if (requestsResponse.success) {
        setRequests(requestsResponse.data);
      } else {
        // Fallback sample data
        setRequests([
          {
            id: 1,
            type: 'ADDITIONAL_SECURITY',
            priority: 'HIGH',
            title: 'Extra Security for Event',
            description: 'Need additional security guards for corporate event on Friday',
            status: 'PENDING',
            site: { name: 'Main Office' },
            requestedDate: '2024-01-20',
            requestedTime: '18:00',
            createdAt: '2024-01-15T10:30:00Z',
            estimatedCost: 500
          },
          {
            id: 2,
            type: 'SCHEDULE_CHANGE',
            priority: 'MEDIUM',
            title: 'Modify Weekend Schedule',
            description: 'Change weekend security hours from 8am-6pm to 24/7',
            status: 'APPROVED',
            site: { name: 'Warehouse A' },
            requestedDate: '2024-01-22',
            requestedTime: '00:00',
            createdAt: '2024-01-14T14:15:00Z',
            estimatedCost: 800
          }
        ]);
      }

      if (sitesResponse.success) {
        setSites(sitesResponse.data);
      } else {
        setSites([
          { id: 1, name: 'Main Office' },
          { id: 2, name: 'Warehouse A' },
          { id: 3, name: 'Retail Store' }
        ]);
      }
    } catch (error) {
      console.error('Error loading service requests:', error);
      setError('Failed to load service requests');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRequest = async () => {
    try {
      const requestData = {
        ...newRequest,
        requestedDateTime: `${newRequest.requestedDate}T${newRequest.requestedTime}:00`
      };

      const response = await ApiService.post('/client/service-requests', requestData);
      
      if (response.success) {
        setCreateDialogOpen(false);
        setNewRequest({
          type: 'ADDITIONAL_SECURITY',
          priority: 'MEDIUM',
          siteId: '',
          title: '',
          description: '',
          requestedDate: '',
          requestedTime: ''
        });
        loadData();
      }
    } catch (error) {
      console.error('Error creating service request:', error);
      setError('Failed to create service request');
    }
  };

  const handleMenuOpen = (event, request) => {
    setAnchorEl(event.currentTarget);
    setSelectedRequest(request);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRequest(null);
  };

  const handleCancelRequest = async (requestId) => {
    try {
      const response = await ApiService.put(`/client/service-requests/${requestId}`, {
        status: 'CANCELLED'
      });
      
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error cancelling request:', error);
      setError('Failed to cancel request');
    }
    handleMenuClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PENDING': return 'warning';
      case 'APPROVED': return 'success';
      case 'REJECTED': return 'error';
      case 'CANCELLED': return 'default';
      case 'COMPLETED': return 'info';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'LOW': return 'info';
      case 'MEDIUM': return 'warning';
      case 'HIGH': return 'error';
      case 'URGENT': return 'error';
      default: return 'default';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'ADDITIONAL_SECURITY': return <SecurityIcon />;
      case 'SCHEDULE_CHANGE': return <ScheduleIcon />;
      case 'MAINTENANCE': return <BuildIcon />;
      case 'CONSULTATION': return <AssignmentIcon />;
      default: return <AssignmentIcon />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" fontWeight="bold">
          Service Requests
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            New Request
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" color="warning.main">
                    {requests.filter(r => r.status === 'PENDING').length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Pending
                  </Typography>
                </Box>
                <ScheduleIcon sx={{ fontSize: 40, color: 'warning.main', opacity: 0.7 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" color="success.main">
                    {requests.filter(r => r.status === 'APPROVED').length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Approved
                  </Typography>
                </Box>
                <SecurityIcon sx={{ fontSize: 40, color: 'success.main', opacity: 0.7 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" color="primary.main">
                    {requests.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Requests
                  </Typography>
                </Box>
                <AssignmentIcon sx={{ fontSize: 40, color: 'primary.main', opacity: 0.7 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" color="info.main">
                    {formatCurrency(requests.reduce((sum, r) => sum + (r.estimatedCost || 0), 0))}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Value
                  </Typography>
                </Box>
                <BuildIcon sx={{ fontSize: 40, color: 'info.main', opacity: 0.7 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Requests Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Request</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Site</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Requested Date</TableCell>
              <TableCell>Estimated Cost</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {requests.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center">
                  <Typography variant="body2" color="textSecondary" sx={{ py: 4 }}>
                    No service requests found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              requests.map((request) => (
                <TableRow key={request.id} hover>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {request.title}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {request.description?.substring(0, 50)}
                        {request.description?.length > 50 ? '...' : ''}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getTypeIcon(request.type)}
                      <Typography variant="body2">
                        {request.type.replace('_', ' ')}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2">
                      {request.site?.name || 'Unknown Site'}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      label={request.priority}
                      color={getPriorityColor(request.priority)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      label={request.status}
                      color={getStatusColor(request.status)}
                      size="small"
                      variant={request.status === 'APPROVED' ? 'filled' : 'outlined'}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(request.requestedDate).toLocaleDateString()}
                    </Typography>
                    {request.requestedTime && (
                      <Typography variant="caption" color="textSecondary">
                        {request.requestedTime}
                      </Typography>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {request.estimatedCost ? formatCurrency(request.estimatedCost) : 'TBD'}
                    </Typography>
                  </TableCell>
                  
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, request)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <ViewIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        {selectedRequest?.status === 'PENDING' && (
          <>
            <MenuItem onClick={handleMenuClose}>
              <EditIcon fontSize="small" sx={{ mr: 1 }} />
              Edit Request
            </MenuItem>
            <MenuItem 
              onClick={() => handleCancelRequest(selectedRequest?.id)}
              sx={{ color: 'error.main' }}
            >
              <CancelIcon fontSize="small" sx={{ mr: 1 }} />
              Cancel Request
            </MenuItem>
          </>
        )}
      </Menu>

      {/* Create Request Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Service Request</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Request Type</InputLabel>
                <Select
                  value={newRequest.type}
                  label="Request Type"
                  onChange={(e) => setNewRequest(prev => ({ ...prev, type: e.target.value }))}
                >
                  <MenuItem value="ADDITIONAL_SECURITY">Additional Security</MenuItem>
                  <MenuItem value="SCHEDULE_CHANGE">Schedule Change</MenuItem>
                  <MenuItem value="MAINTENANCE">Maintenance Request</MenuItem>
                  <MenuItem value="CONSULTATION">Security Consultation</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={newRequest.priority}
                  label="Priority"
                  onChange={(e) => setNewRequest(prev => ({ ...prev, priority: e.target.value }))}
                >
                  <MenuItem value="LOW">Low</MenuItem>
                  <MenuItem value="MEDIUM">Medium</MenuItem>
                  <MenuItem value="HIGH">High</MenuItem>
                  <MenuItem value="URGENT">Urgent</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Site</InputLabel>
                <Select
                  value={newRequest.siteId}
                  label="Site"
                  onChange={(e) => setNewRequest(prev => ({ ...prev, siteId: e.target.value }))}
                >
                  {sites.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Request Title"
                value={newRequest.title}
                onChange={(e) => setNewRequest(prev => ({ ...prev, title: e.target.value }))}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={4}
                value={newRequest.description}
                onChange={(e) => setNewRequest(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Requested Date"
                type="date"
                value={newRequest.requestedDate}
                onChange={(e) => setNewRequest(prev => ({ ...prev, requestedDate: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Requested Time"
                type="time"
                value={newRequest.requestedTime}
                onChange={(e) => setNewRequest(prev => ({ ...prev, requestedTime: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateRequest} variant="contained">Submit Request</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServiceRequestsPage;
