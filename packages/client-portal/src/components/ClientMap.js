// BahinLink Client Map Component
// ⚠️ CRITICAL: Real-time client site monitoring with production GPS data ONLY

'use client';

import { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Box, Typography, Chip, Alert } from '@mui/material';
import { LocationOn, Security, CheckCircle, Warning } from '@mui/icons-material';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: '/leaflet/marker-icon-2x.png',
  iconUrl: '/leaflet/marker-icon.png',
  shadowUrl: '/leaflet/marker-shadow.png',
});

// Custom icons for client view
const siteIcon = new L.Icon({
  iconUrl: '/icons/site-secure.png',
  iconRetinaUrl: '/icons/site-secure-2x.png',
  iconSize: [32, 32],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
  shadowUrl: '/leaflet/marker-shadow.png',
  shadowSize: [41, 41],
  shadowAnchor: [12, 41]
});

const guardIcon = new L.Icon({
  iconUrl: '/icons/guard-active.png',
  iconRetinaUrl: '/icons/guard-active-2x.png',
  iconSize: [28, 28],
  iconAnchor: [14, 28],
  popupAnchor: [0, -28],
  shadowUrl: '/leaflet/marker-shadow.png',
  shadowSize: [35, 35],
  shadowAnchor: [10, 35]
});

const ClientMap = ({ sites = [], agents = [], height = 400 }) => {
  const [mapReady, setMapReady] = useState(false);
  const [error, setError] = useState(null);
  const mapRef = useRef(null);

  // Default center: Dakar, Senegal (client's business area)
  const defaultCenter = [14.6937, -17.4441];
  const defaultZoom = 12;

  useEffect(() => {
    // Ensure we're in browser environment
    if (typeof window !== 'undefined') {
      setMapReady(true);
    }
  }, []);

  useEffect(() => {
    // Auto-fit map bounds when sites change
    if (mapRef.current && sites.length > 0) {
      const map = mapRef.current;
      const bounds = L.latLngBounds();
      
      sites.forEach(site => {
        if (site.latitude && site.longitude) {
          bounds.extend([site.latitude, site.longitude]);
        }
      });
      
      if (bounds.isValid()) {
        map.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [sites]);

  const getSiteStatusColor = (activeAgents) => {
    if (activeAgents > 0) return '#4caf50'; // Green - Protected
    return '#ff9800'; // Orange - Unprotected
  };

  const getGuardStatusColor = (status) => {
    switch (status) {
      case 'active': return '#4caf50';
      case 'on_shift': return '#2196f3';
      case 'break': return '#ff9800';
      default: return '#757575';
    }
  };

  const formatLastUpdate = (timestamp) => {
    if (!timestamp) return 'Unknown';
    
    const now = new Date();
    const updateTime = new Date(timestamp);
    const diffMinutes = Math.floor((now - updateTime) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return updateTime.toLocaleDateString();
  };

  if (!mapReady) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height={height}
        bgcolor="#f5f5f5"
      >
        <Typography>Loading security map...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ height }}>
        Failed to load security map: {error}
      </Alert>
    );
  }

  return (
    <Box height={height} width="100%" position="relative">
      <MapContainer
        center={defaultCenter}
        zoom={defaultZoom}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
      >
        {/* Real OpenStreetMap tiles */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {/* Client site markers with real coordinates */}
        {sites.map((site) => (
          <div key={site.id}>
            <Marker
              position={[site.latitude, site.longitude]}
              icon={siteIcon}
            >
              <Popup>
                <Box p={1}>
                  <Typography variant="h6" gutterBottom>
                    <Security sx={{ mr: 1, verticalAlign: 'middle' }} />
                    {site.name}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Address:</strong> {site.address}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Protection Status:</strong>{' '}
                    <Chip 
                      label={site.activeAgents > 0 ? 'Protected' : 'Unprotected'}
                      size="small"
                      sx={{ 
                        backgroundColor: getSiteStatusColor(site.activeAgents),
                        color: 'white'
                      }}
                    />
                  </Typography>
                  
                  {site.activeAgents > 0 && (
                    <Typography variant="body2" gutterBottom>
                      <strong>Active Guards:</strong> {site.activeAgents}
                    </Typography>
                  )}
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Security Level:</strong> {site.securityLevel || 'Standard'}
                  </Typography>
                  
                  {site.lastIncident && (
                    <Typography variant="body2" color="text.secondary">
                      <strong>Last Incident:</strong> {formatLastUpdate(site.lastIncident)}
                    </Typography>
                  )}
                  
                  {site.nextPatrol && (
                    <Typography variant="body2" color="primary">
                      <strong>Next Patrol:</strong> {formatLastUpdate(site.nextPatrol)}
                    </Typography>
                  )}
                </Box>
              </Popup>
            </Marker>
            
            {/* Security perimeter circle */}
            <Circle
              center={[site.latitude, site.longitude]}
              radius={site.geofenceRadius || 100}
              pathOptions={{
                color: getSiteStatusColor(site.activeAgents),
                fillColor: getSiteStatusColor(site.activeAgents),
                fillOpacity: 0.1,
                weight: 2
              }}
            />
          </div>
        ))}
        
        {/* Active guard markers with real GPS positions */}
        {agents.filter(agent => agent.latitude && agent.longitude).map((agent) => (
          <Marker
            key={agent.id}
            position={[agent.latitude, agent.longitude]}
            icon={guardIcon}
          >
            <Popup>
              <Box p={1}>
                <Typography variant="h6" gutterBottom>
                  <Security sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Security Guard
                </Typography>
                
                <Typography variant="body2" gutterBottom>
                  <strong>ID:</strong> {agent.employeeId}
                </Typography>
                
                <Typography variant="body2" gutterBottom>
                  <strong>Status:</strong>{' '}
                  <Chip 
                    label={agent.status === 'active' ? 'On Duty' : agent.status}
                    size="small"
                    sx={{ 
                      backgroundColor: getGuardStatusColor(agent.status),
                      color: 'white'
                    }}
                  />
                </Typography>
                
                {agent.currentSite && (
                  <Typography variant="body2" gutterBottom>
                    <strong>Assigned Site:</strong> {agent.currentSite}
                  </Typography>
                )}
                
                <Typography variant="body2" gutterBottom>
                  <strong>Location:</strong> {agent.latitude.toFixed(6)}, {agent.longitude.toFixed(6)}
                </Typography>
                
                <Typography variant="body2" color="text.secondary">
                  <strong>Last Update:</strong> {formatLastUpdate(agent.lastUpdate)}
                </Typography>
                
                {agent.withinGeofence !== undefined && (
                  <Typography 
                    variant="body2" 
                    color={agent.withinGeofence ? 'success.main' : 'warning.main'}
                  >
                    <strong>Position:</strong> {agent.withinGeofence ? 'At assigned location ✓' : 'Away from site ⚠️'}
                  </Typography>
                )}
              </Box>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
      
      {/* Map legend for client view */}
      <Box
        position="absolute"
        top={10}
        right={10}
        bgcolor="white"
        p={1}
        borderRadius={1}
        boxShadow={2}
        zIndex={1000}
      >
        <Typography variant="caption" display="block" gutterBottom>
          <strong>Security Status</strong>
        </Typography>
        <Box display="flex" alignItems="center" mb={0.5}>
          <Box
            width={12}
            height={12}
            bgcolor="#4caf50"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">Protected Site</Typography>
        </Box>
        <Box display="flex" alignItems="center" mb={0.5}>
          <Box
            width={12}
            height={12}
            bgcolor="#ff9800"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">Unprotected Site</Typography>
        </Box>
        <Box display="flex" alignItems="center" mb={0.5}>
          <Box
            width={12}
            height={12}
            bgcolor="#2196f3"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">Active Guard</Typography>
        </Box>
      </Box>
      
      {/* Real-time status indicator */}
      <Box
        position="absolute"
        bottom={10}
        left={10}
        bgcolor="white"
        p={1}
        borderRadius={1}
        boxShadow={2}
        zIndex={1000}
        display="flex"
        alignItems="center"
      >
        <Box
          width={8}
          height={8}
          bgcolor="#4caf50"
          borderRadius="50%"
          mr={1}
          sx={{
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.5 },
              '100%': { opacity: 1 }
            }
          }}
        />
        <Typography variant="caption">
          Live Tracking Active
        </Typography>
      </Box>
    </Box>
  );
};

export default ClientMap;
