// BahinLink Client Portal API Service
// ⚠️ CRITICAL: Real production API integration for client data ONLY

import axios from 'axios';
import { auth } from '@clerk/nextjs';

// Real production API base URL
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:3000/api' 
  : 'https://api.bahinlink.com/api';

class ClientApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for real client authentication
   */
  setupInterceptors() {
    // Request interceptor to add real Clerk token
    axios.interceptors.request.use(
      async (config) => {
        try {
          // Get real Clerk token from server-side auth
          const { getToken } = auth();
          const token = await getToken();
          
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          
          config.headers['Content-Type'] = 'application/json';
          config.baseURL = this.baseURL;
          
          console.log(`Client API Request: ${config.method?.toUpperCase()} ${config.url}`);
          return config;
        } catch (error) {
          console.error('Client request interceptor error:', error);
          return config;
        }
      },
      (error) => {
        console.error('Client request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    axios.interceptors.response.use(
      (response) => {
        console.log(`Client API Response: ${response.status} ${response.config.url}`);
        return response.data;
      },
      (error) => {
        console.error('Client API Error:', error.response?.data || error.message);
        
        if (error.response?.status === 401) {
          // Redirect to sign-in
          window.location.href = '/sign-in';
        }
        
        return Promise.reject(error.response?.data || error);
      }
    );
  }

  /**
   * GET request to real API
   */
  async get(endpoint, params = {}) {
    try {
      const response = await axios.get(endpoint, { params });
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST request to real API
   */
  async post(endpoint, data = {}) {
    try {
      const response = await axios.post(endpoint, data);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT request to real API
   */
  async put(endpoint, data = {}) {
    try {
      const response = await axios.put(endpoint, data);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // Client-specific API endpoints

  /**
   * Get client's sites with real data
   */
  async getClientSites() {
    return this.get('/client/sites');
  }

  /**
   * Get agents assigned to client's sites
   */
  async getClientAgents() {
    return this.get('/client/agents');
  }

  /**
   * Get reports for client's sites
   */
  async getClientReports(params = {}) {
    return this.get('/client/reports', params);
  }

  /**
   * Get specific report details
   */
  async getReport(reportId) {
    return this.get(`/client/reports/${reportId}`);
  }

  /**
   * Download report as PDF
   */
  async downloadReport(reportId) {
    try {
      const response = await axios.get(`/client/reports/${reportId}/download`, {
        responseType: 'blob',
        baseURL: this.baseURL
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get client requests/service tickets
   */
  async getClientRequests(params = {}) {
    return this.get('/client/requests', params);
  }

  /**
   * Create new service request
   */
  async createServiceRequest(data) {
    return this.post('/client/requests', data);
  }

  /**
   * Update service request
   */
  async updateServiceRequest(requestId, data) {
    return this.put(`/client/requests/${requestId}`, data);
  }

  /**
   * Provide feedback on service request
   */
  async provideFeedback(requestId, feedback) {
    return this.post(`/client/requests/${requestId}/feedback`, feedback);
  }

  /**
   * Get real-time dashboard data
   */
  async getDashboardData() {
    return this.get('/client/dashboard');
  }

  /**
   * Get site security analytics
   */
  async getSiteAnalytics(siteId, params = {}) {
    return this.get(`/client/sites/${siteId}/analytics`, params);
  }

  /**
   * Get service level agreement metrics
   */
  async getSLAMetrics() {
    return this.get('/client/sla-metrics');
  }

  /**
   * Get incident history
   */
  async getIncidentHistory(params = {}) {
    return this.get('/client/incidents', params);
  }

  /**
   * Get patrol schedules
   */
  async getPatrolSchedules(params = {}) {
    return this.get('/client/patrol-schedules', params);
  }

  /**
   * Get agent performance metrics
   */
  async getAgentPerformance(params = {}) {
    return this.get('/client/agent-performance', params);
  }

  /**
   * Get billing information
   */
  async getBillingInfo(params = {}) {
    return this.get('/client/billing', params);
  }

  /**
   * Get contract details
   */
  async getContractDetails() {
    return this.get('/client/contract');
  }

  /**
   * Submit client satisfaction rating
   */
  async submitRating(data) {
    return this.post('/client/rating', data);
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences() {
    return this.get('/client/notification-preferences');
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(preferences) {
    return this.put('/client/notification-preferences', preferences);
  }

  /**
   * Get emergency contacts
   */
  async getEmergencyContacts() {
    return this.get('/client/emergency-contacts');
  }

  /**
   * Update emergency contacts
   */
  async updateEmergencyContacts(contacts) {
    return this.put('/client/emergency-contacts', contacts);
  }

  /**
   * Export reports to various formats
   */
  async exportReports(format = 'pdf', params = {}) {
    try {
      const response = await axios.get('/client/reports/export', {
        params: { format, ...params },
        responseType: 'blob',
        baseURL: this.baseURL
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get real-time alerts
   */
  async getAlerts(params = {}) {
    return this.get('/client/alerts', params);
  }

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(alertId) {
    return this.put(`/client/alerts/${alertId}/acknowledge`);
  }

  /**
   * Get service statistics
   */
  async getServiceStatistics(params = {}) {
    return this.get('/client/statistics', params);
  }

  /**
   * Schedule site visit
   */
  async scheduleSiteVisit(data) {
    return this.post('/client/site-visits', data);
  }

  /**
   * Get scheduled site visits
   */
  async getSiteVisits(params = {}) {
    return this.get('/client/site-visits', params);
  }

  /**
   * Request additional services
   */
  async requestAdditionalServices(data) {
    return this.post('/client/additional-services', data);
  }

  /**
   * Get service history
   */
  async getServiceHistory(params = {}) {
    return this.get('/client/service-history', params);
  }
}

export default new ClientApiService();
