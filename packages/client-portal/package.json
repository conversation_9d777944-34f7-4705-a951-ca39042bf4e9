{"name": "@bahinlink/client-portal", "version": "1.0.0", "description": "BahinLink Client Portal - Real-time security service monitoring", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "vercel --prod"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.0.4", "@clerk/nextjs": "^4.29.1", "@mui/material": "^5.15.3", "@mui/icons-material": "^5.15.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "react-leaflet": "^4.2.1", "leaflet": "^1.9.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "date-fns": "^3.0.6", "lodash": "^4.17.21", "recharts": "^2.8.0", "react-pdf": "^7.6.0", "jspdf": "^2.5.1", "react-signature-canvas": "^1.0.6", "@vercel/analytics": "^1.1.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4"}}