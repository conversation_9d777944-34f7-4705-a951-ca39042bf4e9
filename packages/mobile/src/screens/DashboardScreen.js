// BahinLink Mobile Dashboard Screen
// ⚠️ CRITICAL: Real agent dashboard with live GPS tracking ONLY

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import Icon from 'react-native-vector-icons/MaterialIcons';

import LocationService from '../services/LocationService';
import ApiService from '../services/ApiService';
import { formatTime, formatDate } from '@bahinlink/shared';

const { width } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationStatus, setLocationStatus] = useState('checking');

  useEffect(() => {
    initializeDashboard();
    startLocationTracking();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const initializeDashboard = async () => {
    try {
      await loadDashboardData();
    } catch (error) {
      console.error('Dashboard initialization error:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      const [profileResponse, shiftsResponse, reportsResponse] = await Promise.all([
        ApiService.get('/auth/profile'),
        ApiService.get('/shifts', { 
          agentId: user?.id,
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        }),
        ApiService.get('/reports', { 
          agentId: user?.id,
          limit: 5 
        })
      ]);

      const currentShift = shiftsResponse.data?.find(shift => 
        shift.status === 'IN_PROGRESS' || shift.status === 'SCHEDULED'
      );

      setDashboardData({
        profile: profileResponse.data,
        currentShift,
        upcomingShifts: shiftsResponse.data?.filter(shift => 
          shift.status === 'SCHEDULED' && shift.id !== currentShift?.id
        ).slice(0, 3) || [],
        recentReports: reportsResponse.data || [],
        stats: {
          totalShifts: shiftsResponse.summary?.totalShifts || 0,
          completedShifts: shiftsResponse.summary?.completedShifts || 0,
          hoursWorked: shiftsResponse.summary?.totalHoursWorked || 0,
          reportsSubmitted: reportsResponse.summary?.totalReports || 0
        }
      });
    } catch (error) {
      console.error('Load dashboard data error:', error);
      throw error;
    }
  };

  const startLocationTracking = async () => {
    try {
      setLocationStatus('requesting');
      
      const hasPermission = await LocationService.requestLocationPermission();
      if (!hasPermission) {
        setLocationStatus('denied');
        Alert.alert(
          'Location Permission Required',
          'Please enable location services to track your work location.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => LocationService.openSettings() }
          ]
        );
        return;
      }

      setLocationStatus('active');
      
      // Start continuous tracking
      LocationService.startTracking((location) => {
        setCurrentLocation(location);
      });

      // Get initial location
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
      
    } catch (error) {
      console.error('Location tracking error:', error);
      setLocationStatus('error');
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadDashboardData();
    } catch (error) {
      Alert.alert('Error', 'Failed to refresh data');
    } finally {
      setRefreshing(false);
    }
  };

  const handleClockIn = () => {
    if (!currentLocation) {
      Alert.alert('Location Required', 'Please wait for GPS location to be available');
      return;
    }
    
    navigation.navigate('TimeTracking', { 
      action: 'clock-in',
      location: currentLocation,
      shift: dashboardData?.currentShift
    });
  };

  const handleClockOut = () => {
    navigation.navigate('TimeTracking', { 
      action: 'clock-out',
      location: currentLocation,
      shift: dashboardData?.currentShift
    });
  };

  const getLocationStatusColor = () => {
    switch (locationStatus) {
      case 'active': return '#4CAF50';
      case 'denied': return '#F44336';
      case 'error': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  const getLocationStatusText = () => {
    switch (locationStatus) {
      case 'active': return 'GPS Active';
      case 'denied': return 'GPS Denied';
      case 'error': return 'GPS Error';
      case 'requesting': return 'Requesting GPS';
      default: return 'Checking GPS';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  const { profile, currentShift, upcomingShifts, recentReports, stats } = dashboardData || {};

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.welcomeText}>Welcome back,</Text>
          <Text style={styles.nameText}>{profile?.agent?.user?.firstName || 'Agent'}</Text>
          <Text style={styles.employeeIdText}>ID: {profile?.agent?.employeeId}</Text>
        </View>
        <View style={styles.locationStatus}>
          <View style={[styles.locationDot, { backgroundColor: getLocationStatusColor() }]} />
          <Text style={styles.locationText}>{getLocationStatusText()}</Text>
        </View>
      </View>

      {/* Current Shift Card */}
      {currentShift ? (
        <View style={styles.currentShiftCard}>
          <View style={styles.cardHeader}>
            <Icon name="schedule" size={24} color="#2196F3" />
            <Text style={styles.cardTitle}>Current Shift</Text>
          </View>
          
          <View style={styles.shiftDetails}>
            <Text style={styles.siteName}>{currentShift.site?.name}</Text>
            <Text style={styles.siteAddress}>{currentShift.site?.address}</Text>
            <Text style={styles.shiftTime}>
              {formatTime(currentShift.startTime)} - {formatTime(currentShift.endTime)}
            </Text>
          </View>

          <View style={styles.shiftActions}>
            {currentShift.isCurrentlyActive ? (
              <TouchableOpacity style={styles.clockOutButton} onPress={handleClockOut}>
                <Icon name="logout" size={20} color="#fff" />
                <Text style={styles.buttonText}>Clock Out</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity style={styles.clockInButton} onPress={handleClockIn}>
                <Icon name="login" size={20} color="#fff" />
                <Text style={styles.buttonText}>Clock In</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity 
              style={styles.reportButton}
              onPress={() => navigation.navigate('Reports', { shift: currentShift })}
            >
              <Icon name="assignment" size={20} color="#2196F3" />
              <Text style={styles.reportButtonText}>Create Report</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.noShiftCard}>
          <Icon name="schedule" size={48} color="#9E9E9E" />
          <Text style={styles.noShiftText}>No active shift</Text>
          <Text style={styles.noShiftSubtext}>Check your upcoming shifts below</Text>
        </View>
      )}

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats?.totalShifts || 0}</Text>
          <Text style={styles.statLabel}>Total Shifts</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{Math.round(stats?.hoursWorked || 0)}</Text>
          <Text style={styles.statLabel}>Hours Worked</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats?.reportsSubmitted || 0}</Text>
          <Text style={styles.statLabel}>Reports</Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsGrid}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Shifts')}
          >
            <Icon name="schedule" size={32} color="#2196F3" />
            <Text style={styles.actionText}>My Shifts</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Reports')}
          >
            <Icon name="assignment" size={32} color="#4CAF50" />
            <Text style={styles.actionText}>Reports</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Location')}
          >
            <Icon name="location-on" size={32} color="#FF9800" />
            <Text style={styles.actionText}>Location</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Profile')}
          >
            <Icon name="person" size={32} color="#9C27B0" />
            <Text style={styles.actionText}>Profile</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Upcoming Shifts */}
      {upcomingShifts?.length > 0 && (
        <View style={styles.upcomingShifts}>
          <Text style={styles.sectionTitle}>Upcoming Shifts</Text>
          {upcomingShifts.map((shift) => (
            <View key={shift.id} style={styles.upcomingShiftCard}>
              <View style={styles.shiftInfo}>
                <Text style={styles.upcomingShiftSite}>{shift.site?.name}</Text>
                <Text style={styles.upcomingShiftDate}>
                  {formatDate(shift.shiftDate)} • {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
                </Text>
              </View>
              <Icon name="chevron-right" size={24} color="#9E9E9E" />
            </View>
          ))}
        </View>
      )}

      {/* Recent Reports */}
      {recentReports?.length > 0 && (
        <View style={styles.recentReports}>
          <Text style={styles.sectionTitle}>Recent Reports</Text>
          {recentReports.slice(0, 3).map((report) => (
            <View key={report.id} style={styles.reportCard}>
              <View style={styles.reportInfo}>
                <Text style={styles.reportTitle}>{report.title}</Text>
                <Text style={styles.reportDate}>{formatDate(report.createdAt)}</Text>
              </View>
              <View style={[styles.reportStatus, { 
                backgroundColor: report.status === 'APPROVED' ? '#4CAF50' : 
                               report.status === 'SUBMITTED' ? '#FF9800' : '#9E9E9E' 
              }]}>
                <Text style={styles.reportStatusText}>{report.status}</Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
  },
  nameText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  employeeIdText: {
    fontSize: 14,
    color: '#999',
  },
  locationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  locationText: {
    fontSize: 12,
    color: '#666',
  },
  currentShiftCard: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333',
  },
  shiftDetails: {
    marginBottom: 16,
  },
  siteName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  siteAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  shiftTime: {
    fontSize: 14,
    color: '#2196F3',
    marginTop: 8,
  },
  shiftActions: {
    flexDirection: 'row',
    gap: 12,
  },
  clockInButton: {
    flex: 1,
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  clockOutButton: {
    flex: 1,
    backgroundColor: '#F44336',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  reportButton: {
    flex: 1,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  reportButtonText: {
    color: '#2196F3',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  noShiftCard: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  noShiftText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 12,
  },
  noShiftSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    elevation: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  quickActions: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#fff',
    width: (width - 44) / 2,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
  },
  actionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  upcomingShifts: {
    margin: 16,
  },
  upcomingShiftCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 1,
  },
  shiftInfo: {
    flex: 1,
  },
  upcomingShiftSite: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  upcomingShiftDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  recentReports: {
    margin: 16,
  },
  reportCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 1,
  },
  reportInfo: {
    flex: 1,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  reportDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  reportStatus: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  reportStatusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default DashboardScreen;
