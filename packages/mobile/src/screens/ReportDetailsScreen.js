// BahinLink Mobile Report Details Screen
// ⚠️ CRITICAL: Real report viewing and editing with media ONLY

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Image,
  Modal,
  Dimensions
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { ApiService } from '../services/ApiService';
import { format } from 'date-fns';

const { width } = Dimensions.get('window');

const ReportDetailsScreen = () => {
  const [report, setReport] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageModalVisible, setImageModalVisible] = useState(false);

  const { user } = useAuth();
  const navigation = useNavigation();
  const route = useRoute();
  const { reportId } = route.params;

  useEffect(() => {
    loadReportDetails();
  }, [reportId]);

  const loadReportDetails = async () => {
    try {
      setIsLoading(true);
      const response = await ApiService.get(`/reports/${reportId}`);
      
      if (response.success) {
        setReport(response.data);
      } else {
        Alert.alert('Error', 'Failed to load report details');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error loading report details:', error);
      Alert.alert('Error', 'Failed to load report details');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = () => {
    if (report.status === 'DRAFT' || report.status === 'REJECTED') {
      navigation.navigate('EditReport', { reportId: report.id });
    } else {
      Alert.alert(
        'Cannot Edit',
        'This report cannot be edited because it has been submitted or approved.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleSubmit = async () => {
    if (report.status !== 'DRAFT') {
      Alert.alert('Error', 'Only draft reports can be submitted');
      return;
    }

    Alert.alert(
      'Submit Report',
      'Are you sure you want to submit this report? You will not be able to edit it after submission.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Submit',
          onPress: async () => {
            try {
              const response = await ApiService.put(`/reports/${reportId}`, {
                status: 'SUBMITTED',
                submittedAt: new Date().toISOString()
              });
              
              if (response.success) {
                Alert.alert('Success', 'Report submitted successfully');
                loadReportDetails();
              }
            } catch (error) {
              console.error('Error submitting report:', error);
              Alert.alert('Error', 'Failed to submit report');
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'DRAFT': return '#6C757D';
      case 'SUBMITTED': return '#FFC107';
      case 'APPROVED': return '#28A745';
      case 'REJECTED': return '#DC3545';
      default: return '#6C757D';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'LOW': return '#28A745';
      case 'MEDIUM': return '#FFC107';
      case 'HIGH': return '#FF6B35';
      case 'URGENT': return '#DC3545';
      default: return '#6C757D';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'INCIDENT': return 'warning';
      case 'MAINTENANCE': return 'construct';
      case 'SECURITY': return 'shield';
      case 'SAFETY': return 'medical';
      case 'GENERAL': return 'document-text';
      default: return 'document';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  const openImageModal = (imageUrl) => {
    setSelectedImage(imageUrl);
    setImageModalVisible(true);
  };

  const renderAttachment = (attachment) => {
    const isImage = attachment.fileType?.startsWith('image/');
    const isVideo = attachment.fileType?.startsWith('video/');

    return (
      <TouchableOpacity
        key={attachment.id}
        style={styles.attachmentItem}
        onPress={() => {
          if (isImage) {
            openImageModal(attachment.url);
          } else {
            // Handle other file types
            Alert.alert('File', `Open ${attachment.fileName}?`);
          }
        }}
      >
        {isImage ? (
          <Image source={{ uri: attachment.url }} style={styles.attachmentImage} />
        ) : (
          <View style={[styles.attachmentPlaceholder, { backgroundColor: isVideo ? '#FF6B35' : '#007AFF' }]}>
            <Ionicons 
              name={isVideo ? 'videocam' : 'document'} 
              size={24} 
              color="#FFFFFF" 
            />
          </View>
        )}
        <Text style={styles.attachmentName} numberOfLines={2}>
          {attachment.fileName}
        </Text>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading report...</Text>
      </View>
    );
  }

  if (!report) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="document-outline" size={64} color="#CCC" />
        <Text style={styles.errorText}>Report not found</Text>
      </View>
    );
  }

  const canEdit = report.status === 'DRAFT' || report.status === 'REJECTED';
  const canSubmit = report.status === 'DRAFT';
  const isOwnReport = report.agent?.user?.id === user?.id;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Report Details</Text>
        
        {isOwnReport && canEdit && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={handleEdit}
          >
            <Ionicons name="create" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content}>
        {/* Status and Priority */}
        <View style={styles.statusSection}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(report.status) }]}>
            <Text style={styles.statusText}>{report.status}</Text>
          </View>
          <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(report.priority) }]}>
            <Text style={styles.priorityText}>{report.priority}</Text>
          </View>
        </View>

        {/* Report Header */}
        <View style={styles.reportHeader}>
          <View style={styles.typeContainer}>
            <Ionicons 
              name={getTypeIcon(report.type)} 
              size={24} 
              color="#007AFF" 
              style={styles.typeIcon}
            />
            <Text style={styles.reportType}>{report.type}</Text>
          </View>
          <Text style={styles.reportTitle}>{report.title}</Text>
        </View>

        {/* Report Info */}
        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Ionicons name="person" size={16} color="#666" />
            <Text style={styles.infoLabel}>Agent:</Text>
            <Text style={styles.infoValue}>
              {report.agent?.user ? 
                `${report.agent.user.firstName} ${report.agent.user.lastName}` : 
                'Unknown'
              }
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="location" size={16} color="#666" />
            <Text style={styles.infoLabel}>Site:</Text>
            <Text style={styles.infoValue}>{report.site?.name || 'Unknown Site'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="business" size={16} color="#666" />
            <Text style={styles.infoLabel}>Client:</Text>
            <Text style={styles.infoValue}>{report.site?.client?.companyName || 'Unknown Client'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="calendar" size={16} color="#666" />
            <Text style={styles.infoLabel}>Created:</Text>
            <Text style={styles.infoValue}>{formatDate(report.createdAt)}</Text>
          </View>
          
          {report.submittedAt && (
            <View style={styles.infoRow}>
              <Ionicons name="send" size={16} color="#666" />
              <Text style={styles.infoLabel}>Submitted:</Text>
              <Text style={styles.infoValue}>{formatDate(report.submittedAt)}</Text>
            </View>
          )}
          
          {report.reviewedAt && (
            <View style={styles.infoRow}>
              <Ionicons name="checkmark-circle" size={16} color="#666" />
              <Text style={styles.infoLabel}>Reviewed:</Text>
              <Text style={styles.infoValue}>{formatDate(report.reviewedAt)}</Text>
            </View>
          )}
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{report.description}</Text>
        </View>

        {/* Location */}
        {report.latitude && report.longitude && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location</Text>
            <View style={styles.locationContainer}>
              <Ionicons name="location" size={20} color="#007AFF" />
              <Text style={styles.locationText}>
                {report.latitude.toFixed(6)}, {report.longitude.toFixed(6)}
              </Text>
            </View>
          </View>
        )}

        {/* Attachments */}
        {report.attachments && report.attachments.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Attachments ({report.attachments.length})
            </Text>
            <View style={styles.attachmentsGrid}>
              {report.attachments.map(renderAttachment)}
            </View>
          </View>
        )}

        {/* Review Notes */}
        {report.reviewNotes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Review Notes</Text>
            <Text style={styles.reviewNotes}>{report.reviewNotes}</Text>
          </View>
        )}

        {/* Quality Score */}
        {report.qualityScore && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quality Score</Text>
            <View style={styles.qualityContainer}>
              <Text style={styles.qualityScore}>{report.qualityScore}/5</Text>
              <View style={styles.starsContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Ionicons
                    key={star}
                    name={star <= report.qualityScore ? "star" : "star-outline"}
                    size={20}
                    color="#FFC107"
                  />
                ))}
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Action Buttons */}
      {isOwnReport && (canEdit || canSubmit) && (
        <View style={styles.actionButtons}>
          {canEdit && (
            <TouchableOpacity
              style={[styles.actionButton, styles.editActionButton]}
              onPress={handleEdit}
            >
              <Ionicons name="create" size={20} color="#007AFF" />
              <Text style={[styles.actionButtonText, { color: '#007AFF' }]}>Edit</Text>
            </TouchableOpacity>
          )}
          
          {canSubmit && (
            <TouchableOpacity
              style={[styles.actionButton, styles.submitActionButton]}
              onPress={handleSubmit}
            >
              <Ionicons name="send" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Submit</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Image Modal */}
      <Modal
        visible={imageModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImageModalVisible(false)}
      >
        <View style={styles.imageModalContainer}>
          <TouchableOpacity
            style={styles.imageModalOverlay}
            onPress={() => setImageModalVisible(false)}
          >
            <View style={styles.imageModalContent}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setImageModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              
              {selectedImage && (
                <Image
                  source={{ uri: selectedImage }}
                  style={styles.fullScreenImage}
                  resizeMode="contain"
                />
              )}
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  statusSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  priorityBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  reportHeader: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeIcon: {
    marginRight: 8,
  },
  reportType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
  },
  reportTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A1A1A',
    lineHeight: 32,
  },
  infoSection: {
    backgroundColor: '#FFFFFF',
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    marginRight: 8,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 14,
    color: '#1A1A1A',
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#1A1A1A',
    lineHeight: 24,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  attachmentsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  attachmentItem: {
    width: (width - 56) / 3,
    margin: 4,
    alignItems: 'center',
  },
  attachmentImage: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 8,
    backgroundColor: '#F1F3F4',
  },
  attachmentPlaceholder: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  attachmentName: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
  reviewNotes: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  qualityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  qualityScore: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginRight: 12,
  },
  starsContainer: {
    flexDirection: 'row',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E1E5E9',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  editActionButton: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  submitActionButton: {
    backgroundColor: '#007AFF',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  imageModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  imageModalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageModalContent: {
    flex: 1,
    width: '100%',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    flex: 1,
    width: '100%',
  },
});

export default ReportDetailsScreen;
