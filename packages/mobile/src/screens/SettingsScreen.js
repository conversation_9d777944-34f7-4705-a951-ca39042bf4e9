// BahinLink Mobile Settings Screen
// ⚠️ CRITICAL: Real app settings with preferences management ONLY

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
  Modal,
  TextInput,
  ActivityIndicator
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import OfflineService from '../services/OfflineService';
import NotificationService from '../services/NotificationService';

const SettingsScreen = () => {
  const [settings, setSettings] = useState({
    pushNotifications: true,
    locationTracking: true,
    autoClockOut: false,
    emergencyAlerts: true,
    soundEnabled: true,
    vibrationEnabled: true,
    darkMode: false,
    autoSync: true,
    dataUsage: 'wifi_only', // wifi_only, always, never
    cacheSize: 'medium' // small, medium, large
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [storageInfo, setStorageInfo] = useState(null);
  const [aboutModalVisible, setAboutModalVisible] = useState(false);
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');

  const { signOut, user } = useAuth();
  const navigation = useNavigation();

  useEffect(() => {
    loadSettings();
    loadStorageInfo();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const savedSettings = await AsyncStorage.getItem('app_settings');
      if (savedSettings) {
        setSettings({ ...settings, ...JSON.parse(savedSettings) });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('app_settings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  const loadStorageInfo = async () => {
    try {
      const info = await OfflineService.getOfflineStorageUsage();
      setStorageInfo(info);
    } catch (error) {
      console.error('Error loading storage info:', error);
    }
  };

  const handleSettingChange = async (key, value) => {
    const newSettings = { ...settings, [key]: value };
    
    // Handle special settings that require permissions or system changes
    if (key === 'pushNotifications') {
      if (value) {
        const { status } = await Notifications.requestPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please enable notifications in your device settings',
            [{ text: 'OK' }]
          );
          return;
        }
      }
    }
    
    if (key === 'locationTracking') {
      if (value) {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please enable location access in your device settings',
            [{ text: 'OK' }]
          );
          return;
        }
      }
    }
    
    await saveSettings(newSettings);
  };

  const clearCache = async () => {
    Alert.alert(
      'Clear Cache',
      'This will remove all cached data. You may need to re-download some information when you go online.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await OfflineService.clearOfflineData();
              await loadStorageInfo();
              Alert.alert('Success', 'Cache cleared successfully');
            } catch (error) {
              console.error('Error clearing cache:', error);
              Alert.alert('Error', 'Failed to clear cache');
            }
          }
        }
      ]
    );
  };

  const exportData = async () => {
    Alert.alert(
      'Export Data',
      'This feature will be available in a future update.',
      [{ text: 'OK' }]
    );
  };

  const sendFeedback = async () => {
    if (!feedbackText.trim()) {
      Alert.alert('Error', 'Please enter your feedback');
      return;
    }

    try {
      // In a real app, this would send feedback to your backend
      console.log('Feedback:', feedbackText);
      
      Alert.alert(
        'Thank You!',
        'Your feedback has been sent. We appreciate your input!',
        [{ text: 'OK' }]
      );
      
      setFeedbackText('');
      setFeedbackModalVisible(false);
    } catch (error) {
      console.error('Error sending feedback:', error);
      Alert.alert('Error', 'Failed to send feedback');
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              navigation.replace('Login');
            } catch (error) {
              console.error('Sign out error:', error);
              Alert.alert('Error', 'Failed to sign out');
            }
          }
        }
      ]
    );
  };

  const SettingItem = ({ 
    title, 
    subtitle, 
    value, 
    onValueChange, 
    type = 'switch', 
    icon,
    onPress,
    showChevron = false 
  }) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={type === 'switch'}
    >
      <View style={styles.settingContent}>
        {icon && (
          <Ionicons name={icon} size={24} color="#007AFF" style={styles.settingIcon} />
        )}
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && (
            <Text style={styles.settingSubtitle}>{subtitle}</Text>
          )}
        </View>
      </View>
      
      {type === 'switch' ? (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#E1E5E9', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      ) : showChevron ? (
        <Ionicons name="chevron-forward" size={16} color="#CCC" />
      ) : null}
    </TouchableOpacity>
  );

  const SectionHeader = ({ title }) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      {/* Notifications Section */}
      <SectionHeader title="Notifications" />
      <View style={styles.section}>
        <SettingItem
          title="Push Notifications"
          subtitle="Receive notifications for important updates"
          value={settings.pushNotifications}
          onValueChange={(value) => handleSettingChange('pushNotifications', value)}
          icon="notifications-outline"
        />
        <SettingItem
          title="Emergency Alerts"
          subtitle="Critical security alerts and emergencies"
          value={settings.emergencyAlerts}
          onValueChange={(value) => handleSettingChange('emergencyAlerts', value)}
          icon="alert-circle-outline"
        />
        <SettingItem
          title="Sound"
          subtitle="Play sound for notifications"
          value={settings.soundEnabled}
          onValueChange={(value) => handleSettingChange('soundEnabled', value)}
          icon="volume-high-outline"
        />
        <SettingItem
          title="Vibration"
          subtitle="Vibrate for notifications"
          value={settings.vibrationEnabled}
          onValueChange={(value) => handleSettingChange('vibrationEnabled', value)}
          icon="phone-portrait-outline"
        />
      </View>

      {/* Location & Tracking Section */}
      <SectionHeader title="Location & Tracking" />
      <View style={styles.section}>
        <SettingItem
          title="Location Tracking"
          subtitle="Required for GPS time tracking and geofencing"
          value={settings.locationTracking}
          onValueChange={(value) => handleSettingChange('locationTracking', value)}
          icon="location-outline"
        />
        <SettingItem
          title="Auto Clock-Out"
          subtitle="Automatically clock out when leaving site"
          value={settings.autoClockOut}
          onValueChange={(value) => handleSettingChange('autoClockOut', value)}
          icon="time-outline"
        />
      </View>

      {/* Data & Storage Section */}
      <SectionHeader title="Data & Storage" />
      <View style={styles.section}>
        <SettingItem
          title="Auto Sync"
          subtitle="Automatically sync data when online"
          value={settings.autoSync}
          onValueChange={(value) => handleSettingChange('autoSync', value)}
          icon="sync-outline"
        />
        <SettingItem
          title="Storage Usage"
          subtitle={storageInfo ? `${storageInfo.totalSizeMB} MB used (${storageInfo.itemCount} items)` : 'Calculating...'}
          type="info"
          icon="folder-outline"
          onPress={loadStorageInfo}
          showChevron
        />
        <SettingItem
          title="Clear Cache"
          subtitle="Remove cached data to free up space"
          type="action"
          icon="trash-outline"
          onPress={clearCache}
          showChevron
        />
      </View>

      {/* Account Section */}
      <SectionHeader title="Account" />
      <View style={styles.section}>
        <SettingItem
          title="Profile"
          subtitle="Manage your profile information"
          type="action"
          icon="person-outline"
          onPress={() => navigation.navigate('Profile')}
          showChevron
        />
        <SettingItem
          title="Export Data"
          subtitle="Download your data"
          type="action"
          icon="download-outline"
          onPress={exportData}
          showChevron
        />
      </View>

      {/* Support Section */}
      <SectionHeader title="Support" />
      <View style={styles.section}>
        <SettingItem
          title="Help & FAQ"
          subtitle="Get help and find answers"
          type="action"
          icon="help-circle-outline"
          onPress={() => navigation.navigate('Help')}
          showChevron
        />
        <SettingItem
          title="Send Feedback"
          subtitle="Help us improve the app"
          type="action"
          icon="chatbubble-outline"
          onPress={() => setFeedbackModalVisible(true)}
          showChevron
        />
        <SettingItem
          title="About"
          subtitle="App version and information"
          type="action"
          icon="information-circle-outline"
          onPress={() => setAboutModalVisible(true)}
          showChevron
        />
      </View>

      {/* Sign Out */}
      <View style={styles.section}>
        <SettingItem
          title="Sign Out"
          subtitle="Sign out of your account"
          type="action"
          icon="log-out-outline"
          onPress={handleSignOut}
        />
      </View>

      {/* About Modal */}
      <Modal
        visible={aboutModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setAboutModalVisible(false)}>
              <Text style={styles.modalCloseButton}>Done</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>About BahinLink</Text>
            <View style={{ width: 50 }} />
          </View>
          
          <View style={styles.modalContent}>
            <Text style={styles.appName}>BahinLink Mobile</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appDescription}>
              Professional workforce management for security companies. 
              Real-time GPS tracking, time management, and reporting.
            </Text>
            
            <View style={styles.aboutSection}>
              <Text style={styles.aboutSectionTitle}>Developer</Text>
              <Text style={styles.aboutSectionText}>Bahin SARL</Text>
            </View>
            
            <View style={styles.aboutSection}>
              <Text style={styles.aboutSectionTitle}>Contact</Text>
              <Text style={styles.aboutSectionText}><EMAIL></Text>
            </View>
          </View>
        </View>
      </Modal>

      {/* Feedback Modal */}
      <Modal
        visible={feedbackModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setFeedbackModalVisible(false)}>
              <Text style={styles.modalCloseButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Send Feedback</Text>
            <TouchableOpacity onPress={sendFeedback}>
              <Text style={styles.modalSendButton}>Send</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            <Text style={styles.feedbackLabel}>
              Tell us what you think about the app:
            </Text>
            <TextInput
              style={styles.feedbackInput}
              multiline
              numberOfLines={6}
              placeholder="Your feedback..."
              value={feedbackText}
              onChangeText={setFeedbackText}
              textAlignVertical="top"
            />
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A1A1A',
  },
  sectionHeader: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1A1A1A',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  modalCloseButton: {
    fontSize: 16,
    color: '#007AFF',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  modalSendButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  appDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  aboutSection: {
    marginBottom: 24,
  },
  aboutSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 8,
  },
  aboutSectionText: {
    fontSize: 16,
    color: '#666',
  },
  feedbackLabel: {
    fontSize: 16,
    color: '#1A1A1A',
    marginBottom: 16,
  },
  feedbackInput: {
    borderWidth: 1,
    borderColor: '#E1E5E9',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1A1A1A',
    minHeight: 120,
  },
});

export default SettingsScreen;
