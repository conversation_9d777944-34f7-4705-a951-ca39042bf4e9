// BahinLink Mobile Emergency Screen
// ⚠️ CRITICAL: Real emergency alert system with GPS location ONLY

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
  Vibration,
  Linking,
  ActivityIndicator
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LocationService } from '../services/LocationService';
import { ApiService } from '../services/ApiService';
import NotificationService from '../services/NotificationService';

const EmergencyScreen = () => {
  const [isEmergencyActive, setIsEmergencyActive] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [emergencyContacts, setEmergencyContacts] = useState([
    { id: 1, name: 'Emergency Services', number: '911', type: 'emergency' },
    { id: 2, name: 'Security Control', number: '+1-555-SECURITY', type: 'security' },
    { id: 3, name: 'Supervisor', number: '+1-555-SUPERVISOR', type: 'supervisor' }
  ]);

  const pulseAnim = useRef(new Animated.Value(1)).current;
  const countdownTimer = useRef(null);
  const { user } = useAuth();
  const navigation = useNavigation();

  useEffect(() => {
    getCurrentLocation();
    
    // Start pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => {
      pulseAnimation.stop();
      if (countdownTimer.current) {
        clearInterval(countdownTimer.current);
      }
    };
  }, []);

  const getCurrentLocation = async () => {
    try {
      setIsLoadingLocation(true);
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert(
        'Location Error',
        'Unable to get your current location. Emergency services may not be able to locate you precisely.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const startEmergencyCountdown = () => {
    setCountdown(10); // 10 second countdown
    setIsEmergencyActive(true);
    
    // Start vibration pattern
    const vibrationPattern = [0, 500, 200, 500, 200, 500];
    Vibration.vibrate(vibrationPattern, true);

    countdownTimer.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          triggerEmergencyAlert();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const cancelEmergency = () => {
    if (countdownTimer.current) {
      clearInterval(countdownTimer.current);
    }
    
    Vibration.cancel();
    setIsEmergencyActive(false);
    setCountdown(0);
    
    Alert.alert(
      'Emergency Cancelled',
      'Emergency alert has been cancelled.',
      [{ text: 'OK' }]
    );
  };

  const triggerEmergencyAlert = async () => {
    try {
      Vibration.cancel();
      setIsEmergencyActive(false);
      
      // Send emergency alert to backend
      const emergencyData = {
        type: 'PANIC_BUTTON',
        location: currentLocation ? {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          accuracy: currentLocation.accuracy,
          timestamp: new Date().toISOString()
        } : null,
        userInfo: {
          id: user?.id,
          name: `${user?.firstName} ${user?.lastName}`,
          email: user?.emailAddresses?.[0]?.emailAddress
        },
        timestamp: new Date().toISOString(),
        deviceInfo: {
          platform: 'mobile',
          userAgent: 'BahinLink Mobile App'
        }
      };

      const response = await ApiService.post('/emergency/alert', emergencyData);
      
      if (response.success) {
        // Send local notification
        await NotificationService.sendLocalNotification(
          'Emergency Alert Sent',
          'Your emergency alert has been sent to security control and supervisors.',
          { type: 'EMERGENCY_SENT' },
          { priority: 'high' }
        );

        Alert.alert(
          'Emergency Alert Sent',
          'Your emergency alert has been sent to security control and supervisors. Help is on the way.',
          [
            {
              text: 'Call 911',
              onPress: () => makeEmergencyCall('911')
            },
            { text: 'OK' }
          ]
        );
      } else {
        throw new Error('Failed to send emergency alert');
      }
    } catch (error) {
      console.error('Emergency alert error:', error);
      
      Alert.alert(
        'Alert Failed',
        'Failed to send emergency alert. Please call emergency services directly.',
        [
          {
            text: 'Call 911',
            onPress: () => makeEmergencyCall('911')
          },
          { text: 'Retry', onPress: triggerEmergencyAlert },
          { text: 'Cancel' }
        ]
      );
    }
  };

  const makeEmergencyCall = (number) => {
    const phoneNumber = `tel:${number}`;
    
    Alert.alert(
      'Emergency Call',
      `Call ${number}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => {
            Linking.openURL(phoneNumber).catch(err => {
              console.error('Error making call:', err);
              Alert.alert('Error', 'Unable to make phone call');
            });
          }
        }
      ]
    );
  };

  const sendQuickMessage = (type) => {
    const messages = {
      'need_help': 'I need immediate assistance at my current location.',
      'medical': 'Medical emergency - need immediate medical assistance.',
      'security': 'Security incident in progress - need backup immediately.',
      'fire': 'Fire emergency - evacuating area immediately.'
    };

    Alert.alert(
      'Send Quick Message',
      `Send: "${messages[type]}"`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: async () => {
            try {
              await ApiService.post('/emergency/quick-message', {
                message: messages[type],
                type,
                location: currentLocation,
                timestamp: new Date().toISOString()
              });
              
              Alert.alert('Message Sent', 'Your emergency message has been sent.');
            } catch (error) {
              console.error('Quick message error:', error);
              Alert.alert('Error', 'Failed to send message');
            }
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Emergency</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Location Status */}
      <View style={styles.locationStatus}>
        <View style={styles.locationInfo}>
          <Ionicons 
            name={currentLocation ? "location" : "location-outline"} 
            size={20} 
            color={currentLocation ? "#28A745" : "#DC3545"} 
          />
          <Text style={styles.locationText}>
            {isLoadingLocation 
              ? 'Getting location...' 
              : currentLocation 
                ? 'Location available' 
                : 'Location unavailable'
            }
          </Text>
        </View>
        {isLoadingLocation && (
          <ActivityIndicator size="small" color="#007AFF" />
        )}
      </View>

      {/* Emergency Button */}
      <View style={styles.emergencySection}>
        {isEmergencyActive ? (
          <View style={styles.countdownContainer}>
            <Text style={styles.countdownTitle}>Emergency Alert in</Text>
            <Text style={styles.countdownNumber}>{countdown}</Text>
            <Text style={styles.countdownSubtitle}>seconds</Text>
            
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={cancelEmergency}
            >
              <Text style={styles.cancelButtonText}>CANCEL</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.panicButtonContainer}>
            <Text style={styles.emergencyTitle}>Emergency Alert</Text>
            <Text style={styles.emergencySubtitle}>
              Press and hold to send emergency alert
            </Text>
            
            <Animated.View style={[
              styles.panicButtonWrapper,
              { transform: [{ scale: pulseAnim }] }
            ]}>
              <TouchableOpacity
                style={styles.panicButton}
                onLongPress={startEmergencyCountdown}
                delayLongPress={1000}
              >
                <Ionicons name="warning" size={60} color="#FFFFFF" />
                <Text style={styles.panicButtonText}>EMERGENCY</Text>
              </TouchableOpacity>
            </Animated.View>
            
            <Text style={styles.instructionText}>
              Hold for 1 second to activate
            </Text>
          </View>
        )}
      </View>

      {/* Quick Actions */}
      {!isEmergencyActive && (
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Messages</Text>
          
          <View style={styles.quickButtonsGrid}>
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: '#DC3545' }]}
              onPress={() => sendQuickMessage('need_help')}
            >
              <Ionicons name="help-circle" size={24} color="#FFFFFF" />
              <Text style={styles.quickButtonText}>Need Help</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: '#FF6B6B' }]}
              onPress={() => sendQuickMessage('medical')}
            >
              <Ionicons name="medical" size={24} color="#FFFFFF" />
              <Text style={styles.quickButtonText}>Medical</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: '#FF9500' }]}
              onPress={() => sendQuickMessage('security')}
            >
              <Ionicons name="shield" size={24} color="#FFFFFF" />
              <Text style={styles.quickButtonText}>Security</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: '#FF4757' }]}
              onPress={() => sendQuickMessage('fire')}
            >
              <Ionicons name="flame" size={24} color="#FFFFFF" />
              <Text style={styles.quickButtonText}>Fire</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Emergency Contacts */}
      {!isEmergencyActive && (
        <View style={styles.contactsSection}>
          <Text style={styles.sectionTitle}>Emergency Contacts</Text>
          
          {emergencyContacts.map((contact) => (
            <TouchableOpacity
              key={contact.id}
              style={styles.contactItem}
              onPress={() => makeEmergencyCall(contact.number)}
            >
              <View style={styles.contactInfo}>
                <Ionicons 
                  name={contact.type === 'emergency' ? 'call' : 'person'} 
                  size={24} 
                  color="#007AFF" 
                />
                <View style={styles.contactText}>
                  <Text style={styles.contactName}>{contact.name}</Text>
                  <Text style={styles.contactNumber}>{contact.number}</Text>
                </View>
              </View>
              <Ionicons name="call" size={20} color="#28A745" />
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A1A',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: '#DC3545',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  locationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#FFFFFF',
  },
  emergencySection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  countdownContainer: {
    alignItems: 'center',
  },
  countdownTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 20,
  },
  countdownNumber: {
    fontSize: 120,
    fontWeight: 'bold',
    color: '#DC3545',
    marginBottom: 20,
  },
  countdownSubtitle: {
    fontSize: 18,
    color: '#FFFFFF',
    marginBottom: 40,
  },
  cancelButton: {
    backgroundColor: '#28A745',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 30,
  },
  cancelButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  panicButtonContainer: {
    alignItems: 'center',
  },
  emergencyTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  emergencySubtitle: {
    fontSize: 16,
    color: '#CCC',
    marginBottom: 40,
    textAlign: 'center',
  },
  panicButtonWrapper: {
    marginBottom: 20,
  },
  panicButton: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: '#DC3545',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#DC3545',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
  },
  panicButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 8,
  },
  instructionText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  quickActions: {
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  quickButtonsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickButton: {
    width: '48%',
    aspectRatio: 1.5,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 4,
    textAlign: 'center',
  },
  contactsSection: {
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#3A3A3A',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  contactText: {
    marginLeft: 12,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  contactNumber: {
    fontSize: 14,
    color: '#CCC',
  },
});

export default EmergencyScreen;
