// BahinLink Time Tracking Screen
// ⚠️ CRITICAL: Real GPS-based time tracking with QR code verification ONLY

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  ScrollView,
  RefreshControl
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Text,
  Chip,
  ActivityIndicator,
  Surface
} from 'react-native-paper';
import { useAuth } from '@clerk/clerk-react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import ApiService from '../services/ApiService';
import LocationService from '../services/LocationService';
import { formatTime, formatDuration, calculateHours } from '@bahinlink/shared';

const TimeTrackingScreen = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentShift, setCurrentShift] = useState(null);
  const [timeEntry, setTimeEntry] = useState(null);
  const [location, setLocation] = useState(null);
  const [clockingIn, setClockingin] = useState(false);
  const [clockingOut, setClockingOut] = useState(false);

  useEffect(() => {
    loadTimeTrackingData();
    
    // Update location every 30 seconds
    const locationInterval = setInterval(() => {
      updateCurrentLocation();
    }, 30000);

    return () => clearInterval(locationInterval);
  }, []);

  const loadTimeTrackingData = async () => {
    try {
      setLoading(true);
      
      // Get current shift
      const shiftsResponse = await ApiService.getMyShifts({
        status: 'IN_PROGRESS',
        date: new Date().toISOString().split('T')[0]
      });

      if (shiftsResponse.success && shiftsResponse.data.length > 0) {
        const shift = shiftsResponse.data[0];
        setCurrentShift(shift);

        // Get current time entry
        const timeEntriesResponse = await ApiService.getTimeEntries({
          shiftId: shift.id
        });

        if (timeEntriesResponse.success && timeEntriesResponse.data.length > 0) {
          const entry = timeEntriesResponse.data.find(e => e.clockInTime && !e.clockOutTime);
          setTimeEntry(entry);
        }
      }

      await updateCurrentLocation();
    } catch (error) {
      console.error('Load time tracking data error:', error);
      Alert.alert('Error', 'Failed to load time tracking data');
    } finally {
      setLoading(false);
    }
  };

  const updateCurrentLocation = async () => {
    try {
      const currentLocation = await LocationService.getCurrentLocation();
      setLocation(currentLocation);
    } catch (error) {
      console.error('Update location error:', error);
    }
  };

  const handleClockIn = async () => {
    if (!currentShift) {
      Alert.alert('No Active Shift', 'You need an active shift to clock in.');
      return;
    }

    if (!location) {
      Alert.alert('Location Required', 'Please enable GPS to clock in.');
      return;
    }

    try {
      setClockingin(true);

      // Check if user wants to scan QR code
      Alert.alert(
        'Clock In Method',
        'How would you like to clock in?',
        [
          {
            text: 'GPS Only',
            onPress: () => performClockIn('GPS')
          },
          {
            text: 'Scan QR Code',
            onPress: () => navigation.navigate('QRScanner', {
              onQRScanned: (qrData) => performClockIn('QR_CODE', qrData)
            })
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('Clock in error:', error);
      Alert.alert('Error', 'Failed to initiate clock in');
    } finally {
      setClockingin(false);
    }
  };

  const performClockIn = async (method, qrCode = null) => {
    try {
      setClockingin(true);

      const clockInData = {
        shiftId: currentShift.id,
        location: {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy
        },
        method,
        qrCode
      };

      const response = await ApiService.clockIn(clockInData);

      if (response.success) {
        setTimeEntry({
          id: response.data.timeEntryId,
          shiftId: currentShift.id,
          clockInTime: response.data.clockInTime,
          clockInLatitude: location.latitude,
          clockInLongitude: location.longitude,
          clockInMethod: method
        });

        Alert.alert(
          'Clock In Successful',
          response.message || 'You have successfully clocked in.',
          [{ text: 'OK' }]
        );

        // Refresh data
        await loadTimeTrackingData();
      }
    } catch (error) {
      console.error('Perform clock in error:', error);
      Alert.alert(
        'Clock In Failed',
        error.error?.message || 'Failed to clock in. Please try again.'
      );
    } finally {
      setClockingin(false);
    }
  };

  const handleClockOut = async () => {
    if (!timeEntry) {
      Alert.alert('Not Clocked In', 'You are not currently clocked in.');
      return;
    }

    if (!location) {
      Alert.alert('Location Required', 'Please enable GPS to clock out.');
      return;
    }

    Alert.alert(
      'Confirm Clock Out',
      'Are you sure you want to clock out?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Clock Out',
          onPress: performClockOut
        }
      ]
    );
  };

  const performClockOut = async () => {
    try {
      setClockingOut(true);

      const clockOutData = {
        timeEntryId: timeEntry.id,
        location: {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy
        },
        method: 'GPS'
      };

      const response = await ApiService.clockOut(clockOutData);

      if (response.success) {
        setTimeEntry(null);

        Alert.alert(
          'Clock Out Successful',
          response.message || 'You have successfully clocked out.',
          [{ text: 'OK' }]
        );

        // Refresh data
        await loadTimeTrackingData();
      }
    } catch (error) {
      console.error('Perform clock out error:', error);
      Alert.alert(
        'Clock Out Failed',
        error.error?.message || 'Failed to clock out. Please try again.'
      );
    } finally {
      setClockingOut(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTimeTrackingData();
    setRefreshing(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'IN_PROGRESS': return '#4CAF50';
      case 'SCHEDULED': return '#FF9800';
      case 'COMPLETED': return '#2196F3';
      default: return '#757575';
    }
  };

  const calculateCurrentHours = () => {
    if (!timeEntry?.clockInTime) return 0;
    return calculateHours(new Date(timeEntry.clockInTime), new Date());
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading time tracking data...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Current Location Status */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Location Status</Title>
          {location ? (
            <View>
              <Paragraph>
                📍 {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
              </Paragraph>
              <Paragraph>
                🎯 Accuracy: ±{Math.round(location.accuracy)}m
              </Paragraph>
              <Paragraph>
                🕐 Updated: {formatTime(location.timestamp)}
              </Paragraph>
            </View>
          ) : (
            <Paragraph style={styles.errorText}>
              ⚠️ Location not available. Please enable GPS.
            </Paragraph>
          )}
        </Card.Content>
      </Card>

      {/* Current Shift */}
      {currentShift ? (
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.shiftHeader}>
              <Title>Current Shift</Title>
              <Chip 
                style={[styles.statusChip, { backgroundColor: getStatusColor(currentShift.status) }]}
                textStyle={styles.statusChipText}
              >
                {currentShift.status}
              </Chip>
            </View>
            
            <Paragraph>🏢 {currentShift.site?.name}</Paragraph>
            <Paragraph>📍 {currentShift.site?.address}</Paragraph>
            <Paragraph>
              🕐 {formatTime(currentShift.startTime)} - {formatTime(currentShift.endTime)}
            </Paragraph>
            
            {currentShift.site && location && (
              <Paragraph>
                📏 Distance: {Math.round(LocationService.calculateDistanceToSite({
                  latitude: currentShift.site.latitude,
                  longitude: currentShift.site.longitude
                }) || 0)}m from site
              </Paragraph>
            )}
          </Card.Content>
        </Card>
      ) : (
        <Card style={styles.card}>
          <Card.Content>
            <Title>No Active Shift</Title>
            <Paragraph>You don't have any active shifts today.</Paragraph>
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Shifts')}
              style={styles.button}
            >
              View My Shifts
            </Button>
          </Card.Content>
        </Card>
      )}

      {/* Time Tracking Controls */}
      {currentShift && (
        <Card style={styles.card}>
          <Card.Content>
            <Title>Time Tracking</Title>
            
            {timeEntry ? (
              <View>
                <Surface style={styles.timeDisplay}>
                  <Text style={styles.timeText}>
                    {formatDuration(calculateCurrentHours())}
                  </Text>
                  <Text style={styles.timeLabel}>Hours Worked</Text>
                </Surface>
                
                <Paragraph>
                  🕐 Clocked in: {formatTime(timeEntry.clockInTime)}
                </Paragraph>
                <Paragraph>
                  📍 Method: {timeEntry.clockInMethod}
                </Paragraph>
                
                <Button
                  mode="contained"
                  onPress={handleClockOut}
                  loading={clockingOut}
                  disabled={clockingOut}
                  style={[styles.button, styles.clockOutButton]}
                  icon="logout"
                >
                  Clock Out
                </Button>
              </View>
            ) : (
              <View>
                <Paragraph>You are not currently clocked in.</Paragraph>
                <Button
                  mode="contained"
                  onPress={handleClockIn}
                  loading={clockingIn}
                  disabled={clockingIn || !location}
                  style={[styles.button, styles.clockInButton]}
                  icon="login"
                >
                  Clock In
                </Button>
              </View>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Quick Actions */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Quick Actions</Title>
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Reports')}
              style={styles.actionButton}
              icon="description"
            >
              Create Report
            </Button>
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Location')}
              style={styles.actionButton}
              icon="location-on"
            >
              Location Details
            </Button>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16
  },
  card: {
    marginBottom: 16,
    elevation: 4
  },
  shiftHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  statusChip: {
    height: 32
  },
  statusChipText: {
    color: 'white',
    fontWeight: 'bold'
  },
  timeDisplay: {
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 16,
    backgroundColor: '#e3f2fd'
  },
  timeText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1976d2'
  },
  timeLabel: {
    fontSize: 16,
    color: '#666',
    marginTop: 4
  },
  button: {
    marginTop: 16
  },
  clockInButton: {
    backgroundColor: '#4CAF50'
  },
  clockOutButton: {
    backgroundColor: '#f44336'
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4
  },
  errorText: {
    color: '#f44336'
  }
});

export default TimeTrackingScreen;
