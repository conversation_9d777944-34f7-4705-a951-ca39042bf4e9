// BahinLink Mobile Shifts Screen
// ⚠️ CRITICAL: Real shift management with time tracking ONLY

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Modal
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { ApiService } from '../services/ApiService';
import { LocationService } from '../services/LocationService';

const ShiftsScreen = () => {
  const [shifts, setShifts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeShift, setActiveShift] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [showClockInModal, setShowClockInModal] = useState(false);
  const [selectedShift, setSelectedShift] = useState(null);

  const { isSignedIn } = useAuth();
  const navigation = useNavigation();

  useFocusEffect(
    useCallback(() => {
      if (isSignedIn) {
        loadShifts();
        getCurrentLocation();
      }
    }, [isSignedIn])
  );

  const loadShifts = async () => {
    try {
      setIsLoading(true);
      
      // Get shifts for current agent
      const response = await ApiService.get('/shifts', {
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        includeStats: true
      });

      if (response.success) {
        setShifts(response.data);
        
        // Find active shift
        const active = response.data.find(shift => 
          shift.status === 'IN_PROGRESS' || 
          (shift.status === 'SCHEDULED' && shift.isCurrentlyActive)
        );
        setActiveShift(active);
      }
    } catch (error) {
      console.error('Error loading shifts:', error);
      Alert.alert('Error', 'Failed to load shifts');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadShifts();
    await getCurrentLocation();
    setIsRefreshing(false);
  };

  const handleClockIn = async (shift) => {
    if (!currentLocation) {
      Alert.alert('Location Required', 'Please enable location services to clock in');
      return;
    }

    try {
      const response = await ApiService.post('/time/clock-in', {
        shiftId: shift.id,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        accuracy: currentLocation.accuracy
      });

      if (response.success) {
        Alert.alert('Success', 'Clocked in successfully');
        setActiveShift(shift);
        loadShifts();
      }
    } catch (error) {
      console.error('Clock in error:', error);
      
      if (error.response?.data?.error?.code === 'GEOFENCE_VIOLATION') {
        Alert.alert(
          'Location Warning',
          error.response.data.error.message + '\n\nDo you want to force clock in?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Force Clock In',
              onPress: () => forceClockIn(shift)
            }
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to clock in');
      }
    }
  };

  const forceClockIn = async (shift) => {
    try {
      const response = await ApiService.post('/time/clock-in', {
        shiftId: shift.id,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        accuracy: currentLocation.accuracy,
        forceClockIn: true
      });

      if (response.success) {
        Alert.alert('Success', 'Clocked in successfully');
        setActiveShift(shift);
        loadShifts();
      }
    } catch (error) {
      console.error('Force clock in error:', error);
      Alert.alert('Error', 'Failed to clock in');
    }
  };

  const handleClockOut = async () => {
    if (!activeShift || !currentLocation) {
      Alert.alert('Error', 'No active shift or location not available');
      return;
    }

    Alert.alert(
      'Clock Out',
      'Are you sure you want to clock out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clock Out',
          onPress: async () => {
            try {
              const response = await ApiService.post('/time/clock-out', {
                shiftId: activeShift.id,
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
                accuracy: currentLocation.accuracy
              });

              if (response.success) {
                Alert.alert('Success', 'Clocked out successfully');
                setActiveShift(null);
                loadShifts();
              }
            } catch (error) {
              console.error('Clock out error:', error);
              Alert.alert('Error', 'Failed to clock out');
            }
          }
        }
      ]
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getShiftStatusColor = (status) => {
    switch (status) {
      case 'SCHEDULED': return '#007AFF';
      case 'IN_PROGRESS': return '#28A745';
      case 'COMPLETED': return '#6C757D';
      case 'CANCELLED': return '#DC3545';
      default: return '#6C757D';
    }
  };

  const getShiftStatusIcon = (status) => {
    switch (status) {
      case 'SCHEDULED': return 'calendar-outline';
      case 'IN_PROGRESS': return 'play-circle';
      case 'COMPLETED': return 'checkmark-circle';
      case 'CANCELLED': return 'close-circle';
      default: return 'help-circle';
    }
  };

  const renderShiftItem = ({ item: shift }) => {
    const isToday = new Date(shift.shiftDate).toDateString() === new Date().toDateString();
    const canClockIn = shift.status === 'SCHEDULED' && isToday && !activeShift;
    const isActive = activeShift?.id === shift.id;

    return (
      <TouchableOpacity
        style={[styles.shiftCard, isActive && styles.activeShiftCard]}
        onPress={() => navigation.navigate('ShiftDetails', { shiftId: shift.id })}
      >
        <View style={styles.shiftHeader}>
          <View style={styles.shiftInfo}>
            <Text style={styles.siteName}>{shift.site.name}</Text>
            <Text style={styles.clientName}>{shift.site.clientName}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getShiftStatusColor(shift.status) }]}>
            <Ionicons 
              name={getShiftStatusIcon(shift.status)} 
              size={12} 
              color="#FFFFFF" 
            />
            <Text style={styles.statusText}>{shift.status}</Text>
          </View>
        </View>

        <View style={styles.shiftDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="calendar-outline" size={16} color="#666" />
            <Text style={styles.detailText}>{formatDate(shift.shiftDate)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.detailText}>
              {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="location-outline" size={16} color="#666" />
            <Text style={styles.detailText}>{shift.site.address}</Text>
          </View>
        </View>

        {shift.hoursWorked > 0 && (
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              Hours Worked: {shift.hoursWorked.toFixed(1)}h
            </Text>
            {shift.overtimeHours > 0 && (
              <Text style={styles.overtimeText}>
                Overtime: {shift.overtimeHours.toFixed(1)}h
              </Text>
            )}
          </View>
        )}

        {canClockIn && (
          <TouchableOpacity
            style={styles.clockInButton}
            onPress={() => handleClockIn(shift)}
          >
            <Ionicons name="play" size={16} color="#FFFFFF" />
            <Text style={styles.clockInButtonText}>Clock In</Text>
          </TouchableOpacity>
        )}

        {isActive && (
          <TouchableOpacity
            style={styles.clockOutButton}
            onPress={handleClockOut}
          >
            <Ionicons name="stop" size={16} color="#FFFFFF" />
            <Text style={styles.clockOutButtonText}>Clock Out</Text>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading shifts...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Active Shift Banner */}
      {activeShift && (
        <View style={styles.activeShiftBanner}>
          <View style={styles.bannerContent}>
            <Ionicons name="play-circle" size={24} color="#28A745" />
            <View style={styles.bannerText}>
              <Text style={styles.bannerTitle}>Active Shift</Text>
              <Text style={styles.bannerSubtitle}>{activeShift.site.name}</Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.quickClockOutButton}
            onPress={handleClockOut}
          >
            <Ionicons name="stop" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      )}

      {/* Shifts List */}
      <FlatList
        data={shifts}
        renderItem={renderShiftItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={64} color="#CCC" />
            <Text style={styles.emptyText}>No shifts scheduled</Text>
            <Text style={styles.emptySubtext}>
              Your upcoming shifts will appear here
            </Text>
          </View>
        }
      />

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('Reports')}
        >
          <Ionicons name="document-text-outline" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Report</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('QRScanner')}
        >
          <Ionicons name="qr-code-outline" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>QR Scan</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('Emergency')}
        >
          <Ionicons name="alert-circle-outline" size={24} color="#DC3545" />
          <Text style={[styles.quickActionText, { color: '#DC3545' }]}>Emergency</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  activeShiftBanner: {
    backgroundColor: '#E8F5E8',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#28A745',
  },
  bannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  bannerText: {
    marginLeft: 12,
  },
  bannerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#28A745',
  },
  bannerSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  quickClockOutButton: {
    backgroundColor: '#DC3545',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  shiftCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activeShiftCard: {
    borderWidth: 2,
    borderColor: '#28A745',
  },
  shiftHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  shiftInfo: {
    flex: 1,
  },
  siteName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  clientName: {
    fontSize: 14,
    color: '#666',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  shiftDetails: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  progressText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  overtimeText: {
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '500',
  },
  clockInButton: {
    backgroundColor: '#28A745',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  clockInButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  clockOutButton: {
    backgroundColor: '#DC3545',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  clockOutButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  quickActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: '#E1E5E9',
  },
  quickActionButton: {
    alignItems: 'center',
  },
  quickActionText: {
    marginTop: 4,
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default ShiftsScreen;
