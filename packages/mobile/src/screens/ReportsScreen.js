// BahinLink Mobile Reports Screen
// ⚠️ CRITICAL: Real report creation with photo/video attachments ONLY

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  Modal,
  FlatList
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';

import ApiService from '../services/ApiService';
import LocationService from '../services/LocationService';
import { formatTime, formatDate } from '@bahinlink/shared';

const REPORT_TYPES = [
  { value: 'PATROL', label: 'Patrol Report', icon: 'security' },
  { value: 'INCIDENT', label: 'Incident Report', icon: 'warning' },
  { value: 'MAINTENANCE', label: 'Maintenance Issue', icon: 'build' },
  { value: 'VISITOR', label: 'Visitor Log', icon: 'person' },
  { value: 'OTHER', label: 'Other', icon: 'description' }
];

const PRIORITY_LEVELS = [
  { value: 'LOW', label: 'Low', color: '#4CAF50' },
  { value: 'MEDIUM', label: 'Medium', color: '#FF9800' },
  { value: 'HIGH', label: 'High', color: '#F44336' }
];

const ReportsScreen = ({ navigation, route }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    type: 'PATROL',
    title: '',
    description: '',
    priority: 'MEDIUM',
    siteId: route?.params?.shift?.site?.id || '',
    shiftId: route?.params?.shift?.id || ''
  });
  
  const [attachments, setAttachments] = useState([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    loadReports();
    getCurrentLocation();
  }, []);

  const loadReports = async () => {
    try {
      setLoading(true);
      const response = await ApiService.get('/reports', {
        agentId: user?.id,
        limit: 20
      });
      
      if (response.success) {
        setReports(response.data);
      }
    } catch (error) {
      console.error('Load reports error:', error);
      Alert.alert('Error', 'Failed to load reports');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
    } catch (error) {
      console.error('Get location error:', error);
    }
  };

  const handleCreateReport = () => {
    setShowCreateModal(true);
    setFormData({
      type: 'PATROL',
      title: '',
      description: '',
      priority: 'MEDIUM',
      siteId: route?.params?.shift?.site?.id || '',
      shiftId: route?.params?.shift?.id || ''
    });
    setAttachments([]);
  };

  const handleSubmitReport = async () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      Alert.alert('Validation Error', 'Please fill in all required fields');
      return;
    }

    try {
      setUploading(true);

      // Upload attachments first
      const attachmentIds = [];
      for (const attachment of attachments) {
        const uploadResponse = await uploadAttachment(attachment);
        if (uploadResponse.success) {
          attachmentIds.push(uploadResponse.data.id);
        }
      }

      // Create report
      const reportData = {
        ...formData,
        latitude: currentLocation?.latitude,
        longitude: currentLocation?.longitude,
        attachmentIds
      };

      const response = await ApiService.post('/reports', reportData);
      
      if (response.success) {
        Alert.alert('Success', 'Report submitted successfully');
        setShowCreateModal(false);
        loadReports();
      } else {
        throw new Error(response.error?.message || 'Failed to create report');
      }
    } catch (error) {
      console.error('Submit report error:', error);
      Alert.alert('Error', error.message || 'Failed to submit report');
    } finally {
      setUploading(false);
    }
  };

  const uploadAttachment = async (attachment) => {
    try {
      const endpoint = attachment.type.startsWith('image/') ? '/upload/photo' : '/upload/video';
      
      const formData = new FormData();
      formData.append('file', {
        uri: attachment.uri,
        type: attachment.type,
        name: attachment.fileName || `attachment_${Date.now()}.${attachment.type.split('/')[1]}`
      });
      
      if (currentLocation) {
        formData.append('latitude', currentLocation.latitude.toString());
        formData.append('longitude', currentLocation.longitude.toString());
      }

      const response = await ApiService.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response;
    } catch (error) {
      console.error('Upload attachment error:', error);
      throw error;
    }
  };

  const handleAddPhoto = () => {
    Alert.alert(
      'Add Photo',
      'Choose an option',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() }
      ]
    );
  };

  const openCamera = () => {
    const options = {
      mediaType: 'photo',
      quality: 0.8,
      includeBase64: false
    };

    launchCamera(options, (response) => {
      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        setAttachments(prev => [...prev, {
          uri: asset.uri,
          type: asset.type,
          fileName: asset.fileName,
          fileSize: asset.fileSize
        }]);
      }
    });
  };

  const openGallery = () => {
    const options = {
      mediaType: 'mixed',
      quality: 0.8,
      includeBase64: false,
      selectionLimit: 5
    };

    launchImageLibrary(options, (response) => {
      if (response.assets) {
        const newAttachments = response.assets.map(asset => ({
          uri: asset.uri,
          type: asset.type,
          fileName: asset.fileName,
          fileSize: asset.fileSize
        }));
        setAttachments(prev => [...prev, ...newAttachments]);
      }
    });
  };

  const removeAttachment = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const renderReportItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.reportCard}
      onPress={() => navigation.navigate('ReportDetails', { reportId: item.id })}
    >
      <View style={styles.reportHeader}>
        <View style={styles.reportTypeContainer}>
          <Icon 
            name={REPORT_TYPES.find(t => t.value === item.type)?.icon || 'description'} 
            size={20} 
            color="#2196F3" 
          />
          <Text style={styles.reportType}>{item.type}</Text>
        </View>
        <View style={[styles.priorityBadge, { 
          backgroundColor: PRIORITY_LEVELS.find(p => p.value === item.priority)?.color || '#9E9E9E' 
        }]}>
          <Text style={styles.priorityText}>{item.priority}</Text>
        </View>
      </View>
      
      <Text style={styles.reportTitle}>{item.title}</Text>
      <Text style={styles.reportDescription} numberOfLines={2}>
        {item.description}
      </Text>
      
      <View style={styles.reportFooter}>
        <Text style={styles.reportSite}>{item.site?.name}</Text>
        <Text style={styles.reportDate}>{formatDate(item.createdAt)}</Text>
      </View>
      
      <View style={[styles.statusBadge, { 
        backgroundColor: item.status === 'APPROVED' ? '#4CAF50' : 
                       item.status === 'SUBMITTED' ? '#FF9800' : '#9E9E9E' 
      }]}>
        <Text style={styles.statusText}>{item.status}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderAttachment = ({ item, index }) => (
    <View style={styles.attachmentItem}>
      {item.type.startsWith('image/') ? (
        <Image source={{ uri: item.uri }} style={styles.attachmentImage} />
      ) : (
        <View style={styles.videoPlaceholder}>
          <Icon name="videocam" size={32} color="#666" />
        </View>
      )}
      <TouchableOpacity 
        style={styles.removeAttachment}
        onPress={() => removeAttachment(index)}
      >
        <Icon name="close" size={16} color="#fff" />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Reports</Text>
        <TouchableOpacity style={styles.createButton} onPress={handleCreateReport}>
          <Icon name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Reports List */}
      <FlatList
        data={reports}
        renderItem={renderReportItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.reportsList}
        refreshing={loading}
        onRefresh={loadReports}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="assignment" size={64} color="#ccc" />
            <Text style={styles.emptyText}>No reports yet</Text>
            <Text style={styles.emptySubtext}>Create your first report</Text>
          </View>
        }
      />

      {/* Create Report Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Create Report</Text>
            <TouchableOpacity 
              onPress={handleSubmitReport}
              disabled={uploading}
            >
              <Text style={[styles.submitButton, { opacity: uploading ? 0.5 : 1 }]}>
                {uploading ? 'Submitting...' : 'Submit'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Report Type */}
            <Text style={styles.fieldLabel}>Report Type *</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeSelector}>
              {REPORT_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[styles.typeOption, formData.type === type.value && styles.typeOptionSelected]}
                  onPress={() => setFormData(prev => ({ ...prev, type: type.value }))}
                >
                  <Icon name={type.icon} size={24} color={formData.type === type.value ? '#fff' : '#666'} />
                  <Text style={[styles.typeLabel, formData.type === type.value && styles.typeLabelSelected]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Title */}
            <Text style={styles.fieldLabel}>Title *</Text>
            <TextInput
              style={styles.textInput}
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              placeholder="Enter report title"
              maxLength={100}
            />

            {/* Description */}
            <Text style={styles.fieldLabel}>Description *</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Describe what happened..."
              multiline
              numberOfLines={4}
              maxLength={1000}
            />

            {/* Priority */}
            <Text style={styles.fieldLabel}>Priority</Text>
            <View style={styles.prioritySelector}>
              {PRIORITY_LEVELS.map((priority) => (
                <TouchableOpacity
                  key={priority.value}
                  style={[styles.priorityOption, { 
                    backgroundColor: formData.priority === priority.value ? priority.color : '#f5f5f5' 
                  }]}
                  onPress={() => setFormData(prev => ({ ...prev, priority: priority.value }))}
                >
                  <Text style={[styles.priorityLabel, { 
                    color: formData.priority === priority.value ? '#fff' : '#666' 
                  }]}>
                    {priority.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Attachments */}
            <Text style={styles.fieldLabel}>Attachments</Text>
            <TouchableOpacity style={styles.addAttachmentButton} onPress={handleAddPhoto}>
              <Icon name="add-a-photo" size={24} color="#2196F3" />
              <Text style={styles.addAttachmentText}>Add Photo/Video</Text>
            </TouchableOpacity>

            {attachments.length > 0 && (
              <FlatList
                data={attachments}
                renderItem={renderAttachment}
                keyExtractor={(item, index) => index.toString()}
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.attachmentsList}
              />
            )}

            {/* Location Info */}
            {currentLocation && (
              <View style={styles.locationInfo}>
                <Icon name="location-on" size={16} color="#666" />
                <Text style={styles.locationText}>
                  Location: {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  createButton: {
    backgroundColor: '#2196F3',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reportsList: {
    padding: 16,
  },
  reportCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reportTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportType: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  reportDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  reportFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reportSite: {
    fontSize: 14,
    color: '#2196F3',
  },
  reportDate: {
    fontSize: 12,
    color: '#999',
  },
  statusBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  submitButton: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  typeSelector: {
    marginBottom: 8,
  },
  typeOption: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginRight: 12,
    alignItems: 'center',
    minWidth: 80,
  },
  typeOptionSelected: {
    backgroundColor: '#2196F3',
  },
  typeLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  typeLabelSelected: {
    color: '#fff',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 12,
  },
  priorityOption: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  priorityLabel: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  addAttachmentButton: {
    borderWidth: 2,
    borderColor: '#2196F3',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  addAttachmentText: {
    fontSize: 16,
    color: '#2196F3',
    marginTop: 8,
  },
  attachmentsList: {
    marginTop: 12,
  },
  attachmentItem: {
    position: 'relative',
    marginRight: 12,
  },
  attachmentImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  videoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeAttachment: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#F44336',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  locationText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
});

export default ReportsScreen;
