// BahinLink Mobile Messages Screen
// ⚠️ CRITICAL: Real messaging system with threading ONLY

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  Modal
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { ApiService } from '../services/ApiService';

const MessagesScreen = () => {
  const [conversations, setConversations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [composeModalVisible, setComposeModalVisible] = useState(false);

  const { isSignedIn, user } = useAuth();
  const navigation = useNavigation();

  useFocusEffect(
    useCallback(() => {
      if (isSignedIn) {
        loadMessages();
      }
    }, [isSignedIn])
  );

  const loadMessages = async () => {
    try {
      setIsLoading(true);
      
      const response = await ApiService.get('/messages', {
        limit: 100,
        includeConversations: true
      });
      
      if (response.success) {
        // Group messages into conversations
        const conversationsData = response.data.conversations || [];
        setConversations(conversationsData);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadMessages();
    setIsRefreshing(false);
  };

  const openConversation = (conversation) => {
    navigation.navigate('Conversation', {
      conversationId: conversation.conversationId,
      participants: conversation.participants,
      title: getConversationTitle(conversation)
    });
  };

  const getConversationTitle = (conversation) => {
    const otherParticipants = conversation.participants.filter(
      p => p.id !== user?.id
    );
    
    if (otherParticipants.length === 1) {
      return otherParticipants[0].name;
    } else if (otherParticipants.length > 1) {
      return `${otherParticipants[0].name} +${otherParticipants.length - 1}`;
    }
    
    return 'Unknown';
  };

  const getLastMessagePreview = (conversation) => {
    if (!conversation.lastMessage) return 'No messages';
    
    const message = conversation.lastMessage;
    let preview = message.content;
    
    if (message.type === 'ATTACHMENT') {
      preview = '📎 Attachment';
    } else if (preview.length > 50) {
      preview = preview.substring(0, 50) + '...';
    }
    
    return preview;
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      return `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h`;
    } else if (diffInHours < 168) { // 7 days
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getParticipantInitials = (participants) => {
    const otherParticipants = participants.filter(p => p.id !== user?.id);
    if (otherParticipants.length > 0) {
      const participant = otherParticipants[0];
      return participant.name.split(' ').map(n => n[0]).join('').toUpperCase();
    }
    return '?';
  };

  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    
    const title = getConversationTitle(conversation).toLowerCase();
    const lastMessage = getLastMessagePreview(conversation).toLowerCase();
    const query = searchQuery.toLowerCase();
    
    return title.includes(query) || lastMessage.includes(query);
  });

  const renderConversationItem = ({ item: conversation }) => {
    const hasUnread = conversation.unreadCount > 0;
    const lastMessageTime = conversation.lastMessage?.createdAt;
    
    return (
      <TouchableOpacity
        style={[
          styles.conversationItem,
          hasUnread && styles.unreadConversation
        ]}
        onPress={() => openConversation(conversation)}
      >
        <View style={styles.avatarContainer}>
          <View style={[
            styles.avatar,
            { backgroundColor: hasUnread ? '#007AFF' : '#CCC' }
          ]}>
            <Text style={styles.avatarText}>
              {getParticipantInitials(conversation.participants)}
            </Text>
          </View>
          {hasUnread && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadBadgeText}>
                {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.conversationContent}>
          <View style={styles.conversationHeader}>
            <Text style={[
              styles.conversationTitle,
              hasUnread && styles.unreadTitle
            ]}>
              {getConversationTitle(conversation)}
            </Text>
            {lastMessageTime && (
              <Text style={styles.conversationTime}>
                {formatTime(lastMessageTime)}
              </Text>
            )}
          </View>
          
          <Text style={[
            styles.lastMessage,
            hasUnread && styles.unreadMessage
          ]} numberOfLines={2}>
            {getLastMessagePreview(conversation)}
          </Text>
          
          {conversation.participants.length > 2 && (
            <Text style={styles.participantCount}>
              {conversation.participants.length} participants
            </Text>
          )}
        </View>
        
        <Ionicons 
          name="chevron-forward" 
          size={16} 
          color="#CCC" 
          style={styles.chevron}
        />
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading messages...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>Messages</Text>
          {conversations.reduce((sum, conv) => sum + conv.unreadCount, 0) > 0 && (
            <View style={styles.totalUnreadBadge}>
              <Text style={styles.totalUnreadText}>
                {conversations.reduce((sum, conv) => sum + conv.unreadCount, 0)}
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowSearch(!showSearch)}
          >
            <Ionicons name="search" size={24} color="#007AFF" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setComposeModalVisible(true)}
          >
            <Ionicons name="create" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      {showSearch && (
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search conversations..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Conversations List */}
      <FlatList
        data={filteredConversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.conversationId}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="chatbubbles-outline" size={64} color="#CCC" />
            <Text style={styles.emptyText}>No conversations</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery 
                ? 'No conversations match your search'
                : 'Start a conversation to communicate with your team'
              }
            </Text>
            {!searchQuery && (
              <TouchableOpacity
                style={styles.startChatButton}
                onPress={() => setComposeModalVisible(true)}
              >
                <Text style={styles.startChatButtonText}>Start New Chat</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />

      {/* Floating Action Button */}
      {!showSearch && conversations.length > 0 && (
        <TouchableOpacity
          style={styles.fab}
          onPress={() => setComposeModalVisible(true)}
        >
          <Ionicons name="create" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      )}

      {/* Compose Modal */}
      <Modal
        visible={composeModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.composeContainer}>
          <View style={styles.composeHeader}>
            <TouchableOpacity
              onPress={() => setComposeModalVisible(false)}
            >
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.composeTitle}>New Message</Text>
            <TouchableOpacity>
              <Text style={styles.sendButton}>Send</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.composeContent}>
            <Text style={styles.comingSoonText}>
              Compose feature coming soon...
            </Text>
            <Text style={styles.comingSoonSubtext}>
              For now, you can reply to existing conversations
            </Text>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A1A1A',
  },
  totalUnreadBadge: {
    backgroundColor: '#DC3545',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginLeft: 12,
  },
  totalUnreadText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
  },
  searchContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1A1A1A',
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  unreadConversation: {
    backgroundColor: '#F8F9FF',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  unreadBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#DC3545',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  conversationContent: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1A1A1A',
    flex: 1,
  },
  unreadTitle: {
    fontWeight: '600',
  },
  conversationTime: {
    fontSize: 12,
    color: '#999',
    marginLeft: 8,
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  unreadMessage: {
    color: '#1A1A1A',
    fontWeight: '500',
  },
  participantCount: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  chevron: {
    marginLeft: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  startChatButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  startChatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  composeContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  composeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  cancelButton: {
    fontSize: 16,
    color: '#007AFF',
  },
  composeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  sendButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  composeContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  comingSoonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  comingSoonSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default MessagesScreen;
