// BahinLink Mobile Camera Screen
// ⚠️ CRITICAL: Real camera integration with media upload ONLY

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { Camera } from 'expo-camera';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as MediaLibrary from 'expo-media-library';
import { ApiService } from '../services/ApiService';

const { width, height } = Dimensions.get('window');

const CameraScreen = () => {
  const [hasPermission, setHasPermission] = useState(null);
  const [type, setType] = useState(Camera.Constants.Type.back);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [isRecording, setIsRecording] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);

  const cameraRef = useRef(null);
  const recordingTimer = useRef(null);
  const navigation = useNavigation();
  const route = useRoute();

  const { mode = 'photo', reportId, shiftId } = route.params || {};

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
      
      // Also request media library permissions
      await MediaLibrary.requestPermissionsAsync();
    })();
  }, []);

  useEffect(() => {
    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, []);

  const startRecordingTimer = () => {
    setRecordingTime(0);
    recordingTimer.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  const stopRecordingTimer = () => {
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
      recordingTimer.current = null;
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      try {
        setIsUploading(true);
        
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: true,
          exif: true
        });

        // Save to device gallery
        await MediaLibrary.saveToLibraryAsync(photo.uri);

        // Upload to server
        await uploadMedia(photo, 'photo');
        
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const startRecording = async () => {
    if (cameraRef.current) {
      try {
        setIsRecording(true);
        startRecordingTimer();
        
        const video = await cameraRef.current.recordAsync({
          quality: Camera.Constants.VideoQuality['720p'],
          maxDuration: 300, // 5 minutes max
        });

        stopRecordingTimer();
        setIsRecording(false);

        // Save to device gallery
        await MediaLibrary.saveToLibraryAsync(video.uri);

        // Upload to server
        setIsUploading(true);
        await uploadMedia(video, 'video');
        
      } catch (error) {
        console.error('Error recording video:', error);
        Alert.alert('Error', 'Failed to record video');
        setIsRecording(false);
        stopRecordingTimer();
      } finally {
        setIsUploading(false);
      }
    }
  };

  const stopRecording = () => {
    if (cameraRef.current && isRecording) {
      cameraRef.current.stopRecording();
    }
  };

  const uploadMedia = async (media, mediaType) => {
    try {
      // Convert to base64 if needed
      let base64Data = media.base64;
      if (!base64Data) {
        // For video files, we'd need to convert differently
        // This is a simplified approach
        const response = await fetch(media.uri);
        const blob = await response.blob();
        base64Data = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result.split(',')[1]);
          reader.readAsDataURL(blob);
        });
      }

      const uploadEndpoint = mediaType === 'photo' ? '/upload/photo' : '/upload/video';
      const fileName = `${mediaType}_${Date.now()}.${mediaType === 'photo' ? 'jpg' : 'mp4'}`;

      const uploadResponse = await ApiService.post(uploadEndpoint, {
        file: `data:${mediaType === 'photo' ? 'image/jpeg' : 'video/mp4'};base64,${base64Data}`,
        fileName,
        description: `${mediaType} captured from mobile app`,
        reportId,
        shiftId
      });

      if (uploadResponse.success) {
        Alert.alert(
          'Success',
          `${mediaType === 'photo' ? 'Photo' : 'Video'} uploaded successfully`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      }
    } catch (error) {
      console.error('Upload error:', error);
      Alert.alert('Upload Failed', 'Failed to upload media. Please try again.');
    }
  };

  const toggleCameraType = () => {
    setType(
      type === Camera.Constants.Type.back
        ? Camera.Constants.Type.front
        : Camera.Constants.Type.back
    );
  };

  const toggleFlash = () => {
    setFlashMode(
      flashMode === Camera.Constants.FlashMode.off
        ? Camera.Constants.FlashMode.on
        : Camera.Constants.FlashMode.off
    );
  };

  if (hasPermission === null) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <Ionicons name="camera-outline" size={64} color="#CCC" />
        <Text style={styles.permissionText}>Camera permission required</Text>
        <Text style={styles.permissionSubtext}>
          Please enable camera access in your device settings
        </Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.settingsButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        style={styles.camera}
        type={type}
        flashMode={flashMode}
        ref={cameraRef}
      >
        {/* Header Controls */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="close" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <Text style={styles.modeText}>
              {mode === 'photo' ? 'Photo' : 'Video'}
            </Text>
            {isRecording && (
              <Text style={styles.recordingTime}>
                {formatTime(recordingTime)}
              </Text>
            )}
          </View>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={toggleFlash}
          >
            <Ionicons 
              name={flashMode === Camera.Constants.FlashMode.off ? "flash-off" : "flash"} 
              size={24} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>
        </View>

        {/* Camera Controls */}
        <View style={styles.controls}>
          {/* Mode Selector */}
          <View style={styles.modeSelector}>
            <TouchableOpacity
              style={[styles.modeButton, mode === 'photo' && styles.activeModeButton]}
              onPress={() => {/* Mode switching logic */}}
            >
              <Text style={[styles.modeButtonText, mode === 'photo' && styles.activeModeButtonText]}>
                Photo
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modeButton, mode === 'video' && styles.activeModeButton]}
              onPress={() => {/* Mode switching logic */}}
            >
              <Text style={[styles.modeButtonText, mode === 'video' && styles.activeModeButtonText]}>
                Video
              </Text>
            </TouchableOpacity>
          </View>

          {/* Capture Controls */}
          <View style={styles.captureControls}>
            <View style={styles.captureButtonContainer}>
              {mode === 'photo' ? (
                <TouchableOpacity
                  style={[styles.captureButton, isUploading && styles.captureButtonDisabled]}
                  onPress={takePicture}
                  disabled={isUploading}
                >
                  {isUploading ? (
                    <ActivityIndicator color="#FFFFFF" />
                  ) : (
                    <View style={styles.captureButtonInner} />
                  )}
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.captureButton,
                    isRecording && styles.recordingButton,
                    isUploading && styles.captureButtonDisabled
                  ]}
                  onPress={isRecording ? stopRecording : startRecording}
                  disabled={isUploading}
                >
                  {isUploading ? (
                    <ActivityIndicator color="#FFFFFF" />
                  ) : (
                    <View style={[
                      styles.captureButtonInner,
                      isRecording && styles.recordingButtonInner
                    ]} />
                  )}
                </TouchableOpacity>
              )}
            </View>

            <TouchableOpacity
              style={styles.flipButton}
              onPress={toggleCameraType}
            >
              <Ionicons name="camera-reverse" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Upload Progress */}
        {isUploading && (
          <View style={styles.uploadOverlay}>
            <View style={styles.uploadContainer}>
              <ActivityIndicator size="large" color="#007AFF" />
              <Text style={styles.uploadText}>Uploading...</Text>
            </View>
          </View>
        )}
      </Camera>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#FFFFFF',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    padding: 20,
  },
  permissionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  permissionSubtext: {
    fontSize: 14,
    color: '#CCC',
    textAlign: 'center',
    marginBottom: 24,
  },
  settingsButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  settingsButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  camera: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    alignItems: 'center',
  },
  modeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  recordingTime: {
    fontSize: 14,
    color: '#FF3B30',
    fontWeight: '600',
    marginTop: 4,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 40,
  },
  modeSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 30,
  },
  modeButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  activeModeButton: {
    backgroundColor: '#007AFF',
  },
  modeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  activeModeButtonText: {
    fontWeight: '600',
  },
  captureControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonContainer: {
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  recordingButton: {
    borderColor: '#FF3B30',
  },
  captureButtonDisabled: {
    opacity: 0.5,
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FFFFFF',
  },
  recordingButtonInner: {
    borderRadius: 8,
    backgroundColor: '#FF3B30',
  },
  flipButton: {
    position: 'absolute',
    right: 40,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  uploadText: {
    marginTop: 12,
    fontSize: 16,
    color: '#1A1A1A',
    fontWeight: '500',
  },
});

export default CameraScreen;
