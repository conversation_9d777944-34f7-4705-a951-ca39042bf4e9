// BahinLink Mobile Shift Details Screen
// ⚠️ CRITICAL: Real shift management with time tracking ONLY

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { ApiService } from '../services/ApiService';
import { LocationService } from '../services/LocationService';
import { format, differenceInHours, differenceInMinutes } from 'date-fns';

const ShiftDetailsScreen = () => {
  const [shift, setShift] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [timeEntries, setTimeEntries] = useState([]);

  const { user } = useAuth();
  const navigation = useNavigation();
  const route = useRoute();
  const { shiftId } = route.params;

  useEffect(() => {
    loadShiftDetails();
    getCurrentLocation();
  }, [shiftId]);

  const loadShiftDetails = async () => {
    try {
      setIsLoading(true);
      
      const [shiftResponse, timeResponse] = await Promise.all([
        ApiService.get(`/shifts/${shiftId}`),
        ApiService.get(`/time/entries`, { shiftId })
      ]);
      
      if (shiftResponse.success) {
        setShift(shiftResponse.data);
      }
      
      if (timeResponse.success) {
        setTimeEntries(timeResponse.data);
      }
    } catch (error) {
      console.error('Error loading shift details:', error);
      Alert.alert('Error', 'Failed to load shift details');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadShiftDetails();
    await getCurrentLocation();
    setIsRefreshing(false);
  };

  const handleClockIn = async () => {
    if (!currentLocation) {
      Alert.alert('Location Required', 'Please enable location services to clock in');
      return;
    }

    try {
      const response = await ApiService.post('/time/clock-in', {
        shiftId: shift.id,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        accuracy: currentLocation.accuracy
      });

      if (response.success) {
        Alert.alert('Success', 'Clocked in successfully');
        loadShiftDetails();
      }
    } catch (error) {
      console.error('Clock in error:', error);
      Alert.alert('Error', 'Failed to clock in');
    }
  };

  const handleClockOut = async () => {
    if (!currentLocation) {
      Alert.alert('Location Required', 'Please enable location services to clock out');
      return;
    }

    Alert.alert(
      'Clock Out',
      'Are you sure you want to clock out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clock Out',
          onPress: async () => {
            try {
              const response = await ApiService.post('/time/clock-out', {
                shiftId: shift.id,
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
                accuracy: currentLocation.accuracy
              });

              if (response.success) {
                Alert.alert('Success', 'Clocked out successfully');
                loadShiftDetails();
              }
            } catch (error) {
              console.error('Clock out error:', error);
              Alert.alert('Error', 'Failed to clock out');
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'SCHEDULED': return '#007AFF';
      case 'IN_PROGRESS': return '#28A745';
      case 'COMPLETED': return '#6C757D';
      case 'CANCELLED': return '#DC3545';
      default: return '#6C757D';
    }
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'EEEE, MMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'HH:mm');
  };

  const formatDuration = (hours) => {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    return `${wholeHours}h ${minutes}m`;
  };

  const calculateProgress = () => {
    if (shift.status !== 'IN_PROGRESS') return 0;
    
    const now = new Date();
    const start = new Date(shift.startTime);
    const end = new Date(shift.endTime);
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    const total = end - start;
    const elapsed = now - start;
    return Math.round((elapsed / total) * 100);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading shift details...</Text>
      </View>
    );
  }

  if (!shift) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="calendar-outline" size={64} color="#CCC" />
        <Text style={styles.errorText}>Shift not found</Text>
      </View>
    );
  }

  const isToday = new Date(shift.shiftDate).toDateString() === new Date().toDateString();
  const canClockIn = shift.status === 'SCHEDULED' && isToday;
  const canClockOut = shift.status === 'IN_PROGRESS';
  const isOwnShift = shift.agent?.user?.id === user?.id;
  const progress = calculateProgress();

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Shift Details</Text>
        
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
        >
          <Ionicons name="refresh" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Status Section */}
        <View style={styles.statusSection}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(shift.status) }]}>
            <Text style={styles.statusText}>{shift.status}</Text>
          </View>
          {shift.status === 'IN_PROGRESS' && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>{progress}% Complete</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${progress}%` }]} />
              </View>
            </View>
          )}
        </View>

        {/* Shift Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shift Information</Text>
          
          <View style={styles.infoRow}>
            <Ionicons name="calendar" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Date:</Text>
            <Text style={styles.infoValue}>{formatDate(shift.shiftDate)}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="time" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Time:</Text>
            <Text style={styles.infoValue}>
              {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="hourglass" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Duration:</Text>
            <Text style={styles.infoValue}>
              {formatDuration(shift.scheduledDuration || 0)}
            </Text>
          </View>
          
          {shift.hoursWorked > 0 && (
            <View style={styles.infoRow}>
              <Ionicons name="checkmark-circle" size={20} color="#28A745" />
              <Text style={styles.infoLabel}>Worked:</Text>
              <Text style={styles.infoValue}>
                {formatDuration(shift.hoursWorked)}
                {shift.overtimeHours > 0 && (
                  <Text style={styles.overtimeText}>
                    {' '}(+{formatDuration(shift.overtimeHours)} OT)
                  </Text>
                )}
              </Text>
            </View>
          )}
        </View>

        {/* Site Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Site Information</Text>
          
          <View style={styles.infoRow}>
            <Ionicons name="location" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Site:</Text>
            <Text style={styles.infoValue}>{shift.site?.name || 'Unknown Site'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="business" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Client:</Text>
            <Text style={styles.infoValue}>{shift.site?.client?.companyName || 'Unknown Client'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="map" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Address:</Text>
            <Text style={styles.infoValue}>{shift.site?.address || 'No address'}</Text>
          </View>
        </View>

        {/* Agent Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Agent Information</Text>
          
          <View style={styles.infoRow}>
            <Ionicons name="person" size={20} color="#007AFF" />
            <Text style={styles.infoLabel}>Agent:</Text>
            <Text style={styles.infoValue}>
              {shift.agent?.user ? 
                `${shift.agent.user.firstName} ${shift.agent.user.lastName}` : 
                'Unassigned'
              }
            </Text>
          </View>
          
          {shift.agent?.employeeId && (
            <View style={styles.infoRow}>
              <Ionicons name="card" size={20} color="#007AFF" />
              <Text style={styles.infoLabel}>ID:</Text>
              <Text style={styles.infoValue}>{shift.agent.employeeId}</Text>
            </View>
          )}
          
          {shift.agent?.user?.phone && (
            <View style={styles.infoRow}>
              <Ionicons name="call" size={20} color="#007AFF" />
              <Text style={styles.infoLabel}>Phone:</Text>
              <Text style={styles.infoValue}>{shift.agent.user.phone}</Text>
            </View>
          )}
        </View>

        {/* Special Instructions */}
        {shift.specialInstructions && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Special Instructions</Text>
            <Text style={styles.instructions}>{shift.specialInstructions}</Text>
          </View>
        )}

        {/* Time Entries */}
        {timeEntries.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Time Entries</Text>
            {timeEntries.map((entry, index) => (
              <View key={entry.id || index} style={styles.timeEntry}>
                <View style={styles.timeEntryHeader}>
                  <Text style={styles.timeEntryType}>{entry.type}</Text>
                  <Text style={styles.timeEntryTime}>
                    {formatTime(entry.timestamp)}
                  </Text>
                </View>
                {entry.location && (
                  <Text style={styles.timeEntryLocation}>
                    📍 {entry.location.latitude?.toFixed(6)}, {entry.location.longitude?.toFixed(6)}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity
            style={styles.quickAction}
            onPress={() => navigation.navigate('Reports', { shiftId: shift.id })}
          >
            <Ionicons name="document-text" size={24} color="#007AFF" />
            <Text style={styles.quickActionText}>Create Report</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.quickAction}
            onPress={() => navigation.navigate('QRScanner')}
          >
            <Ionicons name="qr-code" size={24} color="#007AFF" />
            <Text style={styles.quickActionText}>Scan QR Code</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.quickAction}
            onPress={() => navigation.navigate('Camera', { shiftId: shift.id })}
          >
            <Ionicons name="camera" size={24} color="#007AFF" />
            <Text style={styles.quickActionText}>Take Photo</Text>
            <Ionicons name="chevron-forward" size={16} color="#CCC" />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      {isOwnShift && (canClockIn || canClockOut) && (
        <View style={styles.actionButtons}>
          {canClockIn && (
            <TouchableOpacity
              style={[styles.actionButton, styles.clockInButton]}
              onPress={handleClockIn}
            >
              <Ionicons name="play" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Clock In</Text>
            </TouchableOpacity>
          )}
          
          {canClockOut && (
            <TouchableOpacity
              style={[styles.actionButton, styles.clockOutButton]}
              onPress={handleClockOut}
            >
              <Ionicons name="stop" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Clock Out</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E5E9',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  statusSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E1E5E9',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#28A745',
    borderRadius: 2,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginLeft: 12,
    marginRight: 8,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 14,
    color: '#1A1A1A',
    flex: 1,
  },
  overtimeText: {
    color: '#FF9500',
    fontWeight: '500',
  },
  instructions: {
    fontSize: 14,
    color: '#1A1A1A',
    lineHeight: 20,
  },
  timeEntry: {
    backgroundColor: '#F8F9FA',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  timeEntryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  timeEntryType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A1A1A',
  },
  timeEntryTime: {
    fontSize: 14,
    color: '#666',
  },
  timeEntryLocation: {
    fontSize: 12,
    color: '#999',
  },
  quickAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  quickActionText: {
    fontSize: 16,
    color: '#1A1A1A',
    marginLeft: 12,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E1E5E9',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  clockInButton: {
    backgroundColor: '#28A745',
  },
  clockOutButton: {
    backgroundColor: '#DC3545',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});

export default ShiftDetailsScreen;
