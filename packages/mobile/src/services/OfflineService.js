// BahinLink Mobile Offline Service
// ⚠️ CRITICAL: Real offline data management and sync ONLY

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import { ApiService } from './ApiService';

class OfflineService {
  constructor() {
    this.isOnline = true;
    this.syncQueue = [];
    this.isInitialized = false;
    this.netInfoUnsubscribe = null;
    this.syncInProgress = false;
  }

  /**
   * Initialize offline service
   */
  async initialize() {
    try {
      if (this.isInitialized) return;

      // Check initial network state
      const netInfo = await NetInfo.fetch();
      this.isOnline = netInfo.isConnected && netInfo.isInternetReachable;

      // Set up network state listener
      this.netInfoUnsubscribe = NetInfo.addEventListener(this.handleNetworkStateChange.bind(this));

      // Load pending sync queue
      await this.loadSyncQueue();

      // Attempt initial sync if online
      if (this.isOnline) {
        await this.syncPendingData();
      }

      this.isInitialized = true;
      console.log('OfflineService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize OfflineService:', error);
      throw error;
    }
  }

  /**
   * Handle network state changes
   */
  async handleNetworkStateChange(state) {
    const wasOnline = this.isOnline;
    this.isOnline = state.isConnected && state.isInternetReachable;

    console.log('Network state changed:', {
      isConnected: state.isConnected,
      isInternetReachable: state.isInternetReachable,
      type: state.type
    });

    // If we just came back online, sync pending data
    if (!wasOnline && this.isOnline) {
      console.log('Device came back online, syncing pending data...');
      await this.syncPendingData();
    }
  }

  /**
   * Check if device is online
   */
  isDeviceOnline() {
    return this.isOnline;
  }

  /**
   * Store data for offline access
   */
  async storeOfflineData(key, data, expirationHours = 24) {
    try {
      const offlineData = {
        data,
        timestamp: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (expirationHours * 60 * 60 * 1000)).toISOString()
      };

      await AsyncStorage.setItem(`offline_${key}`, JSON.stringify(offlineData));
    } catch (error) {
      console.error('Error storing offline data:', error);
      throw error;
    }
  }

  /**
   * Get offline data
   */
  async getOfflineData(key) {
    try {
      const storedData = await AsyncStorage.getItem(`offline_${key}`);
      if (!storedData) return null;

      const offlineData = JSON.parse(storedData);
      
      // Check if data has expired
      if (new Date() > new Date(offlineData.expiresAt)) {
        await AsyncStorage.removeItem(`offline_${key}`);
        return null;
      }

      return offlineData.data;
    } catch (error) {
      console.error('Error getting offline data:', error);
      return null;
    }
  }

  /**
   * Queue action for sync when online
   */
  async queueForSync(action) {
    try {
      const syncItem = {
        id: Date.now().toString(),
        action,
        timestamp: new Date().toISOString(),
        retryCount: 0,
        maxRetries: 3
      };

      this.syncQueue.push(syncItem);
      await this.saveSyncQueue();

      console.log('Action queued for sync:', action.type);

      // If online, try to sync immediately
      if (this.isOnline && !this.syncInProgress) {
        await this.syncPendingData();
      }
    } catch (error) {
      console.error('Error queueing action for sync:', error);
      throw error;
    }
  }

  /**
   * Load sync queue from storage
   */
  async loadSyncQueue() {
    try {
      const storedQueue = await AsyncStorage.getItem('sync_queue');
      this.syncQueue = storedQueue ? JSON.parse(storedQueue) : [];
    } catch (error) {
      console.error('Error loading sync queue:', error);
      this.syncQueue = [];
    }
  }

  /**
   * Save sync queue to storage
   */
  async saveSyncQueue() {
    try {
      await AsyncStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Error saving sync queue:', error);
    }
  }

  /**
   * Sync pending data when online
   */
  async syncPendingData() {
    if (!this.isOnline || this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`Starting sync of ${this.syncQueue.length} pending items...`);

    const successfulSyncs = [];
    const failedSyncs = [];

    for (const item of this.syncQueue) {
      try {
        await this.syncSingleItem(item);
        successfulSyncs.push(item.id);
        console.log('Successfully synced:', item.action.type);
      } catch (error) {
        console.error('Failed to sync item:', item.action.type, error);
        
        item.retryCount++;
        if (item.retryCount >= item.maxRetries) {
          console.log('Max retries reached for item:', item.action.type);
          failedSyncs.push(item.id);
        }
      }
    }

    // Remove successfully synced and permanently failed items
    this.syncQueue = this.syncQueue.filter(item => 
      !successfulSyncs.includes(item.id) && !failedSyncs.includes(item.id)
    );

    await this.saveSyncQueue();
    this.syncInProgress = false;

    console.log(`Sync completed. Success: ${successfulSyncs.length}, Failed: ${failedSyncs.length}, Remaining: ${this.syncQueue.length}`);
  }

  /**
   * Sync a single item
   */
  async syncSingleItem(item) {
    const { action } = item;

    switch (action.type) {
      case 'CLOCK_IN':
        await ApiService.post('/time/clock-in', action.data);
        break;
      
      case 'CLOCK_OUT':
        await ApiService.post('/time/clock-out', action.data);
        break;
      
      case 'SUBMIT_REPORT':
        await ApiService.post('/reports', action.data);
        break;
      
      case 'UPLOAD_PHOTO':
        await ApiService.post('/upload/photo', action.data);
        break;
      
      case 'UPLOAD_VIDEO':
        await ApiService.post('/upload/video', action.data);
        break;
      
      case 'UPDATE_LOCATION':
        await ApiService.post('/agents/location', action.data);
        break;
      
      default:
        console.warn('Unknown sync action type:', action.type);
        throw new Error(`Unknown sync action type: ${action.type}`);
    }
  }

  /**
   * Cache essential data for offline use
   */
  async cacheEssentialData() {
    if (!this.isOnline) {
      console.log('Device offline, cannot cache data');
      return;
    }

    try {
      console.log('Caching essential data for offline use...');

      // Cache user profile
      const profile = await ApiService.get('/auth/profile');
      if (profile.success) {
        await this.storeOfflineData('user_profile', profile.data, 168); // 7 days
      }

      // Cache current shifts
      const shifts = await ApiService.get('/shifts', {
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      });
      if (shifts.success) {
        await this.storeOfflineData('current_shifts', shifts.data, 24); // 1 day
      }

      // Cache sites data
      const sites = await ApiService.get('/sites');
      if (sites.success) {
        await this.storeOfflineData('sites', sites.data, 72); // 3 days
      }

      // Cache recent reports
      const reports = await ApiService.get('/reports', {
        limit: 50,
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
      });
      if (reports.success) {
        await this.storeOfflineData('recent_reports', reports.data, 24); // 1 day
      }

      console.log('Essential data cached successfully');
    } catch (error) {
      console.error('Error caching essential data:', error);
    }
  }

  /**
   * Get cached shifts for offline use
   */
  async getCachedShifts() {
    return await this.getOfflineData('current_shifts') || [];
  }

  /**
   * Get cached sites for offline use
   */
  async getCachedSites() {
    return await this.getOfflineData('sites') || [];
  }

  /**
   * Get cached user profile for offline use
   */
  async getCachedUserProfile() {
    return await this.getOfflineData('user_profile');
  }

  /**
   * Get cached reports for offline use
   */
  async getCachedReports() {
    return await this.getOfflineData('recent_reports') || [];
  }

  /**
   * Store time entry for offline sync
   */
  async storeTimeEntryOffline(timeEntryData, actionType) {
    await this.queueForSync({
      type: actionType,
      data: timeEntryData
    });

    // Also store locally for immediate UI updates
    const key = `pending_${actionType.toLowerCase()}_${Date.now()}`;
    await this.storeOfflineData(key, timeEntryData, 48);
  }

  /**
   * Store report for offline sync
   */
  async storeReportOffline(reportData) {
    await this.queueForSync({
      type: 'SUBMIT_REPORT',
      data: reportData
    });

    // Store locally for immediate UI updates
    const key = `pending_report_${Date.now()}`;
    await this.storeOfflineData(key, reportData, 48);
  }

  /**
   * Store media upload for offline sync
   */
  async storeMediaUploadOffline(mediaData, mediaType) {
    const actionType = mediaType === 'photo' ? 'UPLOAD_PHOTO' : 'UPLOAD_VIDEO';
    
    await this.queueForSync({
      type: actionType,
      data: mediaData
    });

    // Store locally
    const key = `pending_${mediaType}_${Date.now()}`;
    await this.storeOfflineData(key, mediaData, 48);
  }

  /**
   * Get pending sync count
   */
  getPendingSyncCount() {
    return this.syncQueue.length;
  }

  /**
   * Clear all offline data
   */
  async clearOfflineData() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const offlineKeys = keys.filter(key => key.startsWith('offline_'));
      
      if (offlineKeys.length > 0) {
        await AsyncStorage.multiRemove(offlineKeys);
      }

      this.syncQueue = [];
      await this.saveSyncQueue();

      console.log('All offline data cleared');
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }

  /**
   * Get offline storage usage
   */
  async getOfflineStorageUsage() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const offlineKeys = keys.filter(key => key.startsWith('offline_'));
      
      let totalSize = 0;
      for (const key of offlineKeys) {
        const data = await AsyncStorage.getItem(key);
        if (data) {
          totalSize += new Blob([data]).size;
        }
      }

      return {
        itemCount: offlineKeys.length,
        totalSizeBytes: totalSize,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2)
      };
    } catch (error) {
      console.error('Error calculating offline storage usage:', error);
      return { itemCount: 0, totalSizeBytes: 0, totalSizeMB: '0.00' };
    }
  }

  /**
   * Cleanup service
   */
  cleanup() {
    if (this.netInfoUnsubscribe) {
      this.netInfoUnsubscribe();
    }
    this.isInitialized = false;
  }
}

// Export singleton instance
export default new OfflineService();
