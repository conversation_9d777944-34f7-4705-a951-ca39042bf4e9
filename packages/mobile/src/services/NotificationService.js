// BahinLink Mobile Notification Service
// ⚠️ CRITICAL: Real push notification integration ONLY

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiService } from './ApiService';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    this.isInitialized = false;
  }

  /**
   * Initialize notification service
   */
  async initialize() {
    try {
      if (this.isInitialized) return;

      // Register for push notifications
      await this.registerForPushNotifications();

      // Set up notification listeners
      this.setupNotificationListeners();

      this.isInitialized = true;
      console.log('NotificationService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize NotificationService:', error);
      throw error;
    }
  }

  /**
   * Register device for push notifications
   */
  async registerForPushNotifications() {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices');
        return null;
      }

      // Check existing permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // Request permissions if not granted
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permissions not granted');
        return null;
      }

      // Get push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.expoPushToken = token.data;

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'BahinLink Notifications',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#007AFF',
          sound: 'default',
        });

        // Create additional channels for different notification types
        await Notifications.setNotificationChannelAsync('emergency', {
          name: 'Emergency Alerts',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 500, 250, 500],
          lightColor: '#FF3B30',
          sound: 'default',
        });

        await Notifications.setNotificationChannelAsync('shift', {
          name: 'Shift Notifications',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#007AFF',
          sound: 'default',
        });
      }

      // Register token with backend
      await this.registerTokenWithBackend(this.expoPushToken);

      console.log('Push notification token:', this.expoPushToken);
      return this.expoPushToken;

    } catch (error) {
      console.error('Error registering for push notifications:', error);
      throw error;
    }
  }

  /**
   * Register push token with backend
   */
  async registerTokenWithBackend(token) {
    try {
      await ApiService.post('/notifications/register-device', {
        pushToken: token,
        platform: Platform.OS,
        deviceInfo: {
          brand: Device.brand,
          modelName: Device.modelName,
          osName: Device.osName,
          osVersion: Device.osVersion,
        }
      });
    } catch (error) {
      console.error('Failed to register token with backend:', error);
      // Don't throw here as local notifications can still work
    }
  }

  /**
   * Set up notification listeners
   */
  setupNotificationListeners() {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(
      this.handleNotificationReceived.bind(this)
    );

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      this.handleNotificationResponse.bind(this)
    );
  }

  /**
   * Handle notification received while app is in foreground
   */
  async handleNotificationReceived(notification) {
    console.log('Notification received:', notification);
    
    const { data } = notification.request.content;
    
    // Store notification locally
    await this.storeNotificationLocally(notification);
    
    // Update badge count
    await this.updateBadgeCount();
    
    // Handle specific notification types
    if (data?.type === 'EMERGENCY') {
      // Handle emergency notifications with special UI
      this.handleEmergencyNotification(notification);
    } else if (data?.type === 'SHIFT_REMINDER') {
      // Handle shift reminders
      this.handleShiftReminder(notification);
    }
  }

  /**
   * Handle notification tap/response
   */
  async handleNotificationResponse(response) {
    console.log('Notification response:', response);
    
    const { data } = response.notification.request.content;
    
    // Mark notification as read
    if (data?.notificationId) {
      await this.markNotificationAsRead(data.notificationId);
    }
    
    // Navigate based on notification type
    if (data?.navigationTarget) {
      // This would integrate with your navigation system
      console.log('Navigate to:', data.navigationTarget);
    }
  }

  /**
   * Send local notification
   */
  async sendLocalNotification(title, body, data = {}, options = {}) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: options.sound || 'default',
          priority: options.priority || Notifications.AndroidImportance.HIGH,
          categoryIdentifier: options.category || 'default',
        },
        trigger: options.trigger || null,
      });

      return notificationId;
    } catch (error) {
      console.error('Error sending local notification:', error);
      throw error;
    }
  }

  /**
   * Schedule shift reminder notification
   */
  async scheduleShiftReminder(shift, minutesBefore = 30) {
    try {
      const shiftStart = new Date(shift.startTime);
      const reminderTime = new Date(shiftStart.getTime() - (minutesBefore * 60 * 1000));
      
      if (reminderTime <= new Date()) {
        console.log('Shift reminder time has passed, not scheduling');
        return null;
      }

      const notificationId = await this.sendLocalNotification(
        'Shift Reminder',
        `Your shift at ${shift.site.name} starts in ${minutesBefore} minutes`,
        {
          type: 'SHIFT_REMINDER',
          shiftId: shift.id,
          navigationTarget: 'Shifts'
        },
        {
          trigger: { date: reminderTime },
          category: 'shift',
          priority: Notifications.AndroidImportance.HIGH
        }
      );

      // Store reminder ID for potential cancellation
      await AsyncStorage.setItem(
        `shift_reminder_${shift.id}`,
        notificationId
      );

      return notificationId;
    } catch (error) {
      console.error('Error scheduling shift reminder:', error);
      throw error;
    }
  }

  /**
   * Cancel shift reminder
   */
  async cancelShiftReminder(shiftId) {
    try {
      const reminderKey = `shift_reminder_${shiftId}`;
      const notificationId = await AsyncStorage.getItem(reminderKey);
      
      if (notificationId) {
        await Notifications.cancelScheduledNotificationAsync(notificationId);
        await AsyncStorage.removeItem(reminderKey);
      }
    } catch (error) {
      console.error('Error canceling shift reminder:', error);
    }
  }

  /**
   * Handle emergency notifications
   */
  handleEmergencyNotification(notification) {
    // Emergency notifications should be handled with special UI
    // This could trigger a modal or special alert
    console.log('Emergency notification received:', notification);
  }

  /**
   * Handle shift reminder notifications
   */
  handleShiftReminder(notification) {
    // Handle shift reminder logic
    console.log('Shift reminder received:', notification);
  }

  /**
   * Store notification locally for offline access
   */
  async storeNotificationLocally(notification) {
    try {
      const storedNotifications = await AsyncStorage.getItem('local_notifications');
      const notifications = storedNotifications ? JSON.parse(storedNotifications) : [];
      
      notifications.unshift({
        id: notification.request.identifier,
        title: notification.request.content.title,
        body: notification.request.content.body,
        data: notification.request.content.data,
        receivedAt: new Date().toISOString(),
        isRead: false
      });

      // Keep only last 100 notifications
      const trimmedNotifications = notifications.slice(0, 100);
      
      await AsyncStorage.setItem(
        'local_notifications',
        JSON.stringify(trimmedNotifications)
      );
    } catch (error) {
      console.error('Error storing notification locally:', error);
    }
  }

  /**
   * Get locally stored notifications
   */
  async getLocalNotifications() {
    try {
      const storedNotifications = await AsyncStorage.getItem('local_notifications');
      return storedNotifications ? JSON.parse(storedNotifications) : [];
    } catch (error) {
      console.error('Error getting local notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId) {
    try {
      // Mark as read in backend
      await ApiService.put(`/notifications/${notificationId}`, {
        isRead: true
      });

      // Update local storage
      const storedNotifications = await AsyncStorage.getItem('local_notifications');
      if (storedNotifications) {
        const notifications = JSON.parse(storedNotifications);
        const updatedNotifications = notifications.map(notif => 
          notif.id === notificationId ? { ...notif, isRead: true } : notif
        );
        
        await AsyncStorage.setItem(
          'local_notifications',
          JSON.stringify(updatedNotifications)
        );
      }

      // Update badge count
      await this.updateBadgeCount();
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Update app badge count
   */
  async updateBadgeCount() {
    try {
      const notifications = await this.getLocalNotifications();
      const unreadCount = notifications.filter(notif => !notif.isRead).length;
      
      await Notifications.setBadgeCountAsync(unreadCount);
    } catch (error) {
      console.error('Error updating badge count:', error);
    }
  }

  /**
   * Clear all notifications
   */
  async clearAllNotifications() {
    try {
      await Notifications.dismissAllNotificationsAsync();
      await AsyncStorage.removeItem('local_notifications');
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  /**
   * Get push token
   */
  getPushToken() {
    return this.expoPushToken;
  }

  /**
   * Cleanup service
   */
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
    this.isInitialized = false;
  }
}

// Export singleton instance
export default new NotificationService();
