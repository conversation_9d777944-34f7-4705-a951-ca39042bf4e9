// BahinLink Mobile QR Code Service
// ⚠️ CRITICAL: Real QR code scanning and validation ONLY

import { BarCodeScanner } from 'expo-barcode-scanner';
import { Alert } from 'react-native';
import { ApiService } from './ApiService';
import { LocationService } from './LocationService';
import OfflineService from './OfflineService';

class QRService {
  constructor() {
    this.hasPermissions = false;
    this.isInitialized = false;
    this.isScanning = false;
  }

  /**
   * Initialize QR service and request permissions
   */
  async initialize() {
    try {
      if (this.isInitialized) return;

      // Request camera permissions for barcode scanning
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      this.hasPermissions = status === 'granted';

      if (!this.hasPermissions) {
        console.warn('QR scanner permissions not granted');
      }

      this.isInitialized = true;
      console.log('QRService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize QRService:', error);
      throw error;
    }
  }

  /**
   * Check if camera permissions are granted
   */
  async checkPermissions() {
    const { status } = await BarCodeScanner.getPermissionsAsync();
    return {
      camera: status === 'granted',
      hasPermissions: status === 'granted'
    };
  }

  /**
   * Scan QR code and handle the result
   */
  async handleQRCodeScanned(data, onSuccess, onError) {
    if (this.isScanning) return; // Prevent multiple scans
    
    this.isScanning = true;
    
    try {
      console.log('QR Code scanned:', data);
      
      // Parse QR code data
      const qrData = this.parseQRCode(data);
      
      if (!qrData) {
        throw new Error('Invalid QR code format');
      }

      // Validate QR code based on type
      const validationResult = await this.validateQRCode(qrData);
      
      if (validationResult.success) {
        // Handle successful scan based on QR type
        await this.handleValidQRCode(qrData, validationResult);
        
        if (onSuccess) {
          onSuccess(qrData, validationResult);
        }
      } else {
        throw new Error(validationResult.error || 'QR code validation failed');
      }
      
    } catch (error) {
      console.error('QR code scan error:', error);
      
      if (onError) {
        onError(error);
      } else {
        Alert.alert('QR Code Error', error.message);
      }
    } finally {
      // Reset scanning flag after a delay to prevent rapid re-scanning
      setTimeout(() => {
        this.isScanning = false;
      }, 2000);
    }
  }

  /**
   * Parse QR code data
   */
  parseQRCode(data) {
    try {
      // Try to parse as JSON first (for BahinLink QR codes)
      const parsed = JSON.parse(data);
      
      // Validate required fields for BahinLink QR codes
      if (parsed.type && parsed.siteId) {
        return parsed;
      }
      
      return null;
    } catch (error) {
      // If not JSON, check for other formats
      
      // Check for BahinLink site check-in format
      if (data.startsWith('BAHINLINK_CHECKIN_')) {
        const parts = data.split('_');
        if (parts.length >= 4) {
          return {
            type: 'SITE_CHECKIN',
            code: data,
            siteHash: parts[2],
            timestamp: parts[3],
            randomString: parts[4]
          };
        }
      }
      
      // Check for simple site ID format
      if (data.startsWith('SITE_')) {
        return {
          type: 'SITE_CHECKIN',
          siteId: data.replace('SITE_', ''),
          code: data
        };
      }
      
      return null;
    }
  }

  /**
   * Validate QR code with server
   */
  async validateQRCode(qrData) {
    try {
      // Get current location for validation
      const location = await LocationService.getCurrentLocation();
      
      const validationData = {
        qrCode: qrData.code || JSON.stringify(qrData),
        qrType: qrData.type,
        siteId: qrData.siteId,
        location: location ? {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy
        } : null,
        timestamp: new Date().toISOString()
      };

      if (OfflineService.isDeviceOnline()) {
        // Online validation
        const response = await ApiService.post('/qr/validate', validationData);
        return response;
      } else {
        // Offline validation using cached data
        return await this.validateQRCodeOffline(qrData, location);
      }
    } catch (error) {
      console.error('QR validation error:', error);
      return {
        success: false,
        error: 'Failed to validate QR code'
      };
    }
  }

  /**
   * Validate QR code offline using cached data
   */
  async validateQRCodeOffline(qrData, location) {
    try {
      // Get cached sites data
      const cachedSites = await OfflineService.getCachedSites();
      
      if (!cachedSites || cachedSites.length === 0) {
        return {
          success: false,
          error: 'No cached site data available for offline validation'
        };
      }

      // Find matching site
      let matchingSite = null;
      
      if (qrData.siteId) {
        matchingSite = cachedSites.find(site => site.id === qrData.siteId);
      } else if (qrData.siteHash) {
        // Match by site hash (simplified matching)
        matchingSite = cachedSites.find(site => 
          site.id.substring(0, 8) === qrData.siteHash
        );
      }

      if (!matchingSite) {
        return {
          success: false,
          error: 'Site not found in cached data'
        };
      }

      // Validate location if available
      if (location && matchingSite.latitude && matchingSite.longitude) {
        const distance = this.calculateDistance(
          location.latitude,
          location.longitude,
          matchingSite.latitude,
          matchingSite.longitude
        );

        if (distance > matchingSite.geofenceRadius) {
          return {
            success: false,
            error: `You are ${Math.round(distance)}m from the site (allowed: ${matchingSite.geofenceRadius}m)`,
            distance,
            allowedRadius: matchingSite.geofenceRadius
          };
        }
      }

      return {
        success: true,
        site: matchingSite,
        offline: true,
        message: 'QR code validated offline'
      };
    } catch (error) {
      console.error('Offline QR validation error:', error);
      return {
        success: false,
        error: 'Offline validation failed'
      };
    }
  }

  /**
   * Handle valid QR code based on type
   */
  async handleValidQRCode(qrData, validationResult) {
    switch (qrData.type) {
      case 'SITE_CHECKIN':
        await this.handleSiteCheckIn(qrData, validationResult);
        break;
      
      default:
        console.log('Unknown QR code type:', qrData.type);
    }
  }

  /**
   * Handle site check-in QR code
   */
  async handleSiteCheckIn(qrData, validationResult) {
    try {
      const location = await LocationService.getCurrentLocation();
      
      // Check if user has an active shift at this site
      const cachedShifts = await OfflineService.getCachedShifts();
      const activeShift = cachedShifts.find(shift => 
        shift.siteId === validationResult.site.id &&
        shift.status === 'SCHEDULED' &&
        new Date(shift.shiftDate).toDateString() === new Date().toDateString()
      );

      if (!activeShift) {
        Alert.alert(
          'No Active Shift',
          'You don\'t have an active shift at this site today.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Prompt for clock-in
      Alert.alert(
        'Site Check-In',
        `Would you like to clock in at ${validationResult.site.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Clock In',
            onPress: async () => {
              try {
                const clockInData = {
                  shiftId: activeShift.id,
                  latitude: location?.latitude,
                  longitude: location?.longitude,
                  accuracy: location?.accuracy,
                  qrCode: qrData.code || JSON.stringify(qrData)
                };

                if (OfflineService.isDeviceOnline()) {
                  await ApiService.post('/time/clock-in', clockInData);
                  Alert.alert('Success', 'Clocked in successfully');
                } else {
                  await OfflineService.storeTimeEntryOffline(clockInData, 'CLOCK_IN');
                  Alert.alert('Offline', 'Clock-in saved. Will sync when online.');
                }
              } catch (error) {
                console.error('Clock-in error:', error);
                Alert.alert('Error', 'Failed to clock in');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Site check-in error:', error);
      Alert.alert('Error', 'Failed to process site check-in');
    }
  }

  /**
   * Calculate distance between two coordinates
   */
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  }

  /**
   * Generate QR code data for site
   */
  generateSiteQRCode(site) {
    return JSON.stringify({
      type: 'SITE_CHECKIN',
      siteId: site.id,
      siteName: site.name,
      clientName: site.client?.companyName,
      latitude: site.latitude,
      longitude: site.longitude,
      geofenceRadius: site.geofenceRadius,
      generatedAt: new Date().toISOString()
    });
  }

  /**
   * Get supported barcode types
   */
  getSupportedBarcodeTypes() {
    return [
      BarCodeScanner.Constants.BarCodeType.qr,
      BarCodeScanner.Constants.BarCodeType.pdf417,
      BarCodeScanner.Constants.BarCodeType.aztec,
      BarCodeScanner.Constants.BarCodeType.code128,
      BarCodeScanner.Constants.BarCodeType.code39,
      BarCodeScanner.Constants.BarCodeType.code93,
      BarCodeScanner.Constants.BarCodeType.codabar,
      BarCodeScanner.Constants.BarCodeType.ean13,
      BarCodeScanner.Constants.BarCodeType.ean8,
      BarCodeScanner.Constants.BarCodeType.upc_e,
      BarCodeScanner.Constants.BarCodeType.datamatrix
    ];
  }

  /**
   * Check if scanning is in progress
   */
  isScanningInProgress() {
    return this.isScanning;
  }

  /**
   * Reset scanning state
   */
  resetScanningState() {
    this.isScanning = false;
  }

  /**
   * Get scanner capabilities
   */
  async getScannerCapabilities() {
    try {
      const permissions = await this.checkPermissions();
      
      return {
        hasPermissions: permissions.hasPermissions,
        canScanQR: permissions.camera,
        supportedTypes: this.getSupportedBarcodeTypes(),
        isInitialized: this.isInitialized
      };
    } catch (error) {
      console.error('Error getting scanner capabilities:', error);
      return {
        hasPermissions: false,
        canScanQR: false,
        supportedTypes: [],
        isInitialized: false
      };
    }
  }
}

// Export singleton instance
export default new QRService();
