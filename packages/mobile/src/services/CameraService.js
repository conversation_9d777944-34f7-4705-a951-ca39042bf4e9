// BahinLink Mobile Camera Service
// ⚠️ CRITICAL: Real camera and media management ONLY

import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { ApiService } from './ApiService';
import OfflineService from './OfflineService';

class CameraService {
  constructor() {
    this.hasPermissions = false;
    this.isInitialized = false;
  }

  /**
   * Initialize camera service and request permissions
   */
  async initialize() {
    try {
      if (this.isInitialized) return;

      // Request camera permissions
      const cameraPermission = await Camera.requestCameraPermissionsAsync();
      
      // Request media library permissions
      const mediaPermission = await MediaLibrary.requestPermissionsAsync();
      
      // Request image picker permissions
      const imagePickerPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      this.hasPermissions = 
        cameraPermission.status === 'granted' &&
        mediaPermission.status === 'granted' &&
        imagePickerPermission.status === 'granted';

      if (!this.hasPermissions) {
        console.warn('Camera service permissions not fully granted');
      }

      this.isInitialized = true;
      console.log('CameraService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize CameraService:', error);
      throw error;
    }
  }

  /**
   * Check if camera permissions are granted
   */
  async checkPermissions() {
    const cameraPermission = await Camera.getCameraPermissionsAsync();
    const mediaPermission = await MediaLibrary.getPermissionsAsync();
    
    return {
      camera: cameraPermission.status === 'granted',
      mediaLibrary: mediaPermission.status === 'granted',
      hasAllPermissions: cameraPermission.status === 'granted' && mediaPermission.status === 'granted'
    };
  }

  /**
   * Take photo with camera
   */
  async takePhoto(cameraRef, options = {}) {
    try {
      if (!this.hasPermissions) {
        throw new Error('Camera permissions not granted');
      }

      if (!cameraRef.current) {
        throw new Error('Camera reference not available');
      }

      const defaultOptions = {
        quality: 0.8,
        base64: true,
        exif: true,
        skipProcessing: false
      };

      const photo = await cameraRef.current.takePictureAsync({
        ...defaultOptions,
        ...options
      });

      // Add metadata
      const photoWithMetadata = {
        ...photo,
        timestamp: new Date().toISOString(),
        type: 'photo',
        source: 'camera'
      };

      // Save to device gallery
      if (options.saveToGallery !== false) {
        await MediaLibrary.saveToLibraryAsync(photo.uri);
      }

      return photoWithMetadata;
    } catch (error) {
      console.error('Error taking photo:', error);
      throw error;
    }
  }

  /**
   * Record video with camera
   */
  async recordVideo(cameraRef, options = {}) {
    try {
      if (!this.hasPermissions) {
        throw new Error('Camera permissions not granted');
      }

      if (!cameraRef.current) {
        throw new Error('Camera reference not available');
      }

      const defaultOptions = {
        quality: Camera.Constants.VideoQuality['720p'],
        maxDuration: 300, // 5 minutes
        mute: false
      };

      const video = await cameraRef.current.recordAsync({
        ...defaultOptions,
        ...options
      });

      // Add metadata
      const videoWithMetadata = {
        ...video,
        timestamp: new Date().toISOString(),
        type: 'video',
        source: 'camera'
      };

      // Save to device gallery
      if (options.saveToGallery !== false) {
        await MediaLibrary.saveToLibraryAsync(video.uri);
      }

      return videoWithMetadata;
    } catch (error) {
      console.error('Error recording video:', error);
      throw error;
    }
  }

  /**
   * Stop video recording
   */
  async stopRecording(cameraRef) {
    try {
      if (cameraRef.current) {
        cameraRef.current.stopRecording();
      }
    } catch (error) {
      console.error('Error stopping video recording:', error);
      throw error;
    }
  }

  /**
   * Pick image from gallery
   */
  async pickImageFromGallery(options = {}) {
    try {
      const permissions = await this.checkPermissions();
      if (!permissions.mediaLibrary) {
        throw new Error('Media library permission not granted');
      }

      const defaultOptions = {
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
        exif: true
      };

      const result = await ImagePicker.launchImageLibraryAsync({
        ...defaultOptions,
        ...options
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        return {
          ...asset,
          timestamp: new Date().toISOString(),
          type: 'photo',
          source: 'gallery'
        };
      }

      return null;
    } catch (error) {
      console.error('Error picking image from gallery:', error);
      throw error;
    }
  }

  /**
   * Pick video from gallery
   */
  async pickVideoFromGallery(options = {}) {
    try {
      const permissions = await this.checkPermissions();
      if (!permissions.mediaLibrary) {
        throw new Error('Media library permission not granted');
      }

      const defaultOptions = {
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        quality: 0.8,
        videoMaxDuration: 300 // 5 minutes
      };

      const result = await ImagePicker.launchImageLibraryAsync({
        ...defaultOptions,
        ...options
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        return {
          ...asset,
          timestamp: new Date().toISOString(),
          type: 'video',
          source: 'gallery'
        };
      }

      return null;
    } catch (error) {
      console.error('Error picking video from gallery:', error);
      throw error;
    }
  }

  /**
   * Compress image for upload
   */
  async compressImage(imageUri, options = {}) {
    try {
      const defaultOptions = {
        compress: 0.8,
        format: ImagePicker.ImageFormat.JPEG,
        base64: true
      };

      const result = await ImagePicker.manipulateAsync(
        imageUri,
        [], // No resize operations by default
        {
          ...defaultOptions,
          ...options
        }
      );

      return result;
    } catch (error) {
      console.error('Error compressing image:', error);
      throw error;
    }
  }

  /**
   * Get file info
   */
  async getFileInfo(uri) {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      return fileInfo;
    } catch (error) {
      console.error('Error getting file info:', error);
      throw error;
    }
  }

  /**
   * Upload photo to server
   */
  async uploadPhoto(photo, metadata = {}) {
    try {
      const uploadData = {
        file: photo.base64 ? `data:image/jpeg;base64,${photo.base64}` : photo.uri,
        fileName: `photo_${Date.now()}.jpg`,
        description: metadata.description || 'Photo captured from mobile app',
        reportId: metadata.reportId,
        shiftId: metadata.shiftId,
        location: metadata.location
      };

      if (OfflineService.isDeviceOnline()) {
        const response = await ApiService.post('/upload/photo', uploadData);
        return response;
      } else {
        // Store for offline sync
        await OfflineService.storeMediaUploadOffline(uploadData, 'photo');
        return {
          success: true,
          offline: true,
          message: 'Photo queued for upload when online'
        };
      }
    } catch (error) {
      console.error('Error uploading photo:', error);
      throw error;
    }
  }

  /**
   * Upload video to server
   */
  async uploadVideo(video, metadata = {}) {
    try {
      // For videos, we need to read the file as base64
      let base64Data;
      if (video.base64) {
        base64Data = video.base64;
      } else {
        const fileInfo = await FileSystem.readAsStringAsync(video.uri, {
          encoding: FileSystem.EncodingType.Base64
        });
        base64Data = fileInfo;
      }

      const uploadData = {
        file: `data:video/mp4;base64,${base64Data}`,
        fileName: `video_${Date.now()}.mp4`,
        description: metadata.description || 'Video captured from mobile app',
        reportId: metadata.reportId,
        shiftId: metadata.shiftId,
        location: metadata.location
      };

      if (OfflineService.isDeviceOnline()) {
        const response = await ApiService.post('/upload/video', uploadData);
        return response;
      } else {
        // Store for offline sync
        await OfflineService.storeMediaUploadOffline(uploadData, 'video');
        return {
          success: true,
          offline: true,
          message: 'Video queued for upload when online'
        };
      }
    } catch (error) {
      console.error('Error uploading video:', error);
      throw error;
    }
  }

  /**
   * Show media picker options
   */
  showMediaPickerOptions(onPhotoSelect, onVideoSelect) {
    Alert.alert(
      'Select Media',
      'Choose how you want to add media',
      [
        {
          text: 'Take Photo',
          onPress: () => onPhotoSelect('camera')
        },
        {
          text: 'Record Video',
          onPress: () => onVideoSelect('camera')
        },
        {
          text: 'Choose from Gallery',
          onPress: () => {
            Alert.alert(
              'Gallery',
              'Select media type',
              [
                {
                  text: 'Photo',
                  onPress: () => onPhotoSelect('gallery')
                },
                {
                  text: 'Video',
                  onPress: () => onVideoSelect('gallery')
                },
                { text: 'Cancel', style: 'cancel' }
              ]
            );
          }
        },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  }

  /**
   * Validate media file
   */
  validateMediaFile(media, type = 'photo') {
    const maxPhotoSize = 10 * 1024 * 1024; // 10MB
    const maxVideoSize = 100 * 1024 * 1024; // 100MB
    const maxVideoDuration = 300; // 5 minutes

    if (type === 'photo') {
      if (media.fileSize && media.fileSize > maxPhotoSize) {
        throw new Error('Photo size exceeds 10MB limit');
      }
    } else if (type === 'video') {
      if (media.fileSize && media.fileSize > maxVideoSize) {
        throw new Error('Video size exceeds 100MB limit');
      }
      if (media.duration && media.duration > maxVideoDuration) {
        throw new Error('Video duration exceeds 5 minute limit');
      }
    }

    return true;
  }

  /**
   * Get camera capabilities
   */
  async getCameraCapabilities() {
    try {
      const permissions = await this.checkPermissions();
      
      return {
        hasPermissions: permissions.hasAllPermissions,
        canTakePhotos: permissions.camera,
        canRecordVideos: permissions.camera,
        canAccessGallery: permissions.mediaLibrary,
        supportedRatios: ['4:3', '16:9'],
        supportedVideoQualities: [
          Camera.Constants.VideoQuality['480p'],
          Camera.Constants.VideoQuality['720p'],
          Camera.Constants.VideoQuality['1080p']
        ]
      };
    } catch (error) {
      console.error('Error getting camera capabilities:', error);
      return {
        hasPermissions: false,
        canTakePhotos: false,
        canRecordVideos: false,
        canAccessGallery: false
      };
    }
  }
}

// Export singleton instance
export default new CameraService();
