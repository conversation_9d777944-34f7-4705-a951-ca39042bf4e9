// BahinLink Location Service
// ⚠️ CRITICAL: Real GPS tracking and location updates ONLY

import Geolocation from 'react-native-geolocation-service';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import ApiService from './ApiService';
import { calculateDistance } from '@bahinlink/shared';

class LocationService {
  constructor() {
    this.watchId = null;
    this.lastKnownLocation = null;
    this.isTracking = false;
    this.updateInterval = 30000; // 30 seconds
    this.lastUpdateTime = 0;
    this.geofenceViolationAlerted = false;
  }

  /**
   * Initialize location service with real GPS tracking
   */
  async initialize() {
    try {
      console.log('Initializing LocationService with real GPS tracking...');
      
      // Check if location services are enabled
      const hasPermission = await this.hasLocationPermission();
      if (!hasPermission) {
        throw new Error('Location permission not granted');
      }

      // Get initial location
      await this.getCurrentLocation();
      
      // Start continuous tracking
      await this.startTracking();
      
      console.log('LocationService initialized successfully');
    } catch (error) {
      console.error('LocationService initialization error:', error);
      throw error;
    }
  }

  /**
   * Check if location permission is granted
   */
  async hasLocationPermission() {
    try {
      const result = await new Promise((resolve) => {
        Geolocation.getCurrentPosition(
          () => resolve(true),
          () => resolve(false),
          { enableHighAccuracy: false, timeout: 5000, maximumAge: 0 }
        );
      });
      return result;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current real GPS location
   */
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date(position.timestamp).toISOString(),
            speed: position.coords.speed,
            heading: position.coords.heading,
            altitude: position.coords.altitude
          };
          
          this.lastKnownLocation = location;
          this.saveLocationToStorage(location);
          resolve(location);
        },
        (error) => {
          console.error('Get current location error:', error);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
          distanceFilter: 5 // Update only if moved 5 meters
        }
      );
    });
  }

  /**
   * Start continuous real GPS tracking
   */
  async startTracking() {
    if (this.isTracking) {
      console.log('Location tracking already started');
      return;
    }

    try {
      this.watchId = Geolocation.watchPosition(
        (position) => {
          this.handleLocationUpdate(position);
        },
        (error) => {
          console.error('Location tracking error:', error);
          this.handleLocationError(error);
        },
        {
          enableHighAccuracy: true,
          distanceFilter: 10, // Update every 10 meters
          interval: this.updateInterval,
          fastestInterval: 15000, // Fastest update: 15 seconds
          forceRequestLocation: true,
          showLocationDialog: true,
          useSignificantChanges: false
        }
      );

      this.isTracking = true;
      console.log('Real GPS tracking started');
    } catch (error) {
      console.error('Start tracking error:', error);
      throw error;
    }
  }

  /**
   * Stop GPS tracking
   */
  stopTracking() {
    if (this.watchId !== null) {
      Geolocation.clearWatch(this.watchId);
      this.watchId = null;
      this.isTracking = false;
      console.log('GPS tracking stopped');
    }
  }

  /**
   * Handle real location updates
   */
  async handleLocationUpdate(position) {
    try {
      const location = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: new Date(position.timestamp).toISOString(),
        speed: position.coords.speed,
        heading: position.coords.heading,
        altitude: position.coords.altitude
      };

      // Validate GPS accuracy (reject if too inaccurate)
      if (location.accuracy > 50) {
        console.log(`GPS accuracy too low: ${location.accuracy}m, skipping update`);
        return;
      }

      // Check if enough time has passed since last update
      const now = Date.now();
      if (now - this.lastUpdateTime < this.updateInterval) {
        return;
      }

      // Check if location has changed significantly
      if (this.lastKnownLocation) {
        const distance = calculateDistance(
          this.lastKnownLocation.latitude,
          this.lastKnownLocation.longitude,
          location.latitude,
          location.longitude
        );
        
        // Skip update if moved less than 5 meters
        if (distance < 5) {
          return;
        }
      }

      this.lastKnownLocation = location;
      this.lastUpdateTime = now;

      // Save to local storage
      await this.saveLocationToStorage(location);

      // Send to real API server
      await this.sendLocationToServer(location);

      // Check geofence violations
      await this.checkGeofenceViolations(location);

      console.log(`Location updated: ${location.latitude}, ${location.longitude} (±${location.accuracy}m)`);
    } catch (error) {
      console.error('Handle location update error:', error);
    }
  }

  /**
   * Send real location to production server
   */
  async sendLocationToServer(location) {
    try {
      const response = await ApiService.post('/agents/me/location', {
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        timestamp: location.timestamp
      });

      if (response.success) {
        console.log('Location sent to server successfully');
      }
    } catch (error) {
      console.error('Send location to server error:', error);
      // Store for offline sync
      await this.storeLocationForOfflineSync(location);
    }
  }

  /**
   * Check for geofence violations with real site data
   */
  async checkGeofenceViolations(location) {
    try {
      // Get current shift from storage or API
      const currentShift = await this.getCurrentShift();
      if (!currentShift || !currentShift.site) {
        return;
      }

      const site = currentShift.site;
      const distance = calculateDistance(
        location.latitude,
        location.longitude,
        site.latitude,
        site.longitude
      );

      const withinGeofence = distance <= site.geofenceRadius;

      if (!withinGeofence && !this.geofenceViolationAlerted) {
        this.geofenceViolationAlerted = true;
        
        Alert.alert(
          'Geofence Alert',
          `You are ${Math.round(distance)}m away from ${site.name}. Please return to your assigned location.`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Reset alert flag after 5 minutes
                setTimeout(() => {
                  this.geofenceViolationAlerted = false;
                }, 300000);
              }
            }
          ]
        );

        // Send geofence violation to server
        await ApiService.post('/geofence/violations', {
          siteId: site.id,
          agentLocation: location,
          distance,
          violationType: 'OUTSIDE_GEOFENCE'
        });
      } else if (withinGeofence) {
        this.geofenceViolationAlerted = false;
      }
    } catch (error) {
      console.error('Geofence check error:', error);
    }
  }

  /**
   * Handle location errors
   */
  handleLocationError(error) {
    console.error('Location error:', error);
    
    switch (error.code) {
      case 1: // PERMISSION_DENIED
        Alert.alert(
          'Location Permission Denied',
          'Please enable location permissions for BahinLink to track your location.',
          [{ text: 'OK' }]
        );
        break;
      case 2: // POSITION_UNAVAILABLE
        Alert.alert(
          'Location Unavailable',
          'Unable to determine your location. Please check your GPS settings.',
          [{ text: 'OK' }]
        );
        break;
      case 3: // TIMEOUT
        console.log('Location request timed out, retrying...');
        // Retry after a delay
        setTimeout(() => {
          this.getCurrentLocation();
        }, 5000);
        break;
      default:
        console.log('Unknown location error:', error);
    }
  }

  /**
   * Save location to local storage
   */
  async saveLocationToStorage(location) {
    try {
      await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(location));
    } catch (error) {
      console.error('Save location to storage error:', error);
    }
  }

  /**
   * Get last known location from storage
   */
  async getLastKnownLocation() {
    try {
      const locationStr = await AsyncStorage.getItem('lastKnownLocation');
      return locationStr ? JSON.parse(locationStr) : null;
    } catch (error) {
      console.error('Get last known location error:', error);
      return null;
    }
  }

  /**
   * Store location for offline sync
   */
  async storeLocationForOfflineSync(location) {
    try {
      const offlineLocations = await AsyncStorage.getItem('offlineLocations');
      const locations = offlineLocations ? JSON.parse(offlineLocations) : [];
      
      locations.push({
        ...location,
        syncPending: true,
        createdAt: new Date().toISOString()
      });

      // Keep only last 100 locations
      if (locations.length > 100) {
        locations.splice(0, locations.length - 100);
      }

      await AsyncStorage.setItem('offlineLocations', JSON.stringify(locations));
    } catch (error) {
      console.error('Store location for offline sync error:', error);
    }
  }

  /**
   * Get current shift data
   */
  async getCurrentShift() {
    try {
      const shiftStr = await AsyncStorage.getItem('currentShift');
      return shiftStr ? JSON.parse(shiftStr) : null;
    } catch (error) {
      console.error('Get current shift error:', error);
      return null;
    }
  }

  /**
   * Calculate distance to site
   */
  calculateDistanceToSite(siteLocation) {
    if (!this.lastKnownLocation) {
      return null;
    }

    return calculateDistance(
      this.lastKnownLocation.latitude,
      this.lastKnownLocation.longitude,
      siteLocation.latitude,
      siteLocation.longitude
    );
  }

  /**
   * Get location service status
   */
  getStatus() {
    return {
      isTracking: this.isTracking,
      lastKnownLocation: this.lastKnownLocation,
      lastUpdateTime: this.lastUpdateTime,
      watchId: this.watchId
    };
  }
}

export default new LocationService();
