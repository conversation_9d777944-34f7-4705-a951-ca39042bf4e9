// BahinLink API Service
// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '@clerk/clerk-react-native';

// Real production API base URL
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://api.bahinlink.com/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = null;
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for real authentication
   */
  setupInterceptors() {
    // Request interceptor to add real Clerk token
    axios.interceptors.request.use(
      async (config) => {
        try {
          // Get real Clerk token
          const token = await this.getAuthToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          
          config.headers['Content-Type'] = 'application/json';
          config.baseURL = this.baseURL;
          
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
          return config;
        } catch (error) {
          console.error('Request interceptor error:', error);
          return config;
        }
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    axios.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response.data;
      },
      async (error) => {
        console.error('API Error:', error.response?.data || error.message);
        
        if (error.response?.status === 401) {
          // Token expired or invalid
          await this.handleAuthError();
        }
        
        // Store failed request for offline sync
        if (!error.response) {
          await this.storeFailedRequest(error.config);
        }
        
        return Promise.reject(error.response?.data || error);
      }
    );
  }

  /**
   * Get real Clerk authentication token
   */
  async getAuthToken() {
    try {
      // This will be called from components with useAuth hook
      // For now, get from storage
      const token = await AsyncStorage.getItem('clerkToken');
      return token;
    } catch (error) {
      console.error('Get auth token error:', error);
      return null;
    }
  }

  /**
   * Set authentication token
   */
  async setAuthToken(token) {
    try {
      this.token = token;
      await AsyncStorage.setItem('clerkToken', token);
    } catch (error) {
      console.error('Set auth token error:', error);
    }
  }

  /**
   * Handle authentication errors
   */
  async handleAuthError() {
    try {
      // Clear stored token
      await AsyncStorage.removeItem('clerkToken');
      this.token = null;
      
      // Redirect to login (handled by Clerk)
      console.log('Authentication error - redirecting to login');
    } catch (error) {
      console.error('Handle auth error:', error);
    }
  }

  /**
   * Store failed request for offline sync
   */
  async storeFailedRequest(config) {
    try {
      const failedRequests = await AsyncStorage.getItem('failedRequests');
      const requests = failedRequests ? JSON.parse(failedRequests) : [];
      
      requests.push({
        url: config.url,
        method: config.method,
        data: config.data,
        headers: config.headers,
        timestamp: new Date().toISOString()
      });

      // Keep only last 50 failed requests
      if (requests.length > 50) {
        requests.splice(0, requests.length - 50);
      }

      await AsyncStorage.setItem('failedRequests', JSON.stringify(requests));
    } catch (error) {
      console.error('Store failed request error:', error);
    }
  }

  /**
   * GET request to real API
   */
  async get(endpoint, params = {}) {
    try {
      const response = await axios.get(endpoint, { params });
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST request to real API
   */
  async post(endpoint, data = {}) {
    try {
      const response = await axios.post(endpoint, data);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT request to real API
   */
  async put(endpoint, data = {}) {
    try {
      const response = await axios.put(endpoint, data);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * DELETE request to real API
   */
  async delete(endpoint) {
    try {
      const response = await axios.delete(endpoint);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upload file to real storage
   */
  async uploadFile(endpoint, file, onProgress = null) {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: file.uri,
        type: file.type,
        name: file.name || 'file'
      });

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const progress = (progressEvent.loaded / progressEvent.total) * 100;
            onProgress(progress);
          }
        }
      };

      const response = await axios.post(endpoint, formData, config);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // User Profile APIs
  async getUserProfile() {
    return this.get('/auth/profile');
  }

  async updateUserProfile(data) {
    return this.put('/auth/profile', data);
  }

  // Agent Location APIs
  async updateAgentLocation(location) {
    return this.post('/agents/me/location', location);
  }

  async getAgentLocation() {
    return this.get('/agents/me/location');
  }

  // Time Tracking APIs
  async clockIn(data) {
    return this.post('/time/clock-in', data);
  }

  async clockOut(data) {
    return this.post('/time/clock-out', data);
  }

  async getTimeEntries(params = {}) {
    return this.get('/time/entries', params);
  }

  // Shifts APIs
  async getMyShifts(params = {}) {
    return this.get('/shifts/me', params);
  }

  async getShift(shiftId) {
    return this.get(`/shifts/${shiftId}`);
  }

  async startShift(shiftId) {
    return this.post(`/shifts/${shiftId}/start`);
  }

  async endShift(shiftId) {
    return this.post(`/shifts/${shiftId}/end`);
  }

  // Reports APIs
  async getReports(params = {}) {
    return this.get('/reports', params);
  }

  async createReport(data) {
    return this.post('/reports', data);
  }

  async updateReport(reportId, data) {
    return this.put(`/reports/${reportId}`, data);
  }

  async submitReport(reportId) {
    return this.post(`/reports/${reportId}/submit`);
  }

  async addReportSignature(reportId, signature) {
    return this.post(`/reports/${reportId}/signature`, signature);
  }

  // File Upload APIs
  async uploadPhoto(file, onProgress) {
    return this.uploadFile('/upload/photo', file, onProgress);
  }

  async uploadVideo(file, onProgress) {
    return this.uploadFile('/upload/video', file, onProgress);
  }

  async uploadDocument(file, onProgress) {
    return this.uploadFile('/upload/document', file, onProgress);
  }

  // Notifications APIs
  async getNotifications(params = {}) {
    return this.get('/notifications', params);
  }

  async markNotificationRead(notificationId) {
    return this.put(`/notifications/${notificationId}/read`);
  }

  // Communications APIs
  async getMessages(params = {}) {
    return this.get('/communications', params);
  }

  async sendMessage(data) {
    return this.post('/communications', data);
  }

  async markMessageRead(messageId) {
    return this.put(`/communications/${messageId}/read`);
  }

  // Geofence APIs
  async checkGeofence(data) {
    return this.post('/geofence/check', data);
  }

  async getGeofenceViolations(params = {}) {
    return this.get('/geofence/violations', params);
  }

  // Offline sync
  async syncOfflineData() {
    try {
      console.log('Starting offline data sync...');
      
      // Sync failed requests
      await this.syncFailedRequests();
      
      // Sync offline locations
      await this.syncOfflineLocations();
      
      console.log('Offline data sync completed');
    } catch (error) {
      console.error('Offline sync error:', error);
    }
  }

  async syncFailedRequests() {
    try {
      const failedRequests = await AsyncStorage.getItem('failedRequests');
      if (!failedRequests) return;

      const requests = JSON.parse(failedRequests);
      const syncedRequests = [];

      for (const request of requests) {
        try {
          await axios({
            method: request.method,
            url: request.url,
            data: request.data,
            headers: request.headers
          });
          syncedRequests.push(request);
        } catch (error) {
          console.error('Failed to sync request:', error);
        }
      }

      // Remove synced requests
      const remainingRequests = requests.filter(req => !syncedRequests.includes(req));
      await AsyncStorage.setItem('failedRequests', JSON.stringify(remainingRequests));
      
      console.log(`Synced ${syncedRequests.length} failed requests`);
    } catch (error) {
      console.error('Sync failed requests error:', error);
    }
  }

  async syncOfflineLocations() {
    try {
      const offlineLocations = await AsyncStorage.getItem('offlineLocations');
      if (!offlineLocations) return;

      const locations = JSON.parse(offlineLocations);
      const syncedLocations = [];

      for (const location of locations) {
        if (location.syncPending) {
          try {
            await this.updateAgentLocation({
              latitude: location.latitude,
              longitude: location.longitude,
              accuracy: location.accuracy,
              timestamp: location.timestamp
            });
            syncedLocations.push(location);
          } catch (error) {
            console.error('Failed to sync location:', error);
          }
        }
      }

      // Remove synced locations
      const remainingLocations = locations.filter(loc => !syncedLocations.includes(loc));
      await AsyncStorage.setItem('offlineLocations', JSON.stringify(remainingLocations));
      
      console.log(`Synced ${syncedLocations.length} offline locations`);
    } catch (error) {
      console.error('Sync offline locations error:', error);
    }
  }
}

export default new ApiService();
