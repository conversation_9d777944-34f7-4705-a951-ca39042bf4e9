// BahinLink Mobile Socket Service
// ⚠️ CRITICAL: Real-time communication for mobile app ONLY

import { io } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.listeners = new Map();
  }

  /**
   * Initialize socket connection
   */
  async initialize() {
    try {
      const token = await AsyncStorage.getItem('clerk_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const serverUrl = __DEV__ 
        ? 'http://localhost:3000'
        : 'https://api.bahinlink.com';

      this.socket = io(serverUrl, {
        auth: {
          token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true
      });

      this.setupEventHandlers();
      
      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          console.log('Socket connected successfully');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error);
          this.isConnected = false;
          reject(error);
        });
      });

    } catch (error) {
      console.error('Socket initialization error:', error);
      throw error;
    }
  }

  /**
   * Setup socket event handlers
   */
  setupEventHandlers() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.notifyListeners('connection', { connected: true });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
      this.notifyListeners('connection', { connected: false, reason });
      
      // Auto-reconnect for certain disconnect reasons
      if (reason === 'io server disconnect') {
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
      this.notifyListeners('connection', { connected: false, error: error.message });
    });

    // Location update confirmations
    this.socket.on('location-update-confirmed', (data) => {
      this.notifyListeners('location-update-confirmed', data);
    });

    // Shift status confirmations
    this.socket.on('shift-status-change-confirmed', (data) => {
      this.notifyListeners('shift-status-change-confirmed', data);
    });

    // Emergency alert confirmations
    this.socket.on('emergency-alert-confirmed', (data) => {
      this.notifyListeners('emergency-alert-confirmed', data);
    });

    // Incoming messages from supervisors/admins
    this.socket.on('message', (data) => {
      this.notifyListeners('message', data);
      
      // Show notification if app is in background
      this.showNotification('New Message', data.message);
    });

    // Geofence violation alerts
    this.socket.on('geofence-violation', (data) => {
      this.notifyListeners('geofence-violation', data);
      
      Alert.alert(
        'Geofence Alert',
        `You are ${data.distance}m away from your assigned site. Please return to the designated area.`,
        [{ text: 'OK' }]
      );
    });

    // Shift updates from admin
    this.socket.on('shift-update', (data) => {
      this.notifyListeners('shift-update', data);
      
      this.showNotification('Shift Update', `Your shift has been updated: ${data.message}`);
    });

    // Emergency alerts from other agents
    this.socket.on('emergency-alert', (data) => {
      this.notifyListeners('emergency-alert', data);
      
      if (data.priority === 'HIGH') {
        Alert.alert(
          'Emergency Alert',
          `Emergency reported by ${data.userName}: ${data.message}`,
          [{ text: 'OK' }]
        );
      }
    });

    // Error handling
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
      this.notifyListeners('error', error);
      
      Alert.alert('Connection Error', error.message || 'An error occurred');
    });
  }

  /**
   * Send location update
   */
  sendLocationUpdate(locationData) {
    if (!this.isConnected || !this.socket) {
      console.warn('Socket not connected, cannot send location update');
      return false;
    }

    try {
      this.socket.emit('location-update', {
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp || Date.now()
      });
      
      return true;
    } catch (error) {
      console.error('Send location update error:', error);
      return false;
    }
  }

  /**
   * Send shift status change
   */
  sendShiftStatusChange(shiftId, status, timestamp = null) {
    if (!this.isConnected || !this.socket) {
      console.warn('Socket not connected, cannot send shift status change');
      return false;
    }

    try {
      this.socket.emit('shift-status-change', {
        shiftId,
        status,
        timestamp: timestamp || Date.now()
      });
      
      return true;
    } catch (error) {
      console.error('Send shift status change error:', error);
      return false;
    }
  }

  /**
   * Send emergency alert
   */
  sendEmergencyAlert(message, location = null, priority = 'HIGH') {
    if (!this.isConnected || !this.socket) {
      console.warn('Socket not connected, cannot send emergency alert');
      return false;
    }

    try {
      this.socket.emit('emergency-alert', {
        message,
        latitude: location?.latitude,
        longitude: location?.longitude,
        priority,
        timestamp: Date.now()
      });
      
      return true;
    } catch (error) {
      console.error('Send emergency alert error:', error);
      return false;
    }
  }

  /**
   * Send report submission notification
   */
  sendReportSubmitted(reportId) {
    if (!this.isConnected || !this.socket) {
      console.warn('Socket not connected, cannot send report notification');
      return false;
    }

    try {
      this.socket.emit('report-submitted', {
        reportId,
        timestamp: Date.now()
      });
      
      return true;
    } catch (error) {
      console.error('Send report submitted error:', error);
      return false;
    }
  }

  /**
   * Send chat message
   */
  sendMessage(message, recipientId = null, recipientRole = null) {
    if (!this.isConnected || !this.socket) {
      console.warn('Socket not connected, cannot send message');
      return false;
    }

    try {
      this.socket.emit('message', {
        message,
        recipientId,
        recipientRole,
        timestamp: Date.now()
      });
      
      return true;
    } catch (error) {
      console.error('Send message error:', error);
      return false;
    }
  }

  /**
   * Add event listener
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Notify all listeners for an event
   */
  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Listener callback error:', error);
        }
      });
    }
  }

  /**
   * Reconnect to socket
   */
  async reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    try {
      await new Promise(resolve => setTimeout(resolve, 2000 * this.reconnectAttempts));
      await this.initialize();
    } catch (error) {
      console.error('Reconnection failed:', error);
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnect();
      }
    }
  }

  /**
   * Show local notification
   */
  showNotification(title, message) {
    // This would integrate with react-native-push-notification
    // or @react-native-async-storage/async-storage for local notifications
    console.log(`Notification: ${title} - ${message}`);
  }

  /**
   * Disconnect socket
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    };
  }

  /**
   * Check if socket is connected
   */
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }
}

// Export singleton instance
export default new SocketService();
