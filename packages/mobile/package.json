{"name": "@bahinlink/mobile", "version": "1.0.0", "description": "BahinLink Mobile App - React Native with real GPS tracking and offline capabilities", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace BahinLinkApp.xcworkspace -scheme BahinLinkApp -configuration Release -destination generic/platform=iOS -archivePath BahinLinkApp.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.7", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@clerk/clerk-react-native": "^0.19.14", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.8.2", "react-native-gesture-handler": "^2.14.0", "react-native-reanimated": "^3.6.1", "react-native-maps": "^1.8.0", "react-native-geolocation-service": "^5.3.1", "react-native-permissions": "^4.0.3", "react-native-camera": "^4.2.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-image-picker": "^7.1.0", "react-native-document-picker": "^9.1.1", "@react-native-async-storage/async-storage": "^1.21.0", "react-native-sqlite-storage": "^6.0.1", "react-native-push-notification": "^8.1.1", "@react-native-firebase/app": "^18.6.2", "@react-native-firebase/messaging": "^18.6.2", "react-native-vector-icons": "^10.0.3", "react-native-paper": "^5.11.6", "react-native-elements": "^3.4.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-native-signature-canvas": "^4.7.2", "react-native-fs": "^2.20.0", "react-native-share": "^10.0.2", "react-native-device-info": "^10.11.0", "react-native-network-info": "^5.2.1", "react-native-background-job": "^0.2.9", "react-native-background-timer": "^2.4.1", "date-fns": "^3.0.6", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}