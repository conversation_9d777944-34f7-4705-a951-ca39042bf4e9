// BahinLink Mobile App
// ⚠️ CRITICAL: Real GPS tracking, Clerk authentication, and production data integration ONLY

import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { ClerkProvider, useAuth } from '@clerk/clerk-react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { PermissionsAndroid, Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Screens
import LoginScreen from './src/screens/LoginScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import TimeTrackingScreen from './src/screens/TimeTrackingScreen';
import ReportsScreen from './src/screens/ReportsScreen';
import LocationScreen from './src/screens/LocationScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import ShiftsScreen from './src/screens/ShiftsScreen';
import CameraScreen from './src/screens/CameraScreen';
import QRScannerScreen from './src/screens/QRScannerScreen';

// Services
import LocationService from './src/services/LocationService';
import NotificationService from './src/services/NotificationService';
import OfflineService from './src/services/OfflineService';

// Theme
import { theme } from './src/theme/theme';

const Stack = createStackNavigator();

// Real Clerk publishable key - MUST be production key
const CLERK_PUBLISHABLE_KEY = 'pk_live_Y2xlcmsuYmFoaW5saW5rLmNvbSQ'; // Replace with actual production key

function AppNavigator() {
  const { isSignedIn, isLoaded } = useAuth();

  if (!isLoaded) {
    return null; // Loading screen would go here
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      {isSignedIn ? (
        // Authenticated screens
        <>
          <Stack.Screen 
            name="Dashboard" 
            component={DashboardScreen}
            options={{ title: 'BahinLink Dashboard' }}
          />
          <Stack.Screen 
            name="TimeTracking" 
            component={TimeTrackingScreen}
            options={{ title: 'Time Tracking' }}
          />
          <Stack.Screen 
            name="Reports" 
            component={ReportsScreen}
            options={{ title: 'Reports' }}
          />
          <Stack.Screen 
            name="Location" 
            component={LocationScreen}
            options={{ title: 'Location Tracking' }}
          />
          <Stack.Screen 
            name="Profile" 
            component={ProfileScreen}
            options={{ title: 'Profile' }}
          />
          <Stack.Screen 
            name="Shifts" 
            component={ShiftsScreen}
            options={{ title: 'My Shifts' }}
          />
          <Stack.Screen 
            name="Camera" 
            component={CameraScreen}
            options={{ title: 'Take Photo' }}
          />
          <Stack.Screen 
            name="QRScanner" 
            component={QRScannerScreen}
            options={{ title: 'Scan QR Code' }}
          />
        </>
      ) : (
        // Unauthenticated screens
        <Stack.Screen 
          name="Login" 
          component={LoginScreen}
          options={{ headerShown: false }}
        />
      )}
    </Stack.Navigator>
  );
}

function App() {
  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Request permissions for real GPS tracking
      await requestLocationPermissions();
      
      // Request camera permissions for real photo capture
      await requestCameraPermissions();
      
      // Initialize real notification service
      await NotificationService.initialize();
      
      // Initialize offline service for real data sync
      await OfflineService.initialize();
      
      // Start real location tracking service
      await LocationService.initialize();
      
    } catch (error) {
      console.error('App initialization error:', error);
      Alert.alert(
        'Initialization Error',
        'Failed to initialize app services. Some features may not work properly.',
        [{ text: 'OK' }]
      );
    }
  };

  const requestLocationPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
        ]);

        const fineLocationGranted = granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === 'granted';
        const coarseLocationGranted = granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === 'granted';

        if (!fineLocationGranted || !coarseLocationGranted) {
          Alert.alert(
            'Location Permission Required',
            'BahinLink requires location access for real-time tracking and geofencing. Please enable location permissions in settings.',
            [{ text: 'OK' }]
          );
        }
      } catch (error) {
        console.error('Location permission error:', error);
      }
    }
  };

  const requestCameraPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        ]);

        const cameraGranted = granted[PermissionsAndroid.PERMISSIONS.CAMERA] === 'granted';

        if (!cameraGranted) {
          Alert.alert(
            'Camera Permission Required',
            'BahinLink requires camera access for taking photos in reports. Please enable camera permissions in settings.',
            [{ text: 'OK' }]
          );
        }
      } catch (error) {
        console.error('Camera permission error:', error);
      }
    }
  };

  return (
    <ClerkProvider publishableKey={CLERK_PUBLISHABLE_KEY}>
      <PaperProvider theme={theme}>
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
      </PaperProvider>
    </ClerkProvider>
  );
}

export default App;
