// BahinLink Shared Types
// ⚠️ CRITICAL: All types must support REAL PRODUCTION DATA ONLY

export enum UserRole {
  ADMIN = 'ADMIN',
  SUPERVISOR = 'SUPERVISOR',
  AGENT = 'AGENT',
  CLIENT = 'CLIENT'
}

export enum ShiftStatus {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW'
}

export enum ClockMethod {
  GPS = 'GPS',
  QR_CODE = 'QR_CODE',
  MANUAL = 'MANUAL',
  NFC = 'NFC'
}

export enum ReportType {
  PATROL = 'PATROL',
  INCIDENT = 'INCIDENT',
  INSPECTION = 'INSPECTION',
  MAINTENANCE = 'MAINTENANCE'
}

export enum ReportStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ARCHIVED = 'ARCHIVED'
}

export enum Priority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
  EMERGENCY = 'EMERGENCY'
}

export enum DeliveryMethod {
  APP = 'APP',
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PUSH = 'PUSH'
}

export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  VOICE = 'VOICE',
  VIDEO = 'VIDEO'
}

export enum ClientRequestStatus {
  OPEN = 'OPEN',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

// Core Data Types
export interface User {
  id: string;
  clerkId: string;
  email: string;
  role: UserRole;
  firstName: string;
  lastName: string;
  phone?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Agent {
  id: string;
  userId: string;
  employeeId: string;
  photoUrl?: string;
  certifications: string[];
  skills: string[];
  availability: Record<string, any>;
  performanceStats: Record<string, any>;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  hireDate?: Date;
  hourlyRate?: number;
  isAvailable: boolean;
  currentLatitude?: number;
  currentLongitude?: number;
  lastLocationUpdate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Client {
  id: string;
  userId: string;
  companyName: string;
  contactPerson?: string;
  billingAddress?: string;
  serviceLevel: string;
  contractStartDate?: Date;
  contractEndDate?: Date;
  billingCycle: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Site {
  id: string;
  clientId: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  geofenceRadius: number;
  qrCode?: string;
  siteType?: string;
  specialInstructions?: string;
  accessCodes: Record<string, any>;
  emergencyContacts: any[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Shift {
  id: string;
  siteId: string;
  agentId?: string;
  supervisorId?: string;
  shiftDate: Date;
  startTime: Date;
  endTime: Date;
  status: ShiftStatus;
  actualStartTime?: Date;
  actualEndTime?: Date;
  breakDuration: number;
  overtimeHours: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimeEntry {
  id: string;
  shiftId: string;
  agentId: string;
  clockInTime?: Date;
  clockOutTime?: Date;
  clockInLatitude?: number;
  clockInLongitude?: number;
  clockOutLatitude?: number;
  clockOutLongitude?: number;
  clockInMethod?: ClockMethod;
  clockOutMethod?: ClockMethod;
  clockInAccuracy?: number;
  clockOutAccuracy?: number;
  totalHours?: number;
  isVerified: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;
  discrepancyNotes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Report {
  id: string;
  type: ReportType;
  shiftId?: string;
  siteId: string;
  agentId: string;
  supervisorId?: string;
  title: string;
  description?: string;
  observations?: string;
  incidents?: string;
  actionsTaken?: string;
  recommendations?: string;
  status: ReportStatus;
  priority: Priority;
  clientSignature?: any;
  clientFeedback?: string;
  photos: string[];
  videos: string[];
  attachments: string[];
  latitude?: number;
  longitude?: number;
  weatherConditions?: string;
  temperature?: number;
  submittedAt?: Date;
  approvedAt?: Date;
  rejectedAt?: Date;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  id: string;
  recipientId: string;
  senderId?: string;
  type: string;
  title: string;
  message: string;
  data: Record<string, any>;
  isRead: boolean;
  priority: Priority;
  deliveryMethod: DeliveryMethod;
  scheduledFor?: Date;
  deliveredAt?: Date;
  createdAt: Date;
  readAt?: Date;
}

export interface Communication {
  id: string;
  senderId: string;
  recipientId: string;
  threadId?: string;
  messageType: MessageType;
  content: string;
  attachments: string[];
  isRead: boolean;
  isUrgent: boolean;
  replyToId?: string;
  createdAt: Date;
  readAt?: Date;
  editedAt?: Date;
}

export interface ClientRequest {
  id: string;
  clientId: string;
  siteId: string;
  requestType: string;
  priority: Priority;
  title: string;
  description: string;
  status: ClientRequestStatus;
  assignedToId?: string;
  estimatedCompletion?: Date;
  actualCompletion?: Date;
  clientSatisfactionRating?: number;
  clientFeedback?: string;
  internalNotes?: string;
  costEstimate?: number;
  actualCost?: number;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}

export interface GeofenceViolation {
  id: string;
  agentId: string;
  siteId: string;
  shiftId?: string;
  violationType: string;
  agentLatitude: number;
  agentLongitude: number;
  distanceFromSite: number;
  durationMinutes?: number;
  isResolved: boolean;
  resolutionNotes?: string;
  createdAt: Date;
  resolvedAt?: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  message?: string;
  timestamp: string;
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: string;
}

export interface GeofenceCheck {
  withinGeofence: boolean;
  distance: number;
  geofenceRadius: number;
}

// Real-time Event Types
export interface LocationUpdateEvent {
  agentId: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: Date;
}

export interface NotificationEvent {
  notification: Notification;
}

export interface ShiftStatusChangeEvent {
  shiftId: string;
  status: ShiftStatus;
  agentId?: string;
  timestamp: Date;
}

export interface ReportSubmittedEvent {
  reportId: string;
  agentId: string;
  supervisorId?: string;
  type: ReportType;
  priority: Priority;
  timestamp: Date;
}

export interface EmergencyAlertEvent {
  agentId: string;
  siteId: string;
  location: Location;
  message: string;
  timestamp: Date;
}

export interface GeofenceViolationEvent {
  violation: GeofenceViolation;
  agent: Agent;
  site: Site;
}

// File Upload Types
export interface FileUpload {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  uploadedAt: Date;
}
