// BahinLink Shared Utilities
// ⚠️ CRITICAL: All utilities must work with REAL PRODUCTION DATA ONLY

import { format, parseISO, isValid } from 'date-fns';
import { Location, GeofenceCheck } from './types';

/**
 * Calculate distance between two GPS coordinates using Haversine formula
 * @param lat1 Latitude of first point
 * @param lon1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lon2 Longitude of second point
 * @returns Distance in meters
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

/**
 * Check if a location is within a geofence
 * @param userLocation User's current location
 * @param centerLocation Center of the geofence
 * @param radiusMeters Radius of the geofence in meters
 * @returns GeofenceCheck result
 */
export function checkGeofence(
  userLocation: Location,
  centerLocation: Location,
  radiusMeters: number
): GeofenceCheck {
  const distance = calculateDistance(
    userLocation.latitude,
    userLocation.longitude,
    centerLocation.latitude,
    centerLocation.longitude
  );

  return {
    withinGeofence: distance <= radiusMeters,
    distance,
    geofenceRadius: radiusMeters
  };
}

/**
 * Format date for display
 * @param date Date to format
 * @param formatString Format string (default: 'PPP')
 * @returns Formatted date string
 */
export function formatDate(date: Date | string, formatString = 'PPP'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(dateObj)) return 'Invalid Date';
  return format(dateObj, formatString);
}

/**
 * Format time for display
 * @param date Date to format
 * @param formatString Format string (default: 'p')
 * @returns Formatted time string
 */
export function formatTime(date: Date | string, formatString = 'p'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(dateObj)) return 'Invalid Time';
  return format(dateObj, formatString);
}

/**
 * Format duration in hours and minutes
 * @param hours Duration in hours (decimal)
 * @returns Formatted duration string
 */
export function formatDuration(hours: number): string {
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  if (wholeHours === 0) {
    return `${minutes}m`;
  }
  
  if (minutes === 0) {
    return `${wholeHours}h`;
  }
  
  return `${wholeHours}h ${minutes}m`;
}

/**
 * Calculate total hours between two dates
 * @param startTime Start time
 * @param endTime End time
 * @returns Total hours (decimal)
 */
export function calculateHours(startTime: Date, endTime: Date): number {
  const diffMs = endTime.getTime() - startTime.getTime();
  return diffMs / (1000 * 60 * 60); // Convert to hours
}

/**
 * Validate email address
 * @param email Email to validate
 * @returns True if valid email
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number (basic validation)
 * @param phone Phone number to validate
 * @returns True if valid phone number
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

/**
 * Generate QR code data for site check-in
 * @param siteId Site ID
 * @param shiftId Shift ID (optional)
 * @returns QR code data string
 */
export function generateQRCodeData(siteId: string, shiftId?: string): string {
  const data = {
    type: 'site_checkin',
    siteId,
    shiftId,
    timestamp: new Date().toISOString()
  };
  return JSON.stringify(data);
}

/**
 * Parse QR code data
 * @param qrData QR code data string
 * @returns Parsed QR data or null if invalid
 */
export function parseQRCodeData(qrData: string): any | null {
  try {
    const data = JSON.parse(qrData);
    if (data.type === 'site_checkin' && data.siteId) {
      return data;
    }
    return null;
  } catch {
    return null;
  }
}

/**
 * Get file extension from filename
 * @param filename Filename
 * @returns File extension (lowercase)
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Check if file type is allowed
 * @param mimeType MIME type of the file
 * @param allowedTypes Array of allowed MIME types
 * @returns True if file type is allowed
 */
export function isAllowedFileType(mimeType: string, allowedTypes: string[]): boolean {
  return allowedTypes.includes(mimeType);
}

/**
 * Format file size for display
 * @param bytes File size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Debounce function
 * @param func Function to debounce
 * @param wait Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function
 * @param func Function to throttle
 * @param limit Time limit in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Generate unique ID
 * @returns Unique ID string
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Capitalize first letter of string
 * @param str String to capitalize
 * @returns Capitalized string
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert enum value to display string
 * @param enumValue Enum value
 * @returns Display string
 */
export function enumToDisplayString(enumValue: string): string {
  return enumValue
    .split('_')
    .map(word => capitalize(word))
    .join(' ');
}

/**
 * Check if current time is within shift hours
 * @param shiftStart Shift start time
 * @param shiftEnd Shift end time
 * @param currentTime Current time (optional, defaults to now)
 * @returns True if within shift hours
 */
export function isWithinShiftHours(
  shiftStart: Date,
  shiftEnd: Date,
  currentTime: Date = new Date()
): boolean {
  return currentTime >= shiftStart && currentTime <= shiftEnd;
}

/**
 * Calculate shift progress percentage
 * @param shiftStart Shift start time
 * @param shiftEnd Shift end time
 * @param currentTime Current time (optional, defaults to now)
 * @returns Progress percentage (0-100)
 */
export function calculateShiftProgress(
  shiftStart: Date,
  shiftEnd: Date,
  currentTime: Date = new Date()
): number {
  const totalDuration = shiftEnd.getTime() - shiftStart.getTime();
  const elapsed = currentTime.getTime() - shiftStart.getTime();
  
  if (elapsed <= 0) return 0;
  if (elapsed >= totalDuration) return 100;
  
  return Math.round((elapsed / totalDuration) * 100);
}
