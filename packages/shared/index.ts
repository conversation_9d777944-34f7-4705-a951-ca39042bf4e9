// BahinLink Shared Package Entry Point
// ⚠️ CRITICAL: All exports must support REAL PRODUCTION DATA ONLY

export * from './types';
export * from './utils';

// Constants
export const API_ENDPOINTS = {
  // Authentication
  AUTH_PROFILE: '/api/auth/profile',
  AUTH_SETUP_AGENT: '/api/auth/setup-agent',
  AUTH_SETUP_CLIENT: '/api/auth/setup-client',
  
  // Users
  USERS: '/api/users',
  USER_BY_ID: (id: string) => `/api/users/${id}`,
  
  // Agents
  AGENTS: '/api/agents',
  AGENT_BY_ID: (id: string) => `/api/agents/${id}`,
  AGENT_LOCATION: '/api/agents/me/location',
  AGENTS_NEARBY: '/api/agents/nearby',
  
  // Sites
  SITES: '/api/sites',
  SITE_BY_ID: (id: string) => `/api/sites/${id}`,
  SITE_QR_GENERATE: (id: string) => `/api/sites/${id}/generate-qr`,
  
  // Shifts
  SHIFTS: '/api/shifts',
  SHIFT_BY_ID: (id: string) => `/api/shifts/${id}`,
  SHIFTS_ME: '/api/shifts/me',
  SHIFT_START: (id: string) => `/api/shifts/${id}/start`,
  SHIFT_END: (id: string) => `/api/shifts/${id}/end`,
  
  // Time Tracking
  TIME_CLOCK_IN: '/api/time/clock-in',
  TIME_CLOCK_OUT: '/api/time/clock-out',
  TIME_ENTRIES: '/api/time/entries',
  TIME_ENTRY_VERIFY: (id: string) => `/api/time/entries/${id}/verify`,
  
  // Reports
  REPORTS: '/api/reports',
  REPORT_BY_ID: (id: string) => `/api/reports/${id}`,
  REPORT_SUBMIT: (id: string) => `/api/reports/${id}/submit`,
  REPORT_APPROVE: (id: string) => `/api/reports/${id}/approve`,
  REPORT_REJECT: (id: string) => `/api/reports/${id}/reject`,
  REPORT_SIGNATURE: (id: string) => `/api/reports/${id}/signature`,
  
  // File Uploads
  UPLOAD_PHOTO: '/api/upload/photo',
  UPLOAD_VIDEO: '/api/upload/video',
  UPLOAD_DOCUMENT: '/api/upload/document',
  
  // Notifications
  NOTIFICATIONS: '/api/notifications',
  NOTIFICATION_READ: (id: string) => `/api/notifications/${id}/read`,
  NOTIFICATIONS_BROADCAST: '/api/notifications/broadcast',
  
  // Communications
  COMMUNICATIONS: '/api/communications',
  COMMUNICATION_READ: (id: string) => `/api/communications/${id}/read`,
  COMMUNICATION_THREADS: '/api/communications/threads',
  
  // Client Portal
  CLIENT_SITES: '/api/client/sites',
  CLIENT_AGENTS: '/api/client/agents',
  CLIENT_REPORTS: '/api/client/reports',
  CLIENT_REQUESTS: '/api/client/requests',
  CLIENT_REQUEST_FEEDBACK: (id: string) => `/api/client/requests/${id}/feedback`,
  
  // Analytics
  ANALYTICS_DASHBOARD: '/api/analytics/dashboard',
  ANALYTICS_PERFORMANCE: '/api/analytics/performance',
  ANALYTICS_LOCATIONS: '/api/analytics/locations',
  
  // Geofencing
  GEOFENCE_CHECK: '/api/geofence/check',
  GEOFENCE_VIOLATIONS: '/api/geofence/violations',
  
  // Webhooks
  WEBHOOK_CLERK: '/api/webhooks/clerk'
} as const;

// Socket.io Events
export const SOCKET_EVENTS = {
  // Connection
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  
  // Location Updates
  LOCATION_UPDATE: 'location-update',
  LOCATION_SUBSCRIBE: 'location-subscribe',
  LOCATION_UNSUBSCRIBE: 'location-unsubscribe',
  
  // Notifications
  NOTIFICATION_NEW: 'notification-new',
  NOTIFICATION_READ: 'notification-read',
  
  // Shift Updates
  SHIFT_STATUS_CHANGE: 'shift-status-change',
  SHIFT_ASSIGNMENT: 'shift-assignment',
  
  // Reports
  REPORT_SUBMITTED: 'report-submitted',
  REPORT_APPROVED: 'report-approved',
  REPORT_REJECTED: 'report-rejected',
  
  // Emergency
  EMERGENCY_ALERT: 'emergency-alert',
  SOS_ALERT: 'sos-alert',
  
  // Geofencing
  GEOFENCE_VIOLATION: 'geofence-violation',
  GEOFENCE_ENTRY: 'geofence-entry',
  GEOFENCE_EXIT: 'geofence-exit',
  
  // Client Requests
  CLIENT_REQUEST_NEW: 'client-request-new',
  CLIENT_REQUEST_UPDATE: 'client-request-update',
  
  // Communications
  MESSAGE_NEW: 'message-new',
  MESSAGE_READ: 'message-read',
  TYPING_START: 'typing-start',
  TYPING_STOP: 'typing-stop'
} as const;

// Error Codes
export const ERROR_CODES = {
  // Authentication
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID: 'AUTH_INVALID',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  FORBIDDEN: 'FORBIDDEN',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Resources
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',
  
  // Business Logic
  GEOFENCE_VIOLATION: 'GEOFENCE_VIOLATION',
  SHIFT_CONFLICT: 'SHIFT_CONFLICT',
  INVALID_SHIFT_STATUS: 'INVALID_SHIFT_STATUS',
  ALREADY_CLOCKED_IN: 'ALREADY_CLOCKED_IN',
  NOT_CLOCKED_IN: 'NOT_CLOCKED_IN',
  
  // File Upload
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // External Services
  CLERK_WEBHOOK_INVALID: 'CLERK_WEBHOOK_INVALID',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR'
} as const;

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 50,
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif'
  ],
  ALLOWED_VIDEO_TYPES: [
    'video/mp4',
    'video/quicktime',
    'video/x-msvideo',
    'video/webm'
  ],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ]
} as const;

// Geofence Constants
export const GEOFENCE = {
  DEFAULT_RADIUS_METERS: 100,
  MIN_RADIUS_METERS: 10,
  MAX_RADIUS_METERS: 1000,
  GPS_ACCURACY_THRESHOLD_METERS: 20
} as const;

// Time Constants
export const TIME = {
  LOCATION_UPDATE_INTERVAL_MS: 30000, // 30 seconds
  HEARTBEAT_INTERVAL_MS: 60000, // 1 minute
  SESSION_TIMEOUT_MS: 3600000, // 1 hour
  OFFLINE_SYNC_RETRY_INTERVAL_MS: 5000, // 5 seconds
  MAX_OFFLINE_QUEUE_SIZE: 100
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  SHIFT_ASSIGNMENT: 'shift_assignment',
  SHIFT_REMINDER: 'shift_reminder',
  SHIFT_STARTED: 'shift_started',
  SHIFT_ENDED: 'shift_ended',
  REPORT_SUBMITTED: 'report_submitted',
  REPORT_APPROVED: 'report_approved',
  REPORT_REJECTED: 'report_rejected',
  GEOFENCE_VIOLATION: 'geofence_violation',
  EMERGENCY_ALERT: 'emergency_alert',
  CLIENT_REQUEST: 'client_request',
  SYSTEM_MAINTENANCE: 'system_maintenance',
  MESSAGE_RECEIVED: 'message_received'
} as const;

// Default Values
export const DEFAULTS = {
  PAGINATION_LIMIT: 20,
  SEARCH_DEBOUNCE_MS: 300,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000
} as const;
