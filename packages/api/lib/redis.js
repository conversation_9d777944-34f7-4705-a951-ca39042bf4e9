// BahinLink Redis Cache Utilities
// ⚠️ CRITICAL: Real Upstash Redis integration ONLY

const { Redis } = require('@upstash/redis');

// Initialize Redis client with real Upstash credentials
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL,
  token: process.env.UPSTASH_REDIS_REST_TOKEN,
});

/**
 * Get cached data
 * @param {string} key Cache key
 * @returns {any} Cached data or null
 */
async function getCached(key) {
  try {
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Redis get error:', error);
    return null;
  }
}

/**
 * Set cached data with TTL
 * @param {string} key Cache key
 * @param {any} value Data to cache
 * @param {number} ttl Time to live in seconds (default: 1 hour)
 * @returns {boolean} Success status
 */
async function setCached(key, value, ttl = 3600) {
  try {
    await redis.setex(key, ttl, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error('Redis set error:', error);
    return false;
  }
}

/**
 * Delete cached data
 * @param {string} key Cache key
 * @returns {boolean} Success status
 */
async function deleteCached(key) {
  try {
    await redis.del(key);
    return true;
  } catch (error) {
    console.error('Redis delete error:', error);
    return false;
  }
}

/**
 * Cache agent location
 * @param {string} agentId Agent ID
 * @param {Object} location Location data
 */
async function cacheAgentLocation(agentId, location) {
  const key = `agent_location:${agentId}`;
  await setCached(key, {
    ...location,
    timestamp: new Date().toISOString()
  }, 300); // 5 minutes TTL
}

/**
 * Get cached agent location
 * @param {string} agentId Agent ID
 * @returns {Object|null} Location data or null
 */
async function getCachedAgentLocation(agentId) {
  const key = `agent_location:${agentId}`;
  return await getCached(key);
}

/**
 * Cache shift data
 * @param {string} shiftId Shift ID
 * @param {Object} shift Shift data
 */
async function cacheShift(shiftId, shift) {
  const key = `shift:${shiftId}`;
  await setCached(key, shift, 1800); // 30 minutes TTL
}

/**
 * Get cached shift data
 * @param {string} shiftId Shift ID
 * @returns {Object|null} Shift data or null
 */
async function getCachedShift(shiftId) {
  const key = `shift:${shiftId}`;
  return await getCached(key);
}

/**
 * Cache site data
 * @param {string} siteId Site ID
 * @param {Object} site Site data
 */
async function cacheSite(siteId, site) {
  const key = `site:${siteId}`;
  await setCached(key, site, 7200); // 2 hours TTL
}

/**
 * Get cached site data
 * @param {string} siteId Site ID
 * @returns {Object|null} Site data or null
 */
async function getCachedSite(siteId) {
  const key = `site:${siteId}`;
  return await getCached(key);
}

/**
 * Cache user session data
 * @param {string} userId User ID
 * @param {Object} sessionData Session data
 */
async function cacheUserSession(userId, sessionData) {
  const key = `user_session:${userId}`;
  await setCached(key, sessionData, 3600); // 1 hour TTL
}

/**
 * Get cached user session
 * @param {string} userId User ID
 * @returns {Object|null} Session data or null
 */
async function getCachedUserSession(userId) {
  const key = `user_session:${userId}`;
  return await getCached(key);
}

/**
 * Invalidate user session cache
 * @param {string} userId User ID
 */
async function invalidateUserSession(userId) {
  const key = `user_session:${userId}`;
  await deleteCached(key);
}

/**
 * Cache dashboard analytics
 * @param {string} userId User ID (for role-based caching)
 * @param {Object} analytics Analytics data
 */
async function cacheDashboardAnalytics(userId, analytics) {
  const key = `dashboard_analytics:${userId}`;
  await setCached(key, analytics, 300); // 5 minutes TTL
}

/**
 * Get cached dashboard analytics
 * @param {string} userId User ID
 * @returns {Object|null} Analytics data or null
 */
async function getCachedDashboardAnalytics(userId) {
  const key = `dashboard_analytics:${userId}`;
  return await getCached(key);
}

/**
 * Cache geofence check result
 * @param {string} agentId Agent ID
 * @param {string} siteId Site ID
 * @param {Object} result Geofence check result
 */
async function cacheGeofenceCheck(agentId, siteId, result) {
  const key = `geofence:${agentId}:${siteId}`;
  await setCached(key, result, 60); // 1 minute TTL
}

/**
 * Get cached geofence check
 * @param {string} agentId Agent ID
 * @param {string} siteId Site ID
 * @returns {Object|null} Geofence result or null
 */
async function getCachedGeofenceCheck(agentId, siteId) {
  const key = `geofence:${agentId}:${siteId}`;
  return await getCached(key);
}

/**
 * Add item to rate limiting bucket
 * @param {string} identifier Rate limit identifier (IP, user ID, etc.)
 * @param {number} windowMs Time window in milliseconds
 * @param {number} maxRequests Maximum requests per window
 * @returns {Object} Rate limit status
 */
async function checkRateLimit(identifier, windowMs = 900000, maxRequests = 100) {
  try {
    const key = `rate_limit:${identifier}`;
    const window = Math.floor(Date.now() / windowMs);
    const windowKey = `${key}:${window}`;
    
    const current = await redis.incr(windowKey);
    
    if (current === 1) {
      await redis.expire(windowKey, Math.ceil(windowMs / 1000));
    }
    
    return {
      allowed: current <= maxRequests,
      current,
      remaining: Math.max(0, maxRequests - current),
      resetTime: (window + 1) * windowMs
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    return { allowed: true, current: 0, remaining: maxRequests, resetTime: Date.now() + windowMs };
  }
}

/**
 * Store real-time event for offline sync
 * @param {string} userId User ID
 * @param {Object} event Event data
 */
async function storeOfflineEvent(userId, event) {
  try {
    const key = `offline_events:${userId}`;
    const eventWithTimestamp = {
      ...event,
      timestamp: new Date().toISOString(),
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    await redis.lpush(key, JSON.stringify(eventWithTimestamp));
    await redis.ltrim(key, 0, 99); // Keep only last 100 events
    await redis.expire(key, 86400); // 24 hours TTL
  } catch (error) {
    console.error('Error storing offline event:', error);
  }
}

/**
 * Get offline events for user
 * @param {string} userId User ID
 * @returns {Array} Array of offline events
 */
async function getOfflineEvents(userId) {
  try {
    const key = `offline_events:${userId}`;
    const events = await redis.lrange(key, 0, -1);
    return events.map(event => JSON.parse(event));
  } catch (error) {
    console.error('Error getting offline events:', error);
    return [];
  }
}

/**
 * Clear offline events for user
 * @param {string} userId User ID
 */
async function clearOfflineEvents(userId) {
  try {
    const key = `offline_events:${userId}`;
    await redis.del(key);
  } catch (error) {
    console.error('Error clearing offline events:', error);
  }
}

module.exports = {
  redis,
  getCached,
  setCached,
  deleteCached,
  cacheAgentLocation,
  getCachedAgentLocation,
  cacheShift,
  getCachedShift,
  cacheSite,
  getCachedSite,
  cacheUserSession,
  getCachedUserSession,
  invalidateUserSession,
  cacheDashboardAnalytics,
  getCachedDashboardAnalytics,
  cacheGeofenceCheck,
  getCachedGeofenceCheck,
  checkRateLimit,
  storeOfflineEvent,
  getOfflineEvents,
  clearOfflineEvents
};
