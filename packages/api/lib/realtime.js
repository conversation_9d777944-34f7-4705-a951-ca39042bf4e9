// BahinLink Real-time Service
// ⚠️ CRITICAL: Real Socket.io integration for live updates ONLY

const { Server } = require('socket.io');
const { requireAuth } = require('./auth');
const { prisma } = require('./prisma');
const { getCachedAgentLocation, storeOfflineEvent } = require('./redis');

class RealTimeService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map();
    this.agentRooms = new Map();
    this.clientRooms = new Map();
  }

  /**
   * Initialize Socket.io server with real authentication
   */
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? ['https://admin.bahinlink.com', 'https://client.bahinlink.com']
          : ['http://localhost:3001', 'http://localhost:3002'],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('Authentication required'));
        }

        // Verify token with Clerk (simplified for this implementation)
        // In real implementation, this would verify the JWT token
        const user = await this.verifySocketToken(token);
        if (!user) {
          return next(new Error('Invalid token'));
        }

        socket.userId = user.id;
        socket.userRole = user.role;
        socket.user = user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    this.setupEventHandlers();
    console.log('Real-time service initialized with Socket.io');
  }

  /**
   * Setup Socket.io event handlers
   */
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`User connected: ${socket.userId} (${socket.userRole})`);
      
      // Store connected user
      this.connectedUsers.set(socket.userId, {
        socketId: socket.id,
        role: socket.userRole,
        connectedAt: new Date()
      });

      // Join role-based rooms
      this.joinRoleRooms(socket);

      // Handle location updates (agents only)
      socket.on('location-update', (data) => this.handleLocationUpdate(socket, data));

      // Handle location subscription (supervisors/clients)
      socket.on('location-subscribe', (data) => this.handleLocationSubscribe(socket, data));
      socket.on('location-unsubscribe', (data) => this.handleLocationUnsubscribe(socket, data));

      // Handle shift status changes
      socket.on('shift-status-change', (data) => this.handleShiftStatusChange(socket, data));

      // Handle emergency alerts
      socket.on('emergency-alert', (data) => this.handleEmergencyAlert(socket, data));

      // Handle geofence events
      socket.on('geofence-violation', (data) => this.handleGeofenceViolation(socket, data));

      // Handle notifications
      socket.on('notification-read', (data) => this.handleNotificationRead(socket, data));

      // Handle messages
      socket.on('message-send', (data) => this.handleMessageSend(socket, data));
      socket.on('message-read', (data) => this.handleMessageRead(socket, data));

      // Handle typing indicators
      socket.on('typing-start', (data) => this.handleTypingStart(socket, data));
      socket.on('typing-stop', (data) => this.handleTypingStop(socket, data));

      // Handle disconnection
      socket.on('disconnect', () => this.handleDisconnect(socket));
    });
  }

  /**
   * Join user to appropriate rooms based on role
   */
  joinRoleRooms(socket) {
    const { userId, userRole } = socket;

    // Join general role room
    socket.join(`role:${userRole.toLowerCase()}`);

    if (userRole === 'AGENT') {
      // Agents join their own room
      socket.join(`agent:${userId}`);
      this.agentRooms.set(userId, socket.id);
    } else if (userRole === 'CLIENT') {
      // Clients join their client room
      socket.join(`client:${userId}`);
      this.clientRooms.set(userId, socket.id);
    } else if (['ADMIN', 'SUPERVISOR'].includes(userRole)) {
      // Admins and supervisors join monitoring rooms
      socket.join('supervisors');
      socket.join('all-agents');
    }

    console.log(`User ${userId} joined rooms for role: ${userRole}`);
  }

  /**
   * Handle real-time location updates from agents
   */
  async handleLocationUpdate(socket, data) {
    try {
      if (socket.userRole !== 'AGENT') {
        return socket.emit('error', { message: 'Only agents can send location updates' });
      }

      const { latitude, longitude, accuracy, timestamp } = data;

      // Validate GPS coordinates
      if (!latitude || !longitude || 
          latitude < -90 || latitude > 90 || 
          longitude < -180 || longitude > 180) {
        return socket.emit('error', { message: 'Invalid GPS coordinates' });
      }

      // Update location in database
      await prisma.agent.update({
        where: { userId: socket.userId },
        data: {
          currentLatitude: latitude,
          currentLongitude: longitude,
          lastLocationUpdate: new Date(timestamp || Date.now())
        }
      });

      // Broadcast to supervisors and clients
      const locationData = {
        agentId: socket.userId,
        latitude,
        longitude,
        accuracy,
        timestamp: timestamp || new Date().toISOString()
      };

      // Send to supervisors
      this.io.to('supervisors').emit('location-update', locationData);

      // Send to relevant clients (those with sites where agent is assigned)
      await this.broadcastToRelevantClients('location-update', locationData, socket.userId);

      console.log(`Location updated for agent ${socket.userId}: ${latitude}, ${longitude}`);
    } catch (error) {
      console.error('Location update error:', error);
      socket.emit('error', { message: 'Failed to update location' });
    }
  }

  /**
   * Handle shift status changes
   */
  async handleShiftStatusChange(socket, data) {
    try {
      const { shiftId, status, timestamp } = data;

      // Broadcast shift status change
      const statusData = {
        shiftId,
        agentId: socket.userId,
        status,
        timestamp: timestamp || new Date().toISOString()
      };

      // Send to supervisors
      this.io.to('supervisors').emit('shift-status-change', statusData);

      // Send to relevant clients
      await this.broadcastToRelevantClients('shift-status-change', statusData, socket.userId);

      console.log(`Shift status changed: ${shiftId} -> ${status}`);
    } catch (error) {
      console.error('Shift status change error:', error);
    }
  }

  /**
   * Handle emergency alerts
   */
  async handleEmergencyAlert(socket, data) {
    try {
      const { message, location, severity = 'EMERGENCY' } = data;

      const alertData = {
        agentId: socket.userId,
        agentName: socket.user.firstName + ' ' + socket.user.lastName,
        message,
        location,
        severity,
        timestamp: new Date().toISOString()
      };

      // Broadcast emergency alert to all supervisors immediately
      this.io.to('supervisors').emit('emergency-alert', alertData);

      // Send to relevant clients
      await this.broadcastToRelevantClients('emergency-alert', alertData, socket.userId);

      // Store in database
      await prisma.notification.create({
        data: {
          recipientId: socket.userId, // Will be updated to send to supervisors
          type: 'emergency_alert',
          title: 'Emergency Alert',
          message: `Emergency alert from ${alertData.agentName}: ${message}`,
          priority: 'EMERGENCY',
          data: alertData
        }
      });

      console.log(`Emergency alert from agent ${socket.userId}: ${message}`);
    } catch (error) {
      console.error('Emergency alert error:', error);
    }
  }

  /**
   * Handle geofence violations
   */
  async handleGeofenceViolation(socket, data) {
    try {
      const { siteId, distance, violationType } = data;

      const violationData = {
        agentId: socket.userId,
        siteId,
        distance,
        violationType,
        timestamp: new Date().toISOString()
      };

      // Send to supervisors
      this.io.to('supervisors').emit('geofence-violation', violationData);

      // Send to relevant clients
      await this.broadcastToRelevantClients('geofence-violation', violationData, socket.userId);

      console.log(`Geofence violation: Agent ${socket.userId} at site ${siteId}`);
    } catch (error) {
      console.error('Geofence violation error:', error);
    }
  }

  /**
   * Handle message sending
   */
  async handleMessageSend(socket, data) {
    try {
      const { recipientId, content, messageType = 'TEXT' } = data;

      // Create message in database
      const message = await prisma.communication.create({
        data: {
          senderId: socket.userId,
          recipientId,
          content,
          messageType,
          isRead: false
        },
        include: {
          sender: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        }
      });

      const messageData = {
        id: message.id,
        senderId: socket.userId,
        senderName: `${message.sender.firstName} ${message.sender.lastName}`,
        recipientId,
        content,
        messageType,
        timestamp: message.createdAt.toISOString()
      };

      // Send to recipient if connected
      const recipientConnection = this.connectedUsers.get(recipientId);
      if (recipientConnection) {
        this.io.to(recipientConnection.socketId).emit('message-new', messageData);
      }

      // Confirm to sender
      socket.emit('message-sent', { messageId: message.id });

      console.log(`Message sent from ${socket.userId} to ${recipientId}`);
    } catch (error) {
      console.error('Message send error:', error);
      socket.emit('error', { message: 'Failed to send message' });
    }
  }

  /**
   * Broadcast event to clients with sites where agent is assigned
   */
  async broadcastToRelevantClients(event, data, agentUserId) {
    try {
      // Get sites where agent is currently assigned
      const agentShifts = await prisma.shift.findMany({
        where: {
          agent: {
            userId: agentUserId
          },
          status: 'IN_PROGRESS',
          shiftDate: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lte: new Date(new Date().setHours(23, 59, 59, 999))
          }
        },
        include: {
          site: {
            include: {
              client: {
                include: {
                  user: true
                }
              }
            }
          }
        }
      });

      // Send to relevant clients
      for (const shift of agentShifts) {
        const clientUserId = shift.site.client.userId;
        const clientConnection = this.connectedUsers.get(clientUserId);
        
        if (clientConnection) {
          this.io.to(clientConnection.socketId).emit(event, {
            ...data,
            siteId: shift.site.id,
            siteName: shift.site.name
          });
        }
      }
    } catch (error) {
      console.error('Broadcast to clients error:', error);
    }
  }

  /**
   * Handle user disconnection
   */
  handleDisconnect(socket) {
    console.log(`User disconnected: ${socket.userId}`);
    
    // Remove from connected users
    this.connectedUsers.delete(socket.userId);
    
    // Remove from room maps
    this.agentRooms.delete(socket.userId);
    this.clientRooms.delete(socket.userId);
  }

  /**
   * Verify socket authentication token (simplified)
   */
  async verifySocketToken(token) {
    try {
      // In real implementation, this would verify JWT with Clerk
      // For now, we'll do a simple database lookup
      const user = await prisma.user.findFirst({
        where: {
          // This would be based on the decoded JWT token
          isActive: true
        }
      });
      
      return user;
    } catch (error) {
      console.error('Token verification error:', error);
      return null;
    }
  }

  /**
   * Send notification to specific user
   */
  sendNotification(userId, notification) {
    const userConnection = this.connectedUsers.get(userId);
    if (userConnection) {
      this.io.to(userConnection.socketId).emit('notification-new', notification);
    }
  }

  /**
   * Broadcast to all users of a specific role
   */
  broadcastToRole(role, event, data) {
    this.io.to(`role:${role.toLowerCase()}`).emit(event, data);
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  /**
   * Get connected users by role
   */
  getConnectedUsersByRole(role) {
    return Array.from(this.connectedUsers.values())
      .filter(user => user.role === role).length;
  }
}

module.exports = new RealTimeService();
