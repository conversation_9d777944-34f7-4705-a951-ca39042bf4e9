// BahinLink File Storage Utilities
// ⚠️ CRITICAL: Real Vercel Blob storage integration ONLY

const { put, del, list } = require('@vercel/blob');
const { v4: uuidv4 } = require('uuid');

/**
 * Upload file to Vercel Blob storage
 * @param {Buffer} fileBuffer File buffer
 * @param {string} filename Original filename
 * @param {string} mimeType File MIME type
 * @param {string} folder Storage folder (photos, videos, documents)
 * @returns {Object} Upload result with URL
 */
async function uploadFile(fileBuffer, filename, mimeType, folder = 'files') {
  try {
    // Generate unique filename
    const fileExtension = filename.split('.').pop();
    const uniqueFilename = `${folder}/${Date.now()}_${uuidv4()}.${fileExtension}`;
    
    // Upload to Vercel Blob
    const blob = await put(uniqueFilename, fileBuffer, {
      access: 'public',
      contentType: mimeType,
    });

    return {
      success: true,
      url: blob.url,
      filename: uniqueFilename,
      size: fileBuffer.length,
      mimeType,
      uploadedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('File upload error:', error);
    throw new Error('File upload failed');
  }
}

/**
 * Upload photo with optimization
 * @param {Buffer} photoBuffer Photo buffer
 * @param {string} filename Original filename
 * @param {string} mimeType Photo MIME type
 * @returns {Object} Upload result
 */
async function uploadPhoto(photoBuffer, filename, mimeType) {
  // Validate photo type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(mimeType)) {
    throw new Error('Invalid photo type');
  }

  // Check file size (max 10MB for photos)
  if (photoBuffer.length > 10 * 1024 * 1024) {
    throw new Error('Photo file too large');
  }

  return await uploadFile(photoBuffer, filename, mimeType, 'photos');
}

/**
 * Upload video
 * @param {Buffer} videoBuffer Video buffer
 * @param {string} filename Original filename
 * @param {string} mimeType Video MIME type
 * @returns {Object} Upload result
 */
async function uploadVideo(videoBuffer, filename, mimeType) {
  // Validate video type
  const allowedTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'];
  if (!allowedTypes.includes(mimeType)) {
    throw new Error('Invalid video type');
  }

  // Check file size (max 50MB for videos)
  if (videoBuffer.length > 50 * 1024 * 1024) {
    throw new Error('Video file too large');
  }

  return await uploadFile(videoBuffer, filename, mimeType, 'videos');
}

/**
 * Upload document
 * @param {Buffer} documentBuffer Document buffer
 * @param {string} filename Original filename
 * @param {string} mimeType Document MIME type
 * @returns {Object} Upload result
 */
async function uploadDocument(documentBuffer, filename, mimeType) {
  // Validate document type
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ];
  if (!allowedTypes.includes(mimeType)) {
    throw new Error('Invalid document type');
  }

  // Check file size (max 25MB for documents)
  if (documentBuffer.length > 25 * 1024 * 1024) {
    throw new Error('Document file too large');
  }

  return await uploadFile(documentBuffer, filename, mimeType, 'documents');
}

/**
 * Delete file from storage
 * @param {string} fileUrl File URL to delete
 * @returns {boolean} Success status
 */
async function deleteFile(fileUrl) {
  try {
    await del(fileUrl);
    return true;
  } catch (error) {
    console.error('File deletion error:', error);
    return false;
  }
}

/**
 * List files in folder
 * @param {string} folder Folder name
 * @param {number} limit Number of files to return
 * @returns {Array} Array of file objects
 */
async function listFiles(folder, limit = 100) {
  try {
    const { blobs } = await list({
      prefix: folder,
      limit
    });
    
    return blobs.map(blob => ({
      url: blob.url,
      filename: blob.pathname,
      size: blob.size,
      uploadedAt: blob.uploadedAt
    }));
  } catch (error) {
    console.error('File listing error:', error);
    return [];
  }
}

/**
 * Generate signed URL for temporary access
 * @param {string} fileUrl File URL
 * @param {number} expiresIn Expiration time in seconds
 * @returns {string} Signed URL
 */
function generateSignedUrl(fileUrl, expiresIn = 3600) {
  // For Vercel Blob, files are already publicly accessible
  // This function is for future enhancement if private files are needed
  return fileUrl;
}

/**
 * Validate file before upload
 * @param {Buffer} fileBuffer File buffer
 * @param {string} filename Original filename
 * @param {string} mimeType File MIME type
 * @param {string} type File type category (photo, video, document)
 * @returns {Object} Validation result
 */
function validateFile(fileBuffer, filename, mimeType, type) {
  const validation = {
    valid: true,
    errors: []
  };

  // Check file size
  const maxSizes = {
    photo: 10 * 1024 * 1024, // 10MB
    video: 50 * 1024 * 1024, // 50MB
    document: 25 * 1024 * 1024 // 25MB
  };

  if (fileBuffer.length > maxSizes[type]) {
    validation.valid = false;
    validation.errors.push(`File too large. Maximum size: ${maxSizes[type] / (1024 * 1024)}MB`);
  }

  // Check MIME type
  const allowedTypes = {
    photo: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    video: ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'],
    document: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]
  };

  if (!allowedTypes[type].includes(mimeType)) {
    validation.valid = false;
    validation.errors.push(`Invalid file type. Allowed types: ${allowedTypes[type].join(', ')}`);
  }

  // Check filename
  if (!filename || filename.length > 255) {
    validation.valid = false;
    validation.errors.push('Invalid filename');
  }

  return validation;
}

/**
 * Get file info from URL
 * @param {string} fileUrl File URL
 * @returns {Object} File information
 */
function getFileInfo(fileUrl) {
  try {
    const url = new URL(fileUrl);
    const pathname = url.pathname;
    const filename = pathname.split('/').pop();
    const extension = filename.split('.').pop();
    
    return {
      filename,
      extension,
      pathname,
      isImage: ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension.toLowerCase()),
      isVideo: ['mp4', 'mov', 'avi', 'webm'].includes(extension.toLowerCase()),
      isDocument: ['pdf', 'doc', 'docx', 'txt'].includes(extension.toLowerCase())
    };
  } catch (error) {
    console.error('Error getting file info:', error);
    return null;
  }
}

/**
 * Clean up old files (for maintenance)
 * @param {number} daysOld Number of days old to consider for cleanup
 * @returns {number} Number of files cleaned up
 */
async function cleanupOldFiles(daysOld = 30) {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    const folders = ['photos', 'videos', 'documents'];
    let cleanedCount = 0;
    
    for (const folder of folders) {
      const files = await listFiles(folder, 1000);
      
      for (const file of files) {
        const uploadDate = new Date(file.uploadedAt);
        if (uploadDate < cutoffDate) {
          const deleted = await deleteFile(file.url);
          if (deleted) {
            cleanedCount++;
          }
        }
      }
    }
    
    return cleanedCount;
  } catch (error) {
    console.error('Cleanup error:', error);
    return 0;
  }
}

module.exports = {
  uploadFile,
  uploadPhoto,
  uploadVideo,
  uploadDocument,
  deleteFile,
  listFiles,
  generateSignedUrl,
  validateFile,
  getFileInfo,
  cleanupOldFiles
};
