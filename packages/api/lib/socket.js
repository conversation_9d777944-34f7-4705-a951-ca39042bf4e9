// BahinLink Socket.io Real-time Communication
// ⚠️ CRITICAL: Real-time updates for GPS tracking and notifications ONLY

const { Server } = require('socket.io');
const { requireAuth } = require('./auth');
const { prisma } = require('./prisma');
const { redis } = require('./redis');

let io;

/**
 * Initialize Socket.io server
 */
function initializeSocket(server) {
  io = new Server(server, {
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://admin.bahinlink.com', 'https://client.bahinlink.com']
        : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8081'],
      methods: ['GET', 'POST'],
      credentials: true
    },
    transports: ['websocket', 'polling']
  });

  // Authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      // Verify token with Clerk
      const user = await verifyClerkToken(token);
      if (!user) {
        return next(new Error('Invalid authentication token'));
      }

      socket.userId = user.id;
      socket.userRole = user.role;
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication failed'));
    }
  });

  // Connection handling
  io.on('connection', (socket) => {
    console.log(`User ${socket.userId} connected with role ${socket.userRole}`);
    
    // Join role-based rooms
    socket.join(`role:${socket.userRole}`);
    socket.join(`user:${socket.userId}`);

    // Handle location updates from agents
    socket.on('location-update', async (data) => {
      await handleLocationUpdate(socket, data);
    });

    // Handle shift status changes
    socket.on('shift-status-change', async (data) => {
      await handleShiftStatusChange(socket, data);
    });

    // Handle report submissions
    socket.on('report-submitted', async (data) => {
      await handleReportSubmitted(socket, data);
    });

    // Handle emergency alerts
    socket.on('emergency-alert', async (data) => {
      await handleEmergencyAlert(socket, data);
    });

    // Handle chat messages
    socket.on('message', async (data) => {
      await handleMessage(socket, data);
    });

    // Handle geofence violations
    socket.on('geofence-violation', async (data) => {
      await handleGeofenceViolation(socket, data);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User ${socket.userId} disconnected`);
    });
  });

  return io;
}

/**
 * Handle real-time location updates from agents
 */
async function handleLocationUpdate(socket, data) {
  try {
    if (socket.userRole !== 'AGENT') {
      return socket.emit('error', { message: 'Only agents can send location updates' });
    }

    const { latitude, longitude, accuracy, timestamp } = data;

    // Validate location data
    if (!latitude || !longitude || latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return socket.emit('error', { message: 'Invalid location coordinates' });
    }

    // Get agent information
    const agent = await prisma.agent.findUnique({
      where: { userId: socket.userId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        shifts: {
          where: {
            status: 'IN_PROGRESS',
            shiftDate: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
              lte: new Date(new Date().setHours(23, 59, 59, 999))
            }
          },
          include: {
            site: {
              select: {
                id: true,
                name: true,
                latitude: true,
                longitude: true,
                geofenceRadius: true,
                client: {
                  select: {
                    id: true,
                    userId: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!agent) {
      return socket.emit('error', { message: 'Agent profile not found' });
    }

    // Update agent location in database
    await prisma.agent.update({
      where: { id: agent.id },
      data: {
        currentLatitude: latitude,
        currentLongitude: longitude,
        lastLocationUpdate: new Date(timestamp || Date.now())
      }
    });

    // Cache location in Redis for faster access
    await redis.setex(
      `agent_location:${agent.id}`,
      300, // 5 minutes TTL
      JSON.stringify({
        latitude,
        longitude,
        accuracy,
        timestamp: timestamp || Date.now()
      })
    );

    // Check geofence violations for active shifts
    const currentShift = agent.shifts[0];
    if (currentShift) {
      const distance = calculateDistance(
        latitude,
        longitude,
        currentShift.site.latitude,
        currentShift.site.longitude
      );

      const withinGeofence = distance <= currentShift.site.geofenceRadius;
      
      if (!withinGeofence) {
        // Create geofence violation record
        await prisma.geofenceViolation.create({
          data: {
            agentId: agent.id,
            siteId: currentShift.site.id,
            shiftId: currentShift.id,
            latitude,
            longitude,
            distance: Math.round(distance),
            isResolved: false
          }
        });

        // Emit geofence violation alert
        io.to('role:ADMIN').to('role:SUPERVISOR').emit('geofence-violation', {
          agentId: agent.id,
          agentName: `${agent.user.firstName} ${agent.user.lastName}`,
          siteName: currentShift.site.name,
          distance: Math.round(distance),
          timestamp: new Date().toISOString()
        });

        // Notify client if they're connected
        if (currentShift.site.client.userId) {
          io.to(`user:${currentShift.site.client.userId}`).emit('geofence-violation', {
            agentName: `${agent.user.firstName} ${agent.user.lastName}`,
            siteName: currentShift.site.name,
            distance: Math.round(distance),
            timestamp: new Date().toISOString()
          });
        }
      }
    }

    // Broadcast location update to authorized users
    const locationUpdate = {
      agentId: agent.id,
      agentName: `${agent.user.firstName} ${agent.user.lastName}`,
      latitude,
      longitude,
      accuracy,
      timestamp: timestamp || Date.now(),
      currentShift: currentShift ? {
        siteId: currentShift.site.id,
        siteName: currentShift.site.name,
        withinGeofence: currentShift ? calculateDistance(
          latitude,
          longitude,
          currentShift.site.latitude,
          currentShift.site.longitude
        ) <= currentShift.site.geofenceRadius : null
      } : null
    };

    // Send to admins and supervisors
    io.to('role:ADMIN').to('role:SUPERVISOR').emit('location-update', locationUpdate);

    // Send to relevant clients
    if (currentShift?.site.client.userId) {
      io.to(`user:${currentShift.site.client.userId}`).emit('location-update', locationUpdate);
    }

    // Confirm receipt to agent
    socket.emit('location-update-confirmed', {
      timestamp: new Date().toISOString(),
      withinGeofence: currentShift ? locationUpdate.currentShift.withinGeofence : null
    });

  } catch (error) {
    console.error('Handle location update error:', error);
    socket.emit('error', { message: 'Failed to process location update' });
  }
}

/**
 * Handle shift status changes
 */
async function handleShiftStatusChange(socket, data) {
  try {
    const { shiftId, status, timestamp } = data;

    // Get shift information
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                userId: true
              }
            }
          }
        }
      }
    });

    if (!shift) {
      return socket.emit('error', { message: 'Shift not found' });
    }

    // Verify authorization
    if (socket.userRole === 'AGENT' && shift.agent.userId !== socket.userId) {
      return socket.emit('error', { message: 'Not authorized to update this shift' });
    }

    // Update shift status
    await prisma.shift.update({
      where: { id: shiftId },
      data: {
        status,
        ...(status === 'IN_PROGRESS' && { actualStartTime: new Date(timestamp) }),
        ...(status === 'COMPLETED' && { actualEndTime: new Date(timestamp) })
      }
    });

    // Broadcast status change
    const statusUpdate = {
      shiftId,
      agentId: shift.agentId,
      agentName: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
      siteName: shift.site.name,
      status,
      timestamp: timestamp || new Date().toISOString()
    };

    // Send to admins and supervisors
    io.to('role:ADMIN').to('role:SUPERVISOR').emit('shift-status-change', statusUpdate);

    // Send to client
    if (shift.site.client.userId) {
      io.to(`user:${shift.site.client.userId}`).emit('shift-status-change', statusUpdate);
    }

    // Confirm to sender
    socket.emit('shift-status-change-confirmed', statusUpdate);

  } catch (error) {
    console.error('Handle shift status change error:', error);
    socket.emit('error', { message: 'Failed to process shift status change' });
  }
}

/**
 * Handle report submissions
 */
async function handleReportSubmitted(socket, data) {
  try {
    const { reportId } = data;

    // Get report information
    const report = await prisma.report.findUnique({
      where: { id: reportId },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                userId: true
              }
            }
          }
        }
      }
    });

    if (!report) {
      return socket.emit('error', { message: 'Report not found' });
    }

    // Broadcast new report notification
    const reportNotification = {
      reportId: report.id,
      type: report.type,
      title: report.title,
      priority: report.priority,
      agentName: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
      siteName: report.site.name,
      timestamp: report.createdAt
    };

    // Send to admins and supervisors
    io.to('role:ADMIN').to('role:SUPERVISOR').emit('report-submitted', reportNotification);

    // Send to client
    if (report.site.client.userId) {
      io.to(`user:${report.site.client.userId}`).emit('report-submitted', reportNotification);
    }

  } catch (error) {
    console.error('Handle report submitted error:', error);
    socket.emit('error', { message: 'Failed to process report submission' });
  }
}

/**
 * Handle emergency alerts
 */
async function handleEmergencyAlert(socket, data) {
  try {
    const { message, latitude, longitude, priority = 'HIGH' } = data;

    // Get user information
    const user = await prisma.user.findUnique({
      where: { id: socket.userId },
      include: {
        agent: {
          include: {
            shifts: {
              where: {
                status: 'IN_PROGRESS'
              },
              include: {
                site: {
                  include: {
                    client: {
                      select: {
                        userId: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    });

    // Create emergency alert record
    const alert = await prisma.emergencyAlert.create({
      data: {
        userId: socket.userId,
        message,
        latitude,
        longitude,
        priority,
        isResolved: false
      }
    });

    // Broadcast emergency alert
    const emergencyAlert = {
      alertId: alert.id,
      userName: `${user.firstName} ${user.lastName}`,
      userRole: socket.userRole,
      message,
      location: latitude && longitude ? { latitude, longitude } : null,
      priority,
      timestamp: alert.createdAt
    };

    // Send to all admins and supervisors immediately
    io.to('role:ADMIN').to('role:SUPERVISOR').emit('emergency-alert', emergencyAlert);

    // If agent, also notify their current client
    if (socket.userRole === 'AGENT' && user.agent?.shifts[0]?.site.client.userId) {
      io.to(`user:${user.agent.shifts[0].site.client.userId}`).emit('emergency-alert', emergencyAlert);
    }

    // Confirm alert sent
    socket.emit('emergency-alert-confirmed', {
      alertId: alert.id,
      timestamp: alert.createdAt
    });

  } catch (error) {
    console.error('Handle emergency alert error:', error);
    socket.emit('error', { message: 'Failed to process emergency alert' });
  }
}

/**
 * Calculate distance between two GPS coordinates
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

/**
 * Verify Clerk token (simplified - would use actual Clerk verification)
 */
async function verifyClerkToken(token) {
  // This would integrate with Clerk's token verification
  // For now, return a mock user for development
  try {
    // In production, use Clerk's verifyToken method
    return {
      id: 'user_123',
      role: 'AGENT' // This would come from Clerk user metadata
    };
  } catch (error) {
    return null;
  }
}

/**
 * Get Socket.io instance
 */
function getSocketInstance() {
  return io;
}

/**
 * Emit event to specific users or roles
 */
function emitToUsers(userIds, event, data) {
  if (!io) return;
  
  userIds.forEach(userId => {
    io.to(`user:${userId}`).emit(event, data);
  });
}

function emitToRoles(roles, event, data) {
  if (!io) return;
  
  roles.forEach(role => {
    io.to(`role:${role}`).emit(event, data);
  });
}

module.exports = {
  initializeSocket,
  getSocketInstance,
  emitToUsers,
  emitToRoles
};
