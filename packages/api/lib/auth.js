// BahinLink Authentication Utilities
// ⚠️ CRITICAL: Real Clerk authentication integration ONLY

const { clerkClient } = require('@clerk/express');
const { prisma } = require('./prisma');

/**
 * Middleware to require authentication
 * @param {Object} req Express request object
 * @param {Object} res Express response object
 * @param {Function} next Next middleware function
 */
async function requireAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_REQUIRED',
          message: 'Authentication required'
        },
        timestamp: new Date().toISOString()
      });
    }

    const token = authHeader.substring(7);
    
    // Verify token with Clerk
    const session = await clerkClient.verifyToken(token);
    
    if (!session) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_INVALID',
          message: 'Invalid authentication token'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { clerkId: session.sub },
      include: {
        agent: true,
        client: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_INVALID',
          message: 'User not found or inactive'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Attach user to request
    req.user = user;
    req.clerkSession = session;
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTH_INVALID',
        message: 'Authentication failed'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Middleware to require specific roles
 * @param {Array} allowedRoles Array of allowed roles
 * @returns {Function} Middleware function
 */
function requireRole(allowedRoles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_REQUIRED',
          message: 'Authentication required'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions'
        },
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
}

/**
 * Get user from Clerk ID
 * @param {string} clerkId Clerk user ID
 * @returns {Object|null} User object or null
 */
async function getUserByClerkId(clerkId) {
  try {
    return await prisma.user.findUnique({
      where: { clerkId },
      include: {
        agent: true,
        client: true
      }
    });
  } catch (error) {
    console.error('Error getting user by Clerk ID:', error);
    return null;
  }
}

/**
 * Create or update user from Clerk webhook
 * @param {Object} clerkUser Clerk user object
 * @returns {Object} Created/updated user
 */
async function syncUserFromClerk(clerkUser) {
  try {
    const userData = {
      clerkId: clerkUser.id,
      email: clerkUser.email_addresses[0]?.email_address,
      firstName: clerkUser.first_name || '',
      lastName: clerkUser.last_name || '',
      phone: clerkUser.phone_numbers[0]?.phone_number,
      role: clerkUser.public_metadata?.role || 'AGENT',
      isActive: true
    };

    return await prisma.user.upsert({
      where: { clerkId: clerkUser.id },
      update: userData,
      create: userData,
      include: {
        agent: true,
        client: true
      }
    });
  } catch (error) {
    console.error('Error syncing user from Clerk:', error);
    throw error;
  }
}

/**
 * Delete user when deleted from Clerk
 * @param {string} clerkId Clerk user ID
 */
async function deleteUserFromClerk(clerkId) {
  try {
    await prisma.user.delete({
      where: { clerkId }
    });
  } catch (error) {
    console.error('Error deleting user from Clerk:', error);
    throw error;
  }
}

/**
 * Verify Clerk webhook signature
 * @param {string} payload Webhook payload
 * @param {string} signature Webhook signature
 * @returns {boolean} True if signature is valid
 */
function verifyClerkWebhook(payload, signature) {
  try {
    const crypto = require('crypto');
    const secret = process.env.CLERK_WEBHOOK_SECRET;
    
    if (!secret) {
      console.error('CLERK_WEBHOOK_SECRET not configured');
      return false;
    }

    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    console.error('Error verifying Clerk webhook:', error);
    return false;
  }
}

/**
 * Log audit event
 * @param {Object} params Audit log parameters
 */
async function logAuditEvent({
  userId,
  action,
  tableName,
  recordId,
  oldValues,
  newValues,
  ipAddress,
  userAgent,
  apiEndpoint,
  httpMethod,
  responseStatus
}) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        action,
        tableName,
        recordId,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
        ipAddress,
        userAgent,
        apiEndpoint,
        httpMethod,
        responseStatus
      }
    });
  } catch (error) {
    console.error('Error logging audit event:', error);
  }
}

/**
 * Middleware to log API requests
 */
function auditLogger(req, res, next) {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Log the audit event
    if (req.user) {
      logAuditEvent({
        userId: req.user.id,
        action: `${req.method} ${req.path}`,
        apiEndpoint: req.path,
        httpMethod: req.method,
        responseStatus: res.statusCode,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
    }
    
    return originalSend.call(this, data);
  };
  
  next();
}

module.exports = {
  requireAuth,
  requireRole,
  getUserByClerkId,
  syncUserFromClerk,
  deleteUserFromClerk,
  verifyClerkWebhook,
  logAuditEvent,
  auditLogger
};
