// BahinLink Prisma Client
// ⚠️ CRITICAL: Configured for REAL PRODUCTION DATABASE ONLY

const { PrismaClient } = require('@prisma/client');

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

// Create Prisma client with production configuration
const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  errorFormat: 'pretty',
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

module.exports = { prisma };
