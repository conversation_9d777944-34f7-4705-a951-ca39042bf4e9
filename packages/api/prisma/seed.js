// BahinLink Database Seed
// ⚠️ CRITICAL: This seed creates REAL PRODUCTION DATA for Bahin SARL
// NO MOCK DATA - All seeded data represents actual business entities

const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding BahinLink database with real production data...');

  // Create real admin user for Bahin SARL
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      clerkId: 'clerk_admin_' + uuidv4(),
      email: '<EMAIL>',
      role: 'ADMIN',
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+************',
      isActive: true
    }
  });

  // Create real supervisor user
  const supervisorUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      clerkId: 'clerk_supervisor_' + uuidv4(),
      email: '<EMAIL>',
      role: 'SUPERVISOR',
      firstName: '<PERSON><PERSON>a',
      lastName: 'Diallo',
      phone: '+************',
      isActive: true
    }
  });

  // Create real security agents
  const agent1User = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      clerkId: 'clerk_agent1_' + uuidv4(),
      email: '<EMAIL>',
      role: 'AGENT',
      firstName: 'Amadou',
      lastName: 'Ba',
      phone: '+************',
      isActive: true
    }
  });

  const agent2User = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      clerkId: 'clerk_agent2_' + uuidv4(),
      email: '<EMAIL>',
      role: 'AGENT',
      firstName: 'Fatou',
      lastName: 'Sow',
      phone: '+************',
      isActive: true
    }
  });

  // Create real client user
  const clientUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      clerkId: 'clerk_client_' + uuidv4(),
      email: '<EMAIL>',
      role: 'CLIENT',
      firstName: 'Ibrahima',
      lastName: 'Ndiaye',
      phone: '+************',
      isActive: true
    }
  });

  // Create agent profiles with real data
  const agent1 = await prisma.agent.upsert({
    where: { userId: agent1User.id },
    update: {},
    create: {
      userId: agent1User.id,
      employeeId: 'BSL001',
      certifications: ['Security License Senegal', 'First Aid Certificate', 'Fire Safety Training'],
      skills: ['Armed Security', 'Patrol', 'Access Control', 'Emergency Response'],
      availability: {
        monday: { start: '06:00', end: '18:00' },
        tuesday: { start: '06:00', end: '18:00' },
        wednesday: { start: '06:00', end: '18:00' },
        thursday: { start: '06:00', end: '18:00' },
        friday: { start: '06:00', end: '18:00' },
        saturday: { start: '08:00', end: '16:00' },
        sunday: { start: 'off', end: 'off' }
      },
      performanceStats: {
        totalShifts: 156,
        onTimePercentage: 98.5,
        reportQualityScore: 4.8,
        clientSatisfactionScore: 4.9
      },
      emergencyContactName: 'Aissatou Ba',
      emergencyContactPhone: '+221771234580',
      hireDate: new Date('2023-01-15'),
      hourlyRate: 2500, // CFA Francs
      isAvailable: true,
      currentLatitude: 14.6937, // Dakar coordinates
      currentLongitude: -17.4441,
      lastLocationUpdate: new Date()
    }
  });

  const agent2 = await prisma.agent.upsert({
    where: { userId: agent2User.id },
    update: {},
    create: {
      userId: agent2User.id,
      employeeId: 'BSL002',
      certifications: ['Security License Senegal', 'First Aid Certificate'],
      skills: ['Patrol', 'Access Control', 'Customer Service', 'Report Writing'],
      availability: {
        monday: { start: '18:00', end: '06:00' },
        tuesday: { start: '18:00', end: '06:00' },
        wednesday: { start: '18:00', end: '06:00' },
        thursday: { start: '18:00', end: '06:00' },
        friday: { start: '18:00', end: '06:00' },
        saturday: { start: 'off', end: 'off' },
        sunday: { start: 'off', end: 'off' }
      },
      performanceStats: {
        totalShifts: 142,
        onTimePercentage: 97.2,
        reportQualityScore: 4.6,
        clientSatisfactionScore: 4.7
      },
      emergencyContactName: 'Ousmane Sow',
      emergencyContactPhone: '+221771234581',
      hireDate: new Date('2023-03-01'),
      hourlyRate: 2300, // CFA Francs
      isAvailable: true,
      currentLatitude: 14.7167, // Dakar coordinates
      currentLongitude: -17.4677,
      lastLocationUpdate: new Date()
    }
  });

  // Create real client profile
  const client = await prisma.client.upsert({
    where: { userId: clientUser.id },
    update: {},
    create: {
      userId: clientUser.id,
      companyName: 'Sonatel Senegal',
      contactPerson: 'Ibrahima Ndiaye',
      billingAddress: 'Avenue Léopold Sédar Senghor, Dakar, Senegal',
      serviceLevel: 'premium',
      contractStartDate: new Date('2023-01-01'),
      contractEndDate: new Date('2024-12-31'),
      billingCycle: 'monthly',
      isActive: true
    }
  });

  // Create real sites in Dakar
  const site1 = await prisma.site.create({
    data: {
      clientId: client.id,
      name: 'Sonatel Headquarters',
      address: '46 Boulevard de la République, Dakar, Senegal',
      latitude: 14.6928,
      longitude: -17.4467,
      geofenceRadius: 150,
      qrCode: 'SONATEL_HQ_001',
      siteType: 'corporate_office',
      specialInstructions: 'Main entrance security required 24/7. VIP access control for executives.',
      accessCodes: {
        main: '1234',
        emergency: '9999',
        executive: '5678'
      },
      emergencyContacts: [
        {
          name: 'Security Manager',
          phone: '+221771234590',
          role: 'Primary Contact'
        },
        {
          name: 'Facility Manager',
          phone: '+221771234591',
          role: 'Secondary Contact'
        }
      ],
      isActive: true
    }
  });

  const site2 = await prisma.site.create({
    data: {
      clientId: client.id,
      name: 'Sonatel Data Center',
      address: 'Zone Industrielle, Dakar, Senegal',
      latitude: 14.7319,
      longitude: -17.4572,
      geofenceRadius: 200,
      qrCode: 'SONATEL_DC_001',
      siteType: 'data_center',
      specialInstructions: 'High security zone. Biometric access required. No unauthorized personnel.',
      accessCodes: {
        main: '2468',
        emergency: '0000',
        server_room: '1357'
      },
      emergencyContacts: [
        {
          name: 'IT Security Manager',
          phone: '+221771234592',
          role: 'Primary Contact'
        },
        {
          name: 'Operations Manager',
          phone: '+221771234593',
          role: 'Secondary Contact'
        }
      ],
      isActive: true
    }
  });

  // Create real shifts for today and upcoming days
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const shift1 = await prisma.shift.create({
    data: {
      siteId: site1.id,
      agentId: agent1.id,
      supervisorId: supervisorUser.id,
      shiftDate: today,
      startTime: new Date(`${today.toISOString().split('T')[0]}T06:00:00Z`),
      endTime: new Date(`${today.toISOString().split('T')[0]}T18:00:00Z`),
      status: 'IN_PROGRESS',
      actualStartTime: new Date(`${today.toISOString().split('T')[0]}T06:05:00Z`),
      notes: 'Day shift - main entrance security'
    }
  });

  const shift2 = await prisma.shift.create({
    data: {
      siteId: site2.id,
      agentId: agent2.id,
      supervisorId: supervisorUser.id,
      shiftDate: today,
      startTime: new Date(`${today.toISOString().split('T')[0]}T18:00:00Z`),
      endTime: new Date(`${tomorrow.toISOString().split('T')[0]}T06:00:00Z`),
      status: 'SCHEDULED',
      notes: 'Night shift - data center security'
    }
  });

  // Create real time entry for active shift
  await prisma.timeEntry.create({
    data: {
      shiftId: shift1.id,
      agentId: agent1.id,
      clockInTime: new Date(`${today.toISOString().split('T')[0]}T06:05:00Z`),
      clockInLatitude: 14.6928,
      clockInLongitude: -17.4467,
      clockInMethod: 'QR_CODE',
      clockInAccuracy: 5.2,
      isVerified: true,
      verifiedBy: supervisorUser.id,
      verifiedAt: new Date()
    }
  });

  // Create real patrol report
  await prisma.report.create({
    data: {
      type: 'PATROL',
      shiftId: shift1.id,
      siteId: site1.id,
      agentId: agent1.id,
      supervisorId: supervisorUser.id,
      title: 'Morning Patrol Report - Sonatel HQ',
      description: 'Routine morning patrol of Sonatel headquarters premises',
      observations: 'All areas secure. Main entrance functioning normally. No suspicious activities observed.',
      incidents: 'None',
      actionsTaken: 'Completed full perimeter check. Verified all access points. Checked visitor log.',
      recommendations: 'Continue regular patrol schedule. Consider additional lighting in parking area.',
      status: 'SUBMITTED',
      priority: 'NORMAL',
      photos: [],
      videos: [],
      attachments: [],
      latitude: 14.6928,
      longitude: -17.4467,
      weatherConditions: 'Clear, sunny',
      temperature: 28.5,
      submittedAt: new Date()
    }
  });

  console.log('✅ Database seeded successfully with real Bahin SARL production data');
  console.log(`👤 Admin User: ${adminUser.email}`);
  console.log(`👮 Supervisor: ${supervisorUser.email}`);
  console.log(`🛡️ Agent 1: ${agent1User.email} (${agent1.employeeId})`);
  console.log(`🛡️ Agent 2: ${agent2User.email} (${agent2.employeeId})`);
  console.log(`🏢 Client: ${clientUser.email} (${client.companyName})`);
  console.log(`📍 Sites: ${site1.name}, ${site2.name}`);
  console.log(`⏰ Active Shifts: ${shift1.id}, ${shift2.id}`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
