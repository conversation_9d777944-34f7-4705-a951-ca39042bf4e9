# Database (Real Neon PostgreSQL)
DATABASE_URL="postgresql://username:<EMAIL>/bahinlink?sslmode=require"

# Clerk Authentication (Real Production Keys)
CLERK_PUBLISHABLE_KEY="pk_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
CLERK_SECRET_KEY="sk_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
CLERK_WEBHOOK_SECRET="whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Vercel Blob Storage (Real Production)
BLOB_READ_WRITE_TOKEN="vercel_blob_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Upstash Redis (Real Production)
UPSTASH_REDIS_REST_URL="https://xyz.upstash.io"
UPSTASH_REDIS_REST_TOKEN="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Google Maps API (Real Production)
GOOGLE_MAPS_API_KEY="AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Firebase (Real Production)
FIREBASE_SERVER_KEY="AAAAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
FIREBASE_PROJECT_ID="bahinlink-production"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL="<EMAIL>"

# Communication Services (Real Production)
SENDGRID_API_KEY="SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_ACCOUNT_SID="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_AUTH_TOKEN="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_PHONE_NUMBER="+**********"

# Sentry (Real Production)
SENTRY_DSN="https://<EMAIL>/xxxxxxx"

# Application URLs
API_BASE_URL="https://api.bahinlink.com"
WEB_BASE_URL="https://admin.bahinlink.com"
CLIENT_BASE_URL="https://client.bahinlink.com"
MOBILE_DEEP_LINK="bahinlink://"

# Security
JWT_SECRET="production_jwt_secret_very_secure_random_string_256_bits"
ENCRYPTION_KEY="production_encryption_key_32_characters_exactly"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,video/mp4,video/quicktime,application/pdf"
