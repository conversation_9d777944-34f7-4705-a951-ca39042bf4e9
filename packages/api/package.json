{"name": "@bahinlink/api", "version": "1.0.0", "description": "BahinLink API - Real production backend with Vercel functions", "main": "index.js", "scripts": {"dev": "vercel dev", "build": "vercel build", "deploy": "vercel --prod", "db:migrate": "prisma migrate deploy", "db:generate": "prisma generate", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@prisma/client": "^5.7.1", "@clerk/express": "^0.0.8", "@clerk/backend": "^0.38.4", "@vercel/blob": "^0.15.1", "@upstash/redis": "^1.28.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "nodemailer": "^6.9.8", "twilio": "^4.20.0", "firebase-admin": "^12.0.0", "qrcode": "^1.5.3", "sharp": "^0.33.1", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "date-fns": "^3.0.6"}, "devDependencies": {"prisma": "^5.7.1", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2"}, "prisma": {"seed": "node prisma/seed.js"}}