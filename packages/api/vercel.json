{"version": 2, "name": "bahinlink-api", "builds": [{"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"api/**/*.js": {"maxDuration": 30, "memory": 1024}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Authorization, Content-Type, X-Requested-With"}, {"key": "Access-Control-Max-Age", "value": "86400"}]}]}