// BahinLink Notifications Management API
// ⚠️ CRITICAL: Real notification system with push notifications ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getNotifications(req, res);
    } else if (req.method === 'POST') {
      return await createNotification(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Notifications API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get notifications for current user
 */
async function getNotifications(req, res) {
  try {
    const {
      page = 1,
      limit = 50,
      type,
      isRead,
      priority,
      startDate,
      endDate
    } = req.query;

    const currentUser = req.user;

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build where clause
    const where = {
      recipientId: currentUser.id,
      createdAt: {
        gte: start,
        lte: end
      }
    };

    // Additional filters
    if (type) where.type = type;
    if (isRead !== undefined) where.isRead = isRead === 'true';
    if (priority) where.priority = priority;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get notifications with related data
    const [notifications, totalCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        skip,
        take,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          sender: {
            select: {
              firstName: true,
              lastName: true,
              role: true
            }
          }
        }
      }),
      prisma.notification.count({ where })
    ]);

    // Process notifications data
    const processedNotifications = notifications.map(notification => ({
      id: notification.id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      priority: notification.priority,
      isRead: notification.isRead,
      readAt: notification.readAt,
      
      // Sender information
      sender: notification.sender ? {
        name: `${notification.sender.firstName} ${notification.sender.lastName}`,
        role: notification.sender.role
      } : null,
      
      // Related data
      relatedId: notification.relatedId,
      relatedType: notification.relatedType,
      actionUrl: notification.actionUrl,
      
      // Metadata
      metadata: notification.metadata,
      
      // Timestamps
      createdAt: notification.createdAt,
      expiresAt: notification.expiresAt
    }));

    // Calculate summary statistics
    const summary = {
      totalNotifications: totalCount,
      unreadNotifications: processedNotifications.filter(n => !n.isRead).length,
      readNotifications: processedNotifications.filter(n => n.isRead).length,
      highPriorityNotifications: processedNotifications.filter(n => n.priority === 'HIGH').length,
      urgentNotifications: processedNotifications.filter(n => n.priority === 'URGENT').length,
      expiredNotifications: processedNotifications.filter(n => 
        n.expiresAt && new Date(n.expiresAt) < new Date()
      ).length
    };

    return res.json({
      success: true,
      data: processedNotifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve notifications'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Create new notification (admin/supervisor only)
 */
async function createNotification(req, res) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      recipientIds,
      type,
      title,
      message,
      priority = 'MEDIUM',
      actionUrl,
      relatedId,
      relatedType,
      expiresAt,
      sendPushNotification = true,
      metadata = {}
    } = req.body;

    const currentUser = req.user;

    // Validate required fields
    if (!recipientIds || !Array.isArray(recipientIds) || recipientIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Recipient IDs array is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (!type || !title || !message) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Type, title, and message are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate recipients exist
    const validRecipients = await prisma.user.findMany({
      where: {
        id: {
          in: recipientIds
        },
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true
      }
    });

    if (validRecipients.length !== recipientIds.length) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_RECIPIENTS',
          message: 'Some recipient IDs are invalid or inactive'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Create notifications for all recipients
    const notificationsData = validRecipients.map(recipient => ({
      recipientId: recipient.id,
      senderId: currentUser.id,
      type,
      title,
      message,
      priority,
      actionUrl,
      relatedId,
      relatedType,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      metadata,
      isRead: false
    }));

    const createdNotifications = await prisma.notification.createMany({
      data: notificationsData
    });

    // Get the created notifications with full data
    const notifications = await prisma.notification.findMany({
      where: {
        senderId: currentUser.id,
        createdAt: {
          gte: new Date(Date.now() - 1000) // Last second
        }
      },
      include: {
        recipient: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: validRecipients.length
    });

    // TODO: Send push notifications if requested
    if (sendPushNotification) {
      // Implementation would go here for push notification service
      console.log(`Push notifications sent to ${validRecipients.length} recipients`);
    }

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'CREATE_NOTIFICATION',
      tableName: 'notifications',
      recordId: notifications[0]?.id,
      newValues: {
        type,
        title,
        recipientCount: validRecipients.length,
        priority
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.status(201).json({
      success: true,
      data: {
        notificationCount: createdNotifications.count,
        notifications: notifications.map(notification => ({
          id: notification.id,
          recipient: {
            name: `${notification.recipient.firstName} ${notification.recipient.lastName}`,
            email: notification.recipient.email
          },
          type: notification.type,
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          createdAt: notification.createdAt
        })),
        summary: {
          totalSent: createdNotifications.count,
          pushNotificationsSent: sendPushNotification,
          priority,
          type
        }
      },
      message: `${createdNotifications.count} notification(s) created successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Create notification error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to create notification'
      },
      timestamp: new Date().toISOString()
    });
  }
}
