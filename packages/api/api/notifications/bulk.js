// BahinLink Bulk Notification Actions API
// ⚠️ CRITICAL: Real bulk notification management ONLY

const { requireAuth } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await performBulkAction(req, res);
  } catch (error) {
    console.error('Bulk notifications API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Perform bulk actions on notifications
 */
async function performBulkAction(req, res) {
  try {
    const {
      action,
      notificationIds,
      filters
    } = req.body;

    const currentUser = req.user;

    // Validate action
    const allowedActions = ['MARK_READ', 'MARK_UNREAD', 'DELETE', 'MARK_ALL_READ', 'DELETE_ALL_READ'];
    if (!allowedActions.includes(action)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ACTION',
          message: 'Invalid bulk action'
        },
        timestamp: new Date().toISOString()
      });
    }

    let targetNotifications = [];
    let whereClause = { recipientId: currentUser.id };

    // Handle different action types
    if (action === 'MARK_ALL_READ') {
      // Mark all unread notifications as read
      whereClause.isRead = false;
      
      // Apply filters if provided
      if (filters) {
        if (filters.type) whereClause.type = filters.type;
        if (filters.priority) whereClause.priority = filters.priority;
        if (filters.startDate) {
          whereClause.createdAt = {
            ...whereClause.createdAt,
            gte: new Date(filters.startDate)
          };
        }
        if (filters.endDate) {
          whereClause.createdAt = {
            ...whereClause.createdAt,
            lte: new Date(filters.endDate)
          };
        }
      }

      const updateResult = await prisma.notification.updateMany({
        where: whereClause,
        data: {
          isRead: true,
          readAt: new Date(),
          updatedAt: new Date()
        }
      });

      // Create audit log
      await createAuditLog({
        userId: currentUser.id,
        action: 'BULK_MARK_ALL_READ',
        tableName: 'notifications',
        newValues: { count: updateResult.count },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent']
      });

      return res.json({
        success: true,
        data: {
          action: 'MARK_ALL_READ',
          affectedCount: updateResult.count
        },
        message: `${updateResult.count} notification(s) marked as read`,
        timestamp: new Date().toISOString()
      });

    } else if (action === 'DELETE_ALL_READ') {
      // Delete all read notifications
      whereClause.isRead = true;
      
      // Apply filters if provided
      if (filters) {
        if (filters.type) whereClause.type = filters.type;
        if (filters.priority) whereClause.priority = filters.priority;
        if (filters.olderThanDays) {
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - parseInt(filters.olderThanDays));
          whereClause.createdAt = {
            ...whereClause.createdAt,
            lt: cutoffDate
          };
        }
      }

      const deleteResult = await prisma.notification.deleteMany({
        where: whereClause
      });

      // Create audit log
      await createAuditLog({
        userId: currentUser.id,
        action: 'BULK_DELETE_ALL_READ',
        tableName: 'notifications',
        newValues: { count: deleteResult.count },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent']
      });

      return res.json({
        success: true,
        data: {
          action: 'DELETE_ALL_READ',
          affectedCount: deleteResult.count
        },
        message: `${deleteResult.count} read notification(s) deleted`,
        timestamp: new Date().toISOString()
      });

    } else {
      // Handle specific notification IDs
      if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Notification IDs array is required for this action'
          },
          timestamp: new Date().toISOString()
        });
      }

      // Verify all notifications belong to the user
      const validNotifications = await prisma.notification.findMany({
        where: {
          id: { in: notificationIds },
          recipientId: currentUser.id
        },
        select: {
          id: true,
          isRead: true
        }
      });

      if (validNotifications.length !== notificationIds.length) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_NOTIFICATIONS',
            message: 'Some notification IDs are invalid or do not belong to you'
          },
          timestamp: new Date().toISOString()
        });
      }

      let result;
      let actionMessage;

      switch (action) {
        case 'MARK_READ':
          result = await prisma.notification.updateMany({
            where: {
              id: { in: notificationIds },
              recipientId: currentUser.id
            },
            data: {
              isRead: true,
              readAt: new Date(),
              updatedAt: new Date()
            }
          });
          actionMessage = 'marked as read';
          break;

        case 'MARK_UNREAD':
          result = await prisma.notification.updateMany({
            where: {
              id: { in: notificationIds },
              recipientId: currentUser.id
            },
            data: {
              isRead: false,
              readAt: null,
              updatedAt: new Date()
            }
          });
          actionMessage = 'marked as unread';
          break;

        case 'DELETE':
          result = await prisma.notification.deleteMany({
            where: {
              id: { in: notificationIds },
              recipientId: currentUser.id
            }
          });
          actionMessage = 'deleted';
          break;

        default:
          return res.status(400).json({
            success: false,
            error: {
              code: 'INVALID_ACTION',
              message: 'Invalid action for specific notifications'
            },
            timestamp: new Date().toISOString()
          });
      }

      // Create audit log
      await createAuditLog({
        userId: currentUser.id,
        action: `BULK_${action}`,
        tableName: 'notifications',
        newValues: { 
          notificationIds,
          count: result.count 
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent']
      });

      return res.json({
        success: true,
        data: {
          action,
          requestedCount: notificationIds.length,
          affectedCount: result.count,
          notificationIds
        },
        message: `${result.count} notification(s) ${actionMessage}`,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Bulk notification action error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to perform bulk action'
      },
      timestamp: new Date().toISOString()
    });
  }
}
