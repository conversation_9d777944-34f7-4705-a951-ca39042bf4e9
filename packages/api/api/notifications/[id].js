// BahinLink Notification Details API
// ⚠️ CRITICAL: Real notification management ONLY

const { requireAuth } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { id } = req.query;

    if (req.method === 'GET') {
      return await getNotificationById(req, res, id);
    } else if (req.method === 'PUT') {
      return await updateNotification(req, res, id);
    } else if (req.method === 'DELETE') {
      return await deleteNotification(req, res, id);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Notification details API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get notification by ID
 */
async function getNotificationById(req, res, notificationId) {
  try {
    const currentUser = req.user;

    const notification = await prisma.notification.findUnique({
      where: {
        id: notificationId,
        recipientId: currentUser.id // Users can only see their own notifications
      },
      include: {
        sender: {
          select: {
            firstName: true,
            lastName: true,
            role: true
          }
        }
      }
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOTIFICATION_NOT_FOUND',
          message: 'Notification not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Mark as read if not already read
    if (!notification.isRead) {
      await prisma.notification.update({
        where: { id: notificationId },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });
    }

    const responseData = {
      id: notification.id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      priority: notification.priority,
      isRead: true, // Always true after this call
      readAt: notification.readAt || new Date(),
      
      // Sender information
      sender: notification.sender ? {
        name: `${notification.sender.firstName} ${notification.sender.lastName}`,
        role: notification.sender.role
      } : null,
      
      // Related data
      relatedId: notification.relatedId,
      relatedType: notification.relatedType,
      actionUrl: notification.actionUrl,
      
      // Metadata
      metadata: notification.metadata,
      
      // Timestamps
      createdAt: notification.createdAt,
      expiresAt: notification.expiresAt,
      
      // Status
      isExpired: notification.expiresAt ? new Date(notification.expiresAt) < new Date() : false
    };

    return res.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get notification by ID error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve notification'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update notification (mark as read/unread)
 */
async function updateNotification(req, res, notificationId) {
  try {
    const currentUser = req.user;
    const { isRead } = req.body;

    if (isRead === undefined) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'isRead field is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if notification exists and belongs to user
    const existingNotification = await prisma.notification.findUnique({
      where: {
        id: notificationId,
        recipientId: currentUser.id
      }
    });

    if (!existingNotification) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOTIFICATION_NOT_FOUND',
          message: 'Notification not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update notification
    const updatedNotification = await prisma.notification.update({
      where: { id: notificationId },
      data: {
        isRead,
        readAt: isRead ? new Date() : null,
        updatedAt: new Date()
      }
    });

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: isRead ? 'MARK_NOTIFICATION_READ' : 'MARK_NOTIFICATION_UNREAD',
      tableName: 'notifications',
      recordId: notificationId,
      oldValues: { isRead: existingNotification.isRead },
      newValues: { isRead },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: {
        id: updatedNotification.id,
        isRead: updatedNotification.isRead,
        readAt: updatedNotification.readAt,
        updatedAt: updatedNotification.updatedAt
      },
      message: `Notification marked as ${isRead ? 'read' : 'unread'}`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update notification error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update notification'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Delete notification
 */
async function deleteNotification(req, res, notificationId) {
  try {
    const currentUser = req.user;

    // Check if notification exists and belongs to user
    const existingNotification = await prisma.notification.findUnique({
      where: {
        id: notificationId,
        recipientId: currentUser.id
      }
    });

    if (!existingNotification) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOTIFICATION_NOT_FOUND',
          message: 'Notification not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Delete notification
    await prisma.notification.delete({
      where: { id: notificationId }
    });

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'DELETE_NOTIFICATION',
      tableName: 'notifications',
      recordId: notificationId,
      oldValues: existingNotification,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      message: 'Notification deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete notification error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to delete notification'
      },
      timestamp: new Date().toISOString()
    });
  }
}
