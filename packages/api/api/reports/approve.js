// BahinLink Report Approval API
// ⚠️ CRITICAL: Real report approval workflow ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication and supervisor/admin role
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await approveReport(req, res);
  } catch (error) {
    console.error('Report approval API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Approve report with quality scoring
 */
async function approveReport(req, res) {
  try {
    const {
      reportId,
      qualityScore,
      reviewNotes,
      sendNotification = true
    } = req.body;

    const currentUser = req.user;
    const approvalTime = new Date();

    // Validate required fields
    if (!reportId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Report ID is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate quality score
    if (qualityScore !== undefined && (qualityScore < 1 || qualityScore > 5)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Quality score must be between 1 and 5'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get existing report
    const existingReport = await prisma.report.findUnique({
      where: { id: reportId },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                companyName: true,
                user: {
                  select: {
                    email: true
                  }
                }
              }
            }
          }
        },
        shift: {
          select: {
            shiftDate: true
          }
        }
      }
    });

    if (!existingReport) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if report can be approved
    if (existingReport.status !== 'SUBMITTED') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_STATUS',
          message: `Cannot approve report with status: ${existingReport.status}`
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if already reviewed by same person
    if (existingReport.reviewedBy === currentUser.id) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'ALREADY_REVIEWED',
          message: 'Report has already been reviewed by you'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update report status to approved
    const approvedReport = await prisma.report.update({
      where: { id: reportId },
      data: {
        status: 'APPROVED',
        reviewedBy: currentUser.id,
        reviewedAt: approvalTime,
        reviewNotes,
        qualityScore: qualityScore || 5,
        updatedAt: approvalTime
      },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          select: {
            name: true,
            client: {
              select: {
                companyName: true
              }
            }
          }
        }
      }
    });

    // Create review history entry
    await prisma.reportReview.create({
      data: {
        reportId,
        reviewerId: currentUser.id,
        action: 'APPROVED',
        notes: reviewNotes,
        qualityScore: qualityScore || 5
      }
    });

    // Update agent performance stats
    const agent = existingReport.agent;
    const currentStats = agent.performanceStats || {};
    const newStats = {
      ...currentStats,
      totalReports: (currentStats.totalReports || 0) + 1,
      approvedReports: (currentStats.approvedReports || 0) + 1,
      reportQualityScore: qualityScore ? 
        ((currentStats.reportQualityScore || 5) + qualityScore) / 2 : 
        (currentStats.reportQualityScore || 5)
    };

    await prisma.agent.update({
      where: { id: agent.id },
      data: {
        performanceStats: newStats
      }
    });

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'APPROVE_REPORT',
      tableName: 'reports',
      recordId: reportId,
      oldValues: { status: existingReport.status },
      newValues: { 
        status: 'APPROVED', 
        qualityScore: qualityScore || 5,
        reviewNotes 
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    // TODO: Send notification to agent and client if requested
    if (sendNotification) {
      // Implementation would go here for email/push notifications
      console.log(`Notification: Report ${reportId} approved by ${currentUser.firstName} ${currentUser.lastName}`);
    }

    return res.json({
      success: true,
      data: {
        reportId,
        status: 'APPROVED',
        qualityScore: qualityScore || 5,
        reviewedBy: {
          id: currentUser.id,
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          role: currentUser.role
        },
        reviewedAt: approvalTime,
        reviewNotes,
        agent: {
          name: `${existingReport.agent.user.firstName} ${existingReport.agent.user.lastName}`,
          email: existingReport.agent.user.email
        },
        site: {
          name: existingReport.site.name,
          client: existingReport.site.client.companyName
        },
        responseTime: Math.round((approvalTime - new Date(existingReport.submittedAt)) / (1000 * 60 * 60) * 100) / 100
      },
      message: 'Report approved successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Approve report error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to approve report'
      },
      timestamp: new Date().toISOString()
    });
  }
}
