// BahinLink Site Reports API
// ⚠️ CRITICAL: Real site-specific report management ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      siteId,
      page = 1,
      limit = 50,
      type,
      status,
      priority,
      startDate,
      endDate,
      includeStats = 'true'
    } = req.query;

    const currentUser = req.user;

    // Validate siteId parameter
    if (!siteId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Site ID is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify site exists and check permissions
    const siteWhere = { id: siteId };
    if (currentUser.role === 'CLIENT') {
      siteWhere.client = {
        userId: currentUser.id
      };
    }

    const site = await prisma.site.findUnique({
      where: siteWhere,
      include: {
        client: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            serviceLevel: true
          }
        }
      }
    });

    if (!site) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found or access denied'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build where clause
    const where = {
      siteId,
      createdAt: {
        gte: start,
        lte: end
      }
    };

    // Additional filters
    if (type) where.type = type;
    if (status) where.status = status;
    if (priority) where.priority = priority;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get reports with related data
    const [reports, totalCount] = await Promise.all([
      prisma.report.findMany({
        where,
        skip,
        take,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          agent: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  phone: true
                }
              }
            }
          },
          shift: {
            select: {
              id: true,
              shiftDate: true,
              startTime: true,
              endTime: true,
              status: true
            }
          },
          attachments: {
            select: {
              id: true,
              fileName: true,
              fileType: true,
              url: true,
              thumbnailUrl: true
            }
          }
        }
      }),
      prisma.report.count({ where })
    ]);

    // Process reports data
    const processedReports = reports.map(report => {
      // Calculate response time if reviewed
      const responseTime = report.submittedAt && report.reviewedAt ? 
        (new Date(report.reviewedAt) - new Date(report.submittedAt)) / (1000 * 60 * 60) : null;

      // Check if overdue
      const isOverdue = report.status === 'SUBMITTED' && 
        (new Date() - new Date(report.submittedAt)) > (24 * 60 * 60 * 1000);

      return {
        id: report.id,
        type: report.type,
        title: report.title,
        description: report.description,
        status: report.status,
        priority: report.priority,
        
        // Agent information
        agent: {
          id: report.agent.id,
          employeeId: report.agent.employeeId,
          name: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
          phone: report.agent.user.phone
        },
        
        // Shift context
        shift: report.shift,
        
        // Location data
        location: report.latitude && report.longitude ? {
          latitude: report.latitude,
          longitude: report.longitude
        } : null,
        
        // Attachments summary
        attachments: report.attachments.map(att => ({
          id: att.id,
          fileName: att.fileName,
          fileType: att.fileType,
          url: att.url,
          thumbnailUrl: att.thumbnailUrl
        })),
        
        // Quality and review
        qualityScore: report.qualityScore,
        reviewedBy: report.reviewedBy,
        reviewedAt: report.reviewedAt,
        
        // Metrics
        responseTimeHours: responseTime ? Math.round(responseTime * 100) / 100 : null,
        isOverdue,
        daysOld: Math.floor((new Date() - new Date(report.createdAt)) / (1000 * 60 * 60 * 24)),
        
        // Timestamps
        createdAt: report.createdAt,
        submittedAt: report.submittedAt,
        updatedAt: report.updatedAt
      };
    });

    // Calculate statistics if requested
    let statistics = null;
    if (includeStats === 'true') {
      const completedReports = processedReports.filter(r => ['APPROVED', 'REJECTED'].includes(r.status));
      const approvedReports = processedReports.filter(r => r.status === 'APPROVED');
      const overdueReports = processedReports.filter(r => r.isOverdue);
      
      // Calculate average response time
      const responseTimes = completedReports
        .filter(r => r.responseTimeHours !== null)
        .map(r => r.responseTimeHours);
      
      const avgResponseTime = responseTimes.length > 0 ? 
        Math.round((responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length) * 100) / 100 : 0;

      // Calculate average quality score
      const qualityScores = approvedReports
        .filter(r => r.qualityScore !== null)
        .map(r => r.qualityScore);
      
      const avgQualityScore = qualityScores.length > 0 ? 
        Math.round((qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length) * 100) / 100 : 0;

      // Report type distribution
      const typeDistribution = {};
      processedReports.forEach(report => {
        typeDistribution[report.type] = (typeDistribution[report.type] || 0) + 1;
      });

      // Priority distribution
      const priorityDistribution = {};
      processedReports.forEach(report => {
        priorityDistribution[report.priority] = (priorityDistribution[report.priority] || 0) + 1;
      });

      statistics = {
        totalReports: totalCount,
        submittedReports: processedReports.filter(r => r.status === 'SUBMITTED').length,
        approvedReports: approvedReports.length,
        rejectedReports: processedReports.filter(r => r.status === 'REJECTED').length,
        draftReports: processedReports.filter(r => r.status === 'DRAFT').length,
        overdueReports: overdueReports.length,
        highPriorityReports: processedReports.filter(r => r.priority === 'HIGH').length,
        urgentReports: processedReports.filter(r => r.priority === 'URGENT').length,
        averageResponseTimeHours: avgResponseTime,
        averageQualityScore: avgQualityScore,
        approvalRate: completedReports.length > 0 ? 
          Math.round((approvedReports.length / completedReports.length) * 100) : 0,
        typeDistribution,
        priorityDistribution,
        recentActivity: {
          last24Hours: processedReports.filter(r => 
            (new Date() - new Date(r.createdAt)) < (24 * 60 * 60 * 1000)
          ).length,
          last7Days: processedReports.filter(r => 
            (new Date() - new Date(r.createdAt)) < (7 * 24 * 60 * 60 * 1000)
          ).length
        }
      };
    }

    return res.json({
      success: true,
      data: processedReports,
      site: {
        id: site.id,
        name: site.name,
        address: site.address,
        siteType: site.siteType,
        client: site.client
      },
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      ...(statistics && { statistics }),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Site reports API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to retrieve site reports'
      },
      timestamp: new Date().toISOString()
    });
  }
};
