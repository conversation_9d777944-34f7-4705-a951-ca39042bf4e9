// BahinLink Report Rejection API
// ⚠️ CRITICAL: Real report rejection workflow ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication and supervisor/admin role
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await rejectReport(req, res);
  } catch (error) {
    console.error('Report rejection API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Reject report with feedback
 */
async function rejectReport(req, res) {
  try {
    const {
      reportId,
      rejectionReason,
      reviewNotes,
      qualityScore,
      allowResubmission = true,
      sendNotification = true
    } = req.body;

    const currentUser = req.user;
    const rejectionTime = new Date();

    // Validate required fields
    if (!reportId || !rejectionReason) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Report ID and rejection reason are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate quality score if provided
    if (qualityScore !== undefined && (qualityScore < 1 || qualityScore > 5)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Quality score must be between 1 and 5'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get existing report
    const existingReport = await prisma.report.findUnique({
      where: { id: reportId },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                companyName: true,
                user: {
                  select: {
                    email: true
                  }
                }
              }
            }
          }
        },
        shift: {
          select: {
            shiftDate: true
          }
        }
      }
    });

    if (!existingReport) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if report can be rejected
    if (existingReport.status !== 'SUBMITTED') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_STATUS',
          message: `Cannot reject report with status: ${existingReport.status}`
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if already reviewed by same person
    if (existingReport.reviewedBy === currentUser.id) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'ALREADY_REVIEWED',
          message: 'Report has already been reviewed by you'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Determine new status based on resubmission policy
    const newStatus = allowResubmission ? 'DRAFT' : 'REJECTED';

    // Update report status
    const rejectedReport = await prisma.report.update({
      where: { id: reportId },
      data: {
        status: newStatus,
        reviewedBy: currentUser.id,
        reviewedAt: rejectionTime,
        reviewNotes: `${rejectionReason}${reviewNotes ? '\n\nAdditional Notes: ' + reviewNotes : ''}`,
        qualityScore: qualityScore || 1,
        updatedAt: rejectionTime
      },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          select: {
            name: true,
            client: {
              select: {
                companyName: true
              }
            }
          }
        }
      }
    });

    // Create review history entry
    await prisma.reportReview.create({
      data: {
        reportId,
        reviewerId: currentUser.id,
        action: 'REJECTED',
        notes: `${rejectionReason}${reviewNotes ? '\n\nAdditional Notes: ' + reviewNotes : ''}`,
        qualityScore: qualityScore || 1
      }
    });

    // Update agent performance stats
    const agent = existingReport.agent;
    const currentStats = agent.performanceStats || {};
    const newStats = {
      ...currentStats,
      totalReports: (currentStats.totalReports || 0) + 1,
      rejectedReports: (currentStats.rejectedReports || 0) + 1,
      reportQualityScore: qualityScore ? 
        Math.min((currentStats.reportQualityScore || 5), qualityScore) : 
        Math.max((currentStats.reportQualityScore || 5) - 0.5, 1)
    };

    await prisma.agent.update({
      where: { id: agent.id },
      data: {
        performanceStats: newStats
      }
    });

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'REJECT_REPORT',
      tableName: 'reports',
      recordId: reportId,
      oldValues: { status: existingReport.status },
      newValues: { 
        status: newStatus, 
        rejectionReason,
        qualityScore: qualityScore || 1,
        allowResubmission 
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    // TODO: Send notification to agent if requested
    if (sendNotification) {
      // Implementation would go here for email/push notifications
      console.log(`Notification: Report ${reportId} rejected by ${currentUser.firstName} ${currentUser.lastName}`);
    }

    return res.json({
      success: true,
      data: {
        reportId,
        status: newStatus,
        rejectionReason,
        qualityScore: qualityScore || 1,
        allowResubmission,
        reviewedBy: {
          id: currentUser.id,
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          role: currentUser.role
        },
        reviewedAt: rejectionTime,
        reviewNotes,
        agent: {
          name: `${existingReport.agent.user.firstName} ${existingReport.agent.user.lastName}`,
          email: existingReport.agent.user.email
        },
        site: {
          name: existingReport.site.name,
          client: existingReport.site.client.companyName
        },
        responseTime: Math.round((rejectionTime - new Date(existingReport.submittedAt)) / (1000 * 60 * 60) * 100) / 100,
        nextSteps: allowResubmission ? 
          'Agent can revise and resubmit the report' : 
          'Report has been permanently rejected'
      },
      message: `Report ${allowResubmission ? 'rejected for revision' : 'permanently rejected'}`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Reject report error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to reject report'
      },
      timestamp: new Date().toISOString()
    });
  }
}
