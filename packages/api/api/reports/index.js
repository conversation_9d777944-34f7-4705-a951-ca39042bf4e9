// BahinLink Reports Management API
// ⚠️ CRITICAL: Real reporting system with file attachments ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getReports(req, res);
    } else if (req.method === 'POST') {
      return await createReport(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Reports API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get reports with filtering and role-based access
 */
async function getReports(req, res) {
  try {
    const {
      page = 1,
      limit = 50,
      agentId,
      siteId,
      type,
      status,
      priority,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const currentUser = req.user;

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build where clause based on user role
    const where = {
      createdAt: {
        gte: start,
        lte: end
      }
    };

    // Role-based filtering
    if (currentUser.role === 'AGENT') {
      // Agents can only see their own reports
      where.agent = {
        userId: currentUser.id
      };
    } else if (currentUser.role === 'CLIENT') {
      // Clients can only see reports from their sites
      where.site = {
        client: {
          userId: currentUser.id
        }
      };
    } else {
      // Admin/Supervisor can see all reports with optional filtering
      if (agentId) where.agentId = agentId;
      if (siteId) where.siteId = siteId;
    }

    // Additional filters
    if (type) where.type = type;
    if (status) where.status = status;
    if (priority) where.priority = priority;
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { agent: {
          user: {
            OR: [
              { firstName: { contains: search, mode: 'insensitive' } },
              { lastName: { contains: search, mode: 'insensitive' } }
            ]
          }
        }}
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get reports with related data
    const [reports, totalCount] = await Promise.all([
      prisma.report.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        },
        include: {
          agent: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  phone: true
                }
              }
            }
          },
          site: {
            include: {
              client: {
                select: {
                  companyName: true
                }
              }
            }
          },
          shift: {
            select: {
              id: true,
              shiftDate: true,
              startTime: true,
              endTime: true
            }
          },
          attachments: true
        }
      }),
      prisma.report.count({ where })
    ]);

    // Process reports data
    const processedReports = reports.map(report => ({
      id: report.id,
      type: report.type,
      title: report.title,
      description: report.description,
      status: report.status,
      priority: report.priority,
      
      // Agent information
      agent: {
        id: report.agent.id,
        employeeId: report.agent.employeeId,
        name: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
        phone: report.agent.user.phone
      },
      
      // Site information
      site: {
        id: report.site.id,
        name: report.site.name,
        address: report.site.address,
        clientName: report.site.client.companyName
      },
      
      // Shift information
      shift: report.shift,
      
      // Location data
      location: report.latitude && report.longitude ? {
        latitude: report.latitude,
        longitude: report.longitude
      } : null,
      
      // Attachments
      attachments: report.attachments.map(attachment => ({
        id: attachment.id,
        fileName: attachment.fileName,
        fileType: attachment.fileType,
        fileSize: attachment.fileSize,
        url: attachment.url
      })),
      
      // Quality and approval
      qualityScore: report.qualityScore,
      reviewedBy: report.reviewedBy,
      reviewedAt: report.reviewedAt,
      reviewNotes: report.reviewNotes,
      
      // Timestamps
      createdAt: report.createdAt,
      submittedAt: report.submittedAt,
      updatedAt: report.updatedAt
    }));

    // Calculate summary statistics
    const summary = {
      totalReports: totalCount,
      submittedReports: processedReports.filter(r => r.status === 'SUBMITTED').length,
      approvedReports: processedReports.filter(r => r.status === 'APPROVED').length,
      rejectedReports: processedReports.filter(r => r.status === 'REJECTED').length,
      draftReports: processedReports.filter(r => r.status === 'DRAFT').length,
      highPriorityReports: processedReports.filter(r => r.priority === 'HIGH').length,
      averageQualityScore: processedReports.filter(r => r.qualityScore).length > 0 ?
        Math.round(processedReports.reduce((sum, r) => sum + (r.qualityScore || 0), 0) / 
        processedReports.filter(r => r.qualityScore).length * 100) / 100 : 0
    };

    return res.json({
      success: true,
      data: processedReports,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get reports error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve reports'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Create new report
 */
async function createReport(req, res) {
  try {
    const {
      type,
      title,
      description,
      siteId,
      shiftId,
      priority = 'MEDIUM',
      latitude,
      longitude,
      attachmentIds = []
    } = req.body;

    const currentUser = req.user;

    // Validate required fields
    if (!type || !title || !description || !siteId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields: type, title, description, siteId'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get agent ID for current user
    let agentId = null;
    if (currentUser.role === 'AGENT') {
      const agent = await prisma.agent.findUnique({
        where: { userId: currentUser.id }
      });
      
      if (!agent) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent profile not found for current user'
          },
          timestamp: new Date().toISOString()
        });
      }
      
      agentId = agent.id;
    } else {
      // Admin/Supervisor creating report on behalf of agent
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Only agents can create reports'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate site exists
    const site = await prisma.site.findUnique({
      where: { id: siteId }
    });

    if (!site) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate shift if provided
    let shift = null;
    if (shiftId) {
      shift = await prisma.shift.findUnique({
        where: { id: shiftId }
      });

      if (!shift || shift.agentId !== agentId) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_SHIFT',
            message: 'Invalid shift or shift not assigned to current agent'
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    // Validate attachments if provided
    let validAttachments = [];
    if (attachmentIds.length > 0) {
      validAttachments = await prisma.attachment.findMany({
        where: {
          id: {
            in: attachmentIds
          },
          // Ensure attachments belong to current user
          uploadedBy: currentUser.id
        }
      });

      if (validAttachments.length !== attachmentIds.length) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ATTACHMENTS',
            message: 'Some attachments are invalid or not owned by current user'
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    // Create report
    const newReport = await prisma.report.create({
      data: {
        agentId,
        siteId,
        shiftId,
        type,
        title,
        description,
        priority,
        latitude: latitude ? parseFloat(latitude) : null,
        longitude: longitude ? parseFloat(longitude) : null,
        status: 'SUBMITTED',
        submittedAt: new Date(),
        attachments: {
          connect: validAttachments.map(att => ({ id: att.id }))
        }
      },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                companyName: true
              }
            }
          }
        },
        shift: true,
        attachments: true
      }
    });

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'CREATE_REPORT',
      tableName: 'reports',
      recordId: newReport.id,
      newValues: newReport,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    // TODO: Send notification to supervisors about new report

    return res.status(201).json({
      success: true,
      data: newReport,
      message: 'Report created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Create report error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to create report'
      },
      timestamp: new Date().toISOString()
    });
  }
}
