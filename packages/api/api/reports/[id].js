// BahinLink Report Details API
// ⚠️ CRITICAL: Real report management with approval workflow ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { id } = req.query;

    if (req.method === 'GET') {
      return await getReportById(req, res, id);
    } else if (req.method === 'PUT') {
      return await updateReport(req, res, id);
    } else if (req.method === 'DELETE') {
      return await deleteReport(req, res, id);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Report details API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get report by ID with comprehensive data
 */
async function getReportById(req, res, reportId) {
  try {
    const currentUser = req.user;

    // Build where clause based on user role
    const where = { id: reportId };
    
    if (currentUser.role === 'AGENT') {
      where.agent = {
        userId: currentUser.id
      };
    } else if (currentUser.role === 'CLIENT') {
      where.site = {
        client: {
          userId: currentUser.id
        }
      };
    }

    const report = await prisma.report.findUnique({
      where,
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                phone: true,
                email: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                id: true,
                companyName: true,
                contactPerson: true,
                serviceLevel: true,
                user: {
                  select: {
                    email: true,
                    phone: true
                  }
                }
              }
            }
          }
        },
        shift: {
          include: {
            timeEntries: {
              where: {
                clockInTime: {
                  lte: new Date()
                }
              },
              orderBy: {
                clockInTime: 'desc'
              },
              take: 1
            }
          }
        },
        attachments: {
          orderBy: {
            createdAt: 'asc'
          }
        },
        reviewHistory: {
          include: {
            reviewer: {
              select: {
                firstName: true,
                lastName: true,
                role: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!report) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Calculate report metrics
    const responseTime = report.submittedAt && report.reviewedAt ? 
      (new Date(report.reviewedAt) - new Date(report.submittedAt)) / (1000 * 60 * 60) : null;

    const isOverdue = report.status === 'SUBMITTED' && 
      (new Date() - new Date(report.submittedAt)) > (24 * 60 * 60 * 1000); // 24 hours

    const responseData = {
      id: report.id,
      type: report.type,
      title: report.title,
      description: report.description,
      status: report.status,
      priority: report.priority,
      
      // Agent information
      agent: {
        id: report.agent.id,
        employeeId: report.agent.employeeId,
        name: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
        phone: report.agent.user.phone,
        email: report.agent.user.email
      },
      
      // Site information
      site: {
        id: report.site.id,
        name: report.site.name,
        address: report.site.address,
        latitude: report.site.latitude,
        longitude: report.site.longitude,
        client: report.site.client
      },
      
      // Shift context
      shift: report.shift ? {
        id: report.shift.id,
        shiftDate: report.shift.shiftDate,
        startTime: report.shift.startTime,
        endTime: report.shift.endTime,
        status: report.shift.status,
        wasActive: report.shift.timeEntries.length > 0
      } : null,
      
      // Location data
      location: report.latitude && report.longitude ? {
        latitude: report.latitude,
        longitude: report.longitude,
        timestamp: report.createdAt
      } : null,
      
      // Attachments with detailed info
      attachments: report.attachments.map(attachment => ({
        id: attachment.id,
        fileName: attachment.fileName,
        fileType: attachment.fileType,
        fileSize: attachment.fileSize,
        url: attachment.url,
        thumbnailUrl: attachment.thumbnailUrl,
        uploadedAt: attachment.createdAt,
        description: attachment.description
      })),
      
      // Quality and review information
      qualityScore: report.qualityScore,
      reviewedBy: report.reviewedBy,
      reviewedAt: report.reviewedAt,
      reviewNotes: report.reviewNotes,
      
      // Review history
      reviewHistory: report.reviewHistory.map(review => ({
        id: review.id,
        action: review.action,
        notes: review.notes,
        qualityScore: review.qualityScore,
        reviewer: {
          name: `${review.reviewer.firstName} ${review.reviewer.lastName}`,
          role: review.reviewer.role
        },
        createdAt: review.createdAt
      })),
      
      // Metrics
      metrics: {
        responseTimeHours: responseTime ? Math.round(responseTime * 100) / 100 : null,
        isOverdue,
        daysOld: Math.floor((new Date() - new Date(report.createdAt)) / (1000 * 60 * 60 * 24)),
        attachmentCount: report.attachments.length,
        reviewCount: report.reviewHistory.length
      },
      
      // Timestamps
      createdAt: report.createdAt,
      submittedAt: report.submittedAt,
      updatedAt: report.updatedAt
    };

    return res.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get report by ID error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve report'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update report information
 */
async function updateReport(req, res, reportId) {
  try {
    const currentUser = req.user;
    const {
      title,
      description,
      priority,
      status,
      reviewNotes,
      qualityScore
    } = req.body;

    // Get existing report
    const existingReport = await prisma.report.findUnique({
      where: { id: reportId },
      include: {
        agent: {
          include: {
            user: true
          }
        }
      }
    });

    if (!existingReport) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check permissions
    if (currentUser.role === 'AGENT') {
      // Agents can only edit their own reports and only if not yet reviewed
      if (existingReport.agent.userId !== currentUser.id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Cannot edit another agent\'s report'
          },
          timestamp: new Date().toISOString()
        });
      }

      if (existingReport.status !== 'DRAFT' && existingReport.status !== 'SUBMITTED') {
        return res.status(400).json({
          success: false,
          error: {
            code: 'REPORT_LOCKED',
            message: 'Cannot edit report that has been reviewed'
          },
          timestamp: new Date().toISOString()
        });
      }

      // Agents can only update basic fields
      if (status && status !== 'SUBMITTED' && status !== 'DRAFT') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Agents cannot change status to this value'
          },
          timestamp: new Date().toISOString()
        });
      }
    } else if (currentUser.role === 'CLIENT') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Clients cannot edit reports'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare update data
    const updateData = {};
    
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (priority !== undefined) updateData.priority = priority;
    
    // Admin/Supervisor can update review fields
    if (['ADMIN', 'SUPERVISOR'].includes(currentUser.role)) {
      if (status !== undefined) updateData.status = status;
      if (reviewNotes !== undefined) updateData.reviewNotes = reviewNotes;
      if (qualityScore !== undefined) updateData.qualityScore = qualityScore;
      
      if (status && ['APPROVED', 'REJECTED'].includes(status)) {
        updateData.reviewedBy = currentUser.id;
        updateData.reviewedAt = new Date();
      }
    } else if (status !== undefined) {
      updateData.status = status;
      if (status === 'SUBMITTED') {
        updateData.submittedAt = new Date();
      }
    }

    updateData.updatedAt = new Date();

    // Update report
    const updatedReport = await prisma.report.update({
      where: { id: reportId },
      data: updateData,
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                companyName: true
              }
            }
          }
        }
      }
    });

    // Create review history entry for status changes
    if (status && ['APPROVED', 'REJECTED'].includes(status)) {
      await prisma.reportReview.create({
        data: {
          reportId,
          reviewerId: currentUser.id,
          action: status,
          notes: reviewNotes,
          qualityScore
        }
      });
    }

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'UPDATE_REPORT',
      tableName: 'reports',
      recordId: reportId,
      oldValues: existingReport,
      newValues: updateData,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: updatedReport,
      message: 'Report updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update report error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update report'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Delete report (admin only)
 */
async function deleteReport(req, res, reportId) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const existingReport = await prisma.report.findUnique({
      where: { id: reportId },
      include: {
        attachments: true,
        reviewHistory: true
      }
    });

    if (!existingReport) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if report can be deleted
    if (existingReport.status === 'APPROVED') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'REPORT_APPROVED',
          message: 'Cannot delete approved report'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Delete related records first
    await prisma.$transaction([
      // Delete review history
      prisma.reportReview.deleteMany({
        where: { reportId }
      }),
      // Disconnect attachments (don't delete the files)
      prisma.report.update({
        where: { id: reportId },
        data: {
          attachments: {
            disconnect: existingReport.attachments.map(att => ({ id: att.id }))
          }
        }
      }),
      // Delete the report
      prisma.report.delete({
        where: { id: reportId }
      })
    ]);

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'DELETE_REPORT',
      tableName: 'reports',
      recordId: reportId,
      oldValues: existingReport,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      message: 'Report deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete report error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to delete report'
      },
      timestamp: new Date().toISOString()
    });
  }
}
