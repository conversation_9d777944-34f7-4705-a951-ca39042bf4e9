// BahinLink Messages/Communications API
// ⚠️ CRITICAL: Real messaging system with threading ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getMessages(req, res);
    } else if (req.method === 'POST') {
      return await sendMessage(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Messages API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get messages/conversations for current user
 */
async function getMessages(req, res) {
  try {
    const {
      page = 1,
      limit = 50,
      conversationId,
      participantId,
      type = 'ALL',
      isRead,
      startDate,
      endDate
    } = req.query;

    const currentUser = req.user;

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build where clause
    const where = {
      OR: [
        { senderId: currentUser.id },
        { recipientId: currentUser.id }
      ],
      createdAt: {
        gte: start,
        lte: end
      }
    };

    // Additional filters
    if (conversationId) where.conversationId = conversationId;
    if (participantId) {
      where.OR = [
        { senderId: currentUser.id, recipientId: participantId },
        { senderId: participantId, recipientId: currentUser.id }
      ];
    }
    if (type !== 'ALL') where.type = type;
    if (isRead !== undefined) {
      where.isRead = isRead === 'true';
      where.recipientId = currentUser.id; // Only filter read status for received messages
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get messages with related data
    const [messages, totalCount] = await Promise.all([
      prisma.message.findMany({
        where,
        skip,
        take,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          sender: {
            select: {
              firstName: true,
              lastName: true,
              role: true
            }
          },
          recipient: {
            select: {
              firstName: true,
              lastName: true,
              role: true
            }
          },
          attachments: {
            select: {
              id: true,
              fileName: true,
              fileType: true,
              url: true,
              thumbnailUrl: true
            }
          },
          parentMessage: {
            select: {
              id: true,
              content: true,
              sender: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      }),
      prisma.message.count({ where })
    ]);

    // Process messages data
    const processedMessages = messages.map(message => ({
      id: message.id,
      conversationId: message.conversationId,
      type: message.type,
      subject: message.subject,
      content: message.content,
      priority: message.priority,
      isRead: message.isRead,
      readAt: message.readAt,
      
      // Sender information
      sender: {
        id: message.senderId,
        name: `${message.sender.firstName} ${message.sender.lastName}`,
        role: message.sender.role,
        isCurrentUser: message.senderId === currentUser.id
      },
      
      // Recipient information
      recipient: {
        id: message.recipientId,
        name: `${message.recipient.firstName} ${message.recipient.lastName}`,
        role: message.recipient.role,
        isCurrentUser: message.recipientId === currentUser.id
      },
      
      // Thread information
      parentMessage: message.parentMessage ? {
        id: message.parentMessage.id,
        content: message.parentMessage.content.substring(0, 100) + '...',
        senderName: `${message.parentMessage.sender.firstName} ${message.parentMessage.sender.lastName}`
      } : null,
      
      // Attachments
      attachments: message.attachments,
      
      // Metadata
      metadata: message.metadata,
      
      // Timestamps
      createdAt: message.createdAt,
      updatedAt: message.updatedAt
    }));

    // Group messages by conversation for better organization
    const conversations = {};
    processedMessages.forEach(message => {
      const convId = message.conversationId || `${Math.min(message.sender.id, message.recipient.id)}-${Math.max(message.sender.id, message.recipient.id)}`;
      
      if (!conversations[convId]) {
        conversations[convId] = {
          conversationId: convId,
          participants: [message.sender, message.recipient].filter((p, index, self) => 
            index === self.findIndex(participant => participant.id === p.id)
          ),
          messages: [],
          lastMessage: null,
          unreadCount: 0
        };
      }
      
      conversations[convId].messages.push(message);
      if (!conversations[convId].lastMessage || new Date(message.createdAt) > new Date(conversations[convId].lastMessage.createdAt)) {
        conversations[convId].lastMessage = message;
      }
      
      if (!message.isRead && message.recipient.isCurrentUser) {
        conversations[convId].unreadCount++;
      }
    });

    // Calculate summary statistics
    const summary = {
      totalMessages: totalCount,
      unreadMessages: processedMessages.filter(m => !m.isRead && m.recipient.isCurrentUser).length,
      sentMessages: processedMessages.filter(m => m.sender.isCurrentUser).length,
      receivedMessages: processedMessages.filter(m => m.recipient.isCurrentUser).length,
      conversations: Object.keys(conversations).length,
      highPriorityMessages: processedMessages.filter(m => m.priority === 'HIGH').length,
      urgentMessages: processedMessages.filter(m => m.priority === 'URGENT').length
    };

    return res.json({
      success: true,
      data: {
        messages: processedMessages,
        conversations: Object.values(conversations)
      },
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get messages error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve messages'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Send new message
 */
async function sendMessage(req, res) {
  try {
    const {
      recipientId,
      conversationId,
      type = 'DIRECT',
      subject,
      content,
      priority = 'MEDIUM',
      parentMessageId,
      attachmentIds = [],
      sendNotification = true,
      metadata = {}
    } = req.body;

    const currentUser = req.user;

    // Validate required fields
    if (!recipientId || !content) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Recipient ID and content are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate recipient exists and is active
    const recipient = await prisma.user.findUnique({
      where: {
        id: recipientId,
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true
      }
    });

    if (!recipient) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'RECIPIENT_NOT_FOUND',
          message: 'Recipient not found or inactive'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate parent message if replying
    let parentMessage = null;
    if (parentMessageId) {
      parentMessage = await prisma.message.findUnique({
        where: { id: parentMessageId },
        include: {
          sender: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        }
      });

      if (!parentMessage) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'PARENT_MESSAGE_NOT_FOUND',
            message: 'Parent message not found'
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    // Generate conversation ID if not provided
    const finalConversationId = conversationId || 
      `${Math.min(currentUser.id, recipientId)}-${Math.max(currentUser.id, recipientId)}`;

    // Create message
    const message = await prisma.message.create({
      data: {
        senderId: currentUser.id,
        recipientId,
        conversationId: finalConversationId,
        type,
        subject,
        content,
        priority,
        parentMessageId,
        metadata,
        isRead: false
      },
      include: {
        sender: {
          select: {
            firstName: true,
            lastName: true,
            role: true
          }
        },
        recipient: {
          select: {
            firstName: true,
            lastName: true,
            role: true
          }
        }
      }
    });

    // Attach files if provided
    if (attachmentIds.length > 0) {
      await prisma.message.update({
        where: { id: message.id },
        data: {
          attachments: {
            connect: attachmentIds.map(id => ({ id }))
          }
        }
      });
    }

    // TODO: Send notification if requested
    if (sendNotification) {
      // Implementation would go here for push notification
      console.log(`Message notification sent to ${recipient.firstName} ${recipient.lastName}`);
    }

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'SEND_MESSAGE',
      tableName: 'messages',
      recordId: message.id,
      newValues: {
        recipientId,
        type,
        priority,
        hasAttachments: attachmentIds.length > 0
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.status(201).json({
      success: true,
      data: {
        id: message.id,
        conversationId: message.conversationId,
        type: message.type,
        subject: message.subject,
        content: message.content,
        priority: message.priority,
        sender: {
          name: `${message.sender.firstName} ${message.sender.lastName}`,
          role: message.sender.role
        },
        recipient: {
          name: `${message.recipient.firstName} ${message.recipient.lastName}`,
          role: message.recipient.role
        },
        parentMessage: parentMessage ? {
          id: parentMessage.id,
          content: parentMessage.content.substring(0, 100) + '...',
          senderName: `${parentMessage.sender.firstName} ${parentMessage.sender.lastName}`
        } : null,
        attachmentCount: attachmentIds.length,
        createdAt: message.createdAt
      },
      message: 'Message sent successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Send message error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to send message'
      },
      timestamp: new Date().toISOString()
    });
  }
}
