// BahinLink Document Upload API
// ⚠️ CRITICAL: Real document upload to Vercel Blob storage ONLY

const { requireAuth } = require('../../lib/auth');
const { put } = require('@vercel/blob');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await uploadDocument(req, res);
  } catch (error) {
    console.error('Document upload API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Upload document with validation and metadata extraction
 */
async function uploadDocument(req, res) {
  try {
    const { file, fileName, description, category = 'GENERAL' } = req.body;
    const currentUser = req.user;

    // Validate required fields
    if (!file || !fileName) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'File data and filename are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Convert base64 to buffer
    let fileBuffer;
    let mimeType;
    
    try {
      if (file.startsWith('data:')) {
        const matches = file.match(/^data:([^;]+);base64,(.+)$/);
        if (!matches) {
          throw new Error('Invalid base64 format');
        }
        mimeType = matches[1];
        fileBuffer = Buffer.from(matches[2], 'base64');
      } else {
        fileBuffer = Buffer.from(file, 'base64');
        // Try to determine MIME type from filename
        const ext = fileName.toLowerCase().split('.').pop();
        mimeType = getMimeTypeFromExtension(ext);
      }
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_FILE_FORMAT',
          message: 'Invalid file format or encoding'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'application/rtf'
    ];

    if (!allowedTypes.includes(mimeType)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'UNSUPPORTED_FILE_TYPE',
          message: 'Unsupported document type. Allowed: PDF, Word, Excel, PowerPoint, Text, CSV, RTF'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate file size (max 10MB for documents)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (fileBuffer.length > maxSize) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'FILE_TOO_LARGE',
          message: 'Document size exceeds 10MB limit'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = fileName.split('.').pop();
    const uniqueFileName = `documents/${currentUser.id}/${timestamp}_${randomString}.${fileExtension}`;

    // Upload to Vercel Blob
    const blob = await put(uniqueFileName, fileBuffer, {
      access: 'public',
      contentType: mimeType
    });

    // Extract document metadata
    const metadata = {
      originalName: fileName,
      mimeType,
      size: fileBuffer.length,
      category,
      uploadedAt: new Date(),
      uploadedBy: currentUser.id
    };

    // Save attachment record to database
    const attachment = await prisma.attachment.create({
      data: {
        fileName: fileName,
        originalFileName: fileName,
        fileType: mimeType,
        fileSize: fileBuffer.length,
        url: blob.url,
        uploadedBy: currentUser.id,
        category: category,
        description: description || null,
        metadata: metadata
      }
    });

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'UPLOAD_DOCUMENT',
      tableName: 'attachments',
      recordId: attachment.id,
      newValues: {
        fileName,
        fileType: mimeType,
        fileSize: fileBuffer.length,
        category
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.status(201).json({
      success: true,
      data: {
        id: attachment.id,
        fileName: attachment.fileName,
        originalFileName: attachment.originalFileName,
        fileType: attachment.fileType,
        fileSize: attachment.fileSize,
        url: attachment.url,
        category: attachment.category,
        description: attachment.description,
        metadata: attachment.metadata,
        uploadedAt: attachment.createdAt,
        uploadedBy: {
          id: currentUser.id,
          name: `${currentUser.firstName} ${currentUser.lastName}`
        }
      },
      message: 'Document uploaded successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Document upload error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'UPLOAD_FAILED',
        message: 'Failed to upload document'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Get MIME type from file extension
 */
function getMimeTypeFromExtension(extension) {
  const mimeTypes = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt': 'text/plain',
    'csv': 'text/csv',
    'rtf': 'application/rtf'
  };

  return mimeTypes[extension] || 'application/octet-stream';
}
