// BahinLink Photo Upload API
// ⚠️ CRITICAL: Real file upload to Vercel Blob storage ONLY

const { requireAuth } = require('../../lib/auth');
const { put } = require('@vercel/blob');
const { prisma } = require('../../lib/prisma');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await uploadPhoto(req, res);
  } catch (error) {
    console.error('Photo upload API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Upload photo to Vercel Blob storage
 */
async function uploadPhoto(req, res) {
  try {
    const currentUser = req.user;

    // Check if file is provided
    if (!req.body || !req.body.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILE_PROVIDED',
          message: 'No file provided'
        },
        timestamp: new Date().toISOString()
      });
    }

    const {
      file,
      fileName,
      description,
      reportId,
      latitude,
      longitude
    } = req.body;

    // Validate file type (photos only)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const fileType = file.type || 'image/jpeg';
    
    if (!allowedTypes.includes(fileType)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_FILE_TYPE',
          message: 'Only JPEG, PNG, and WebP images are allowed'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'FILE_TOO_LARGE',
          message: 'File size must be less than 10MB'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = fileType.split('/')[1];
    const uniqueFileName = fileName || `photo_${timestamp}_${randomString}.${fileExtension}`;

    // Upload to Vercel Blob
    const blob = await put(uniqueFileName, file, {
      access: 'public',
      token: process.env.BLOB_READ_WRITE_TOKEN
    });

    if (!blob || !blob.url) {
      throw new Error('Failed to upload file to blob storage');
    }

    // Create attachment record in database
    const attachment = await prisma.attachment.create({
      data: {
        fileName: uniqueFileName,
        originalFileName: fileName || uniqueFileName,
        fileType,
        fileSize: file.size,
        url: blob.url,
        description,
        uploadedBy: currentUser.id,
        latitude: latitude ? parseFloat(latitude) : null,
        longitude: longitude ? parseFloat(longitude) : null,
        ...(reportId && { reportId })
      }
    });

    // If this is for a report, update the report
    if (reportId) {
      await prisma.report.update({
        where: { id: reportId },
        data: {
          attachments: {
            connect: { id: attachment.id }
          }
        }
      });
    }

    return res.status(201).json({
      success: true,
      data: {
        id: attachment.id,
        fileName: attachment.fileName,
        originalFileName: attachment.originalFileName,
        fileType: attachment.fileType,
        fileSize: attachment.fileSize,
        url: attachment.url,
        description: attachment.description,
        uploadedAt: attachment.createdAt,
        location: attachment.latitude && attachment.longitude ? {
          latitude: attachment.latitude,
          longitude: attachment.longitude
        } : null
      },
      message: 'Photo uploaded successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Upload photo error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'UPLOAD_FAILED',
        message: 'Failed to upload photo'
      },
      timestamp: new Date().toISOString()
    });
  }
}
