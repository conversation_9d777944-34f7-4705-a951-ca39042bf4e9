// BahinLink Agent Location API
// ⚠️ CRITICAL: Real GPS tracking and database integration ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { cacheAgentLocation, getCachedAgentLocation } = require('../../lib/redis');
const { calculateDistance } = require('@bahinlink/shared');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getAgentLocation(req, res);
    } else if (req.method === 'POST') {
      return await updateAgentLocation(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Agent location API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get current agent location with real data
 */
async function getAgentLocation(req, res) {
  try {
    const user = req.user;

    // Only agents can get their own location, supervisors/admins can get any agent's location
    let agentId;
    if (user.role === 'AGENT') {
      if (!user.agent) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_USER_TYPE',
            message: 'User is not an agent'
          },
          timestamp: new Date().toISOString()
        });
      }
      agentId = user.agent.id;
    } else if (['SUPERVISOR', 'ADMIN'].includes(user.role)) {
      agentId = req.query.agentId;
      if (!agentId) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_AGENT_ID',
            message: 'Agent ID is required'
          },
          timestamp: new Date().toISOString()
        });
      }
    } else {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get agent data from real database
    const agent = await prisma.agent.findUnique({
      where: { id: agentId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            phone: true
          }
        },
        shifts: {
          where: {
            status: 'IN_PROGRESS',
            shiftDate: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
              lt: new Date(new Date().setHours(23, 59, 59, 999))
            }
          },
          include: {
            site: {
              select: {
                id: true,
                name: true,
                latitude: true,
                longitude: true,
                geofenceRadius: true
              }
            }
          },
          take: 1
        }
      }
    });

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Agent not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare location data
    const locationData = {
      agentId: agent.id,
      agentName: `${agent.user.firstName} ${agent.user.lastName}`,
      employeeId: agent.employeeId,
      location: {
        latitude: agent.currentLatitude,
        longitude: agent.currentLongitude,
        lastUpdate: agent.lastLocationUpdate
      },
      isAvailable: agent.isAvailable,
      currentShift: null
    };

    // Add current shift information if agent is on duty
    if (agent.shifts.length > 0) {
      const currentShift = agent.shifts[0];
      const site = currentShift.site;
      
      // Calculate distance from site if location is available
      let distanceFromSite = null;
      let withinGeofence = null;
      
      if (agent.currentLatitude && agent.currentLongitude) {
        distanceFromSite = calculateDistance(
          agent.currentLatitude,
          agent.currentLongitude,
          site.latitude,
          site.longitude
        );
        withinGeofence = distanceFromSite <= site.geofenceRadius;
      }

      locationData.currentShift = {
        id: currentShift.id,
        siteId: site.id,
        siteName: site.name,
        status: currentShift.status,
        startTime: currentShift.startTime,
        endTime: currentShift.endTime,
        siteLocation: {
          latitude: site.latitude,
          longitude: site.longitude
        },
        geofenceRadius: site.geofenceRadius,
        distanceFromSite,
        withinGeofence
      };
    }

    return res.json({
      success: true,
      data: locationData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get agent location error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve agent location'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update agent location with real GPS coordinates
 */
async function updateAgentLocation(req, res) {
  try {
    const user = req.user;
    const { latitude, longitude, accuracy, timestamp } = req.body;

    // Only agents can update their own location
    if (user.role !== 'AGENT' || !user.agent) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Only agents can update their location'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate GPS coordinates
    if (!latitude || !longitude || 
        latitude < -90 || latitude > 90 || 
        longitude < -180 || longitude > 180) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid GPS coordinates'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate accuracy (should be reasonable for GPS)
    if (accuracy && (accuracy < 0 || accuracy > 1000)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid GPS accuracy'
        },
        timestamp: new Date().toISOString()
      });
    }

    const agentId = user.agent.id;
    const locationTimestamp = timestamp ? new Date(timestamp) : new Date();

    // Update location in real database
    const updatedAgent = await prisma.agent.update({
      where: { id: agentId },
      data: {
        currentLatitude: latitude,
        currentLongitude: longitude,
        lastLocationUpdate: locationTimestamp
      }
    });

    // Cache the location for real-time features
    const locationData = {
      agentId,
      latitude,
      longitude,
      accuracy,
      timestamp: locationTimestamp.toISOString()
    };
    await cacheAgentLocation(agentId, locationData);

    // Check for geofence violations if agent is on active shift
    const activeShift = await prisma.shift.findFirst({
      where: {
        agentId,
        status: 'IN_PROGRESS',
        shiftDate: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
          lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
      },
      include: {
        site: true
      }
    });

    let geofenceStatus = null;
    if (activeShift) {
      const site = activeShift.site;
      const distanceFromSite = calculateDistance(
        latitude,
        longitude,
        site.latitude,
        site.longitude
      );
      
      const withinGeofence = distanceFromSite <= site.geofenceRadius;
      
      geofenceStatus = {
        withinGeofence,
        distanceFromSite,
        geofenceRadius: site.geofenceRadius,
        siteName: site.name
      };

      // Log geofence violation if outside
      if (!withinGeofence && distanceFromSite > site.geofenceRadius * 1.2) { // 20% buffer
        await prisma.geofenceViolation.create({
          data: {
            agentId,
            siteId: site.id,
            shiftId: activeShift.id,
            violationType: 'OUTSIDE_GEOFENCE',
            agentLatitude: latitude,
            agentLongitude: longitude,
            distanceFromSite,
            durationMinutes: 1 // Will be updated by background job
          }
        });
      }
    }

    // Emit real-time location update (will be handled by Socket.io in real implementation)
    // For now, we'll store it for real-time sync
    const { storeOfflineEvent } = require('../../lib/redis');
    await storeOfflineEvent(user.id, {
      type: 'location_update',
      data: locationData
    });

    return res.json({
      success: true,
      data: {
        agentId,
        location: {
          latitude,
          longitude,
          accuracy,
          timestamp: locationTimestamp
        },
        geofenceStatus
      },
      message: 'Location updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update agent location error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update agent location'
      },
      timestamp: new Date().toISOString()
    });
  }
}
