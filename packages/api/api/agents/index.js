// BahinLink Agents Management API
// ⚠️ CRITICAL: Real agent management with GPS tracking ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { calculateDistance } = require('@bahinlink/shared');
const { getCachedAgentLocation } = require('../../lib/redis');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getAgents(req, res);
    } else if (req.method === 'POST') {
      return await createAgent(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Agents API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get agents with filtering, pagination, and real-time data
 */
async function getAgents(req, res) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      page = 1,
      limit = 50,
      status,
      isAvailable,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      includeLocation = 'true',
      includePerformance = 'true'
    } = req.query;

    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    // Build where clause
    const where = {};
    
    if (isAvailable !== undefined) {
      where.isAvailable = isAvailable === 'true';
    }
    
    if (search) {
      where.OR = [
        { employeeId: { contains: search, mode: 'insensitive' } },
        { user: {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        }}
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get agents with related data
    const [agents, totalCount] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              isActive: true
            }
          },
          shifts: {
            where: {
              shiftDate: {
                gte: startOfDay,
                lte: endOfDay
              }
            },
            include: {
              site: {
                select: {
                  id: true,
                  name: true,
                  address: true,
                  latitude: true,
                  longitude: true,
                  geofenceRadius: true
                }
              },
              timeEntries: {
                where: {
                  clockInTime: {
                    not: null
                  },
                  clockOutTime: null
                }
              }
            }
          }
        }
      }),
      prisma.agent.count({ where })
    ]);

    // Process agents data with real-time information
    const processedAgents = await Promise.all(agents.map(async (agent) => {
      // Get current shift
      const currentShift = agent.shifts.find(shift => 
        shift.status === 'IN_PROGRESS' && shift.timeEntries.length > 0
      );

      // Get cached location for more recent updates
      let location = null;
      if (includeLocation === 'true') {
        const cachedLocation = await getCachedAgentLocation(agent.id);
        
        location = cachedLocation && 
          new Date(cachedLocation.timestamp) > (agent.lastLocationUpdate || new Date(0))
          ? cachedLocation
          : {
              latitude: agent.currentLatitude,
              longitude: agent.currentLongitude,
              accuracy: 10,
              timestamp: agent.lastLocationUpdate?.toISOString()
            };
      }

      // Calculate distance from assigned site if on shift
      let distanceFromSite = null;
      let withinGeofence = null;
      
      if (currentShift && location?.latitude && location?.longitude) {
        const site = currentShift.site;
        distanceFromSite = calculateDistance(
          location.latitude,
          location.longitude,
          site.latitude,
          site.longitude
        );
        withinGeofence = distanceFromSite <= site.geofenceRadius;
      }

      // Determine agent status
      let agentStatus = 'OFFLINE';
      if (agent.isAvailable) {
        if (currentShift) {
          agentStatus = 'ON_SHIFT';
        } else {
          agentStatus = 'AVAILABLE';
        }
      }

      // Performance metrics
      const performanceStats = agent.performanceStats || {
        totalShifts: 0,
        onTimePercentage: 0,
        clientSatisfactionScore: 0,
        reportQualityScore: 0
      };

      return {
        id: agent.id,
        employeeId: agent.employeeId,
        user: {
          id: agent.user.id,
          name: `${agent.user.firstName} ${agent.user.lastName}`,
          firstName: agent.user.firstName,
          lastName: agent.user.lastName,
          email: agent.user.email,
          phone: agent.user.phone,
          isActive: agent.user.isActive
        },
        
        // Status and availability
        status: agentStatus,
        isAvailable: agent.isAvailable,
        
        // Location data
        location: includeLocation === 'true' && location?.latitude ? {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy,
          lastUpdate: location.timestamp,
          distanceFromSite: distanceFromSite ? Math.round(distanceFromSite) : null,
          withinGeofence
        } : null,
        
        // Current assignment
        currentShift: currentShift ? {
          id: currentShift.id,
          site: {
            id: currentShift.site.id,
            name: currentShift.site.name,
            address: currentShift.site.address
          },
          startTime: currentShift.startTime,
          endTime: currentShift.endTime,
          actualStartTime: currentShift.actualStartTime,
          status: currentShift.status,
          clockedIn: currentShift.timeEntries.length > 0,
          clockInTime: currentShift.timeEntries[0]?.clockInTime
        } : null,
        
        // Professional information
        certifications: agent.certifications || [],
        skills: agent.skills || [],
        hireDate: agent.hireDate,
        
        // Performance metrics
        performance: includePerformance === 'true' ? {
          totalShifts: performanceStats.totalShifts,
          onTimePercentage: performanceStats.onTimePercentage,
          clientSatisfactionScore: performanceStats.clientSatisfactionScore,
          reportQualityScore: performanceStats.reportQualityScore,
          overallScore: (
            (performanceStats.onTimePercentage / 100) * 0.3 +
            (performanceStats.clientSatisfactionScore / 5) * 0.4 +
            (performanceStats.reportQualityScore / 5) * 0.3
          ) * 100
        } : null,
        
        // Emergency contact
        emergencyContact: {
          name: agent.emergencyContactName,
          phone: agent.emergencyContactPhone
        },
        
        // Timestamps
        createdAt: agent.createdAt,
        updatedAt: agent.updatedAt,
        lastLocationUpdate: agent.lastLocationUpdate
      };
    }));

    // Apply status filter after processing
    const filteredAgents = status 
      ? processedAgents.filter(agent => agent.status === status)
      : processedAgents;

    return res.json({
      success: true,
      data: filteredAgents,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary: {
        totalAgents: totalCount,
        availableAgents: filteredAgents.filter(a => a.status === 'AVAILABLE').length,
        onShiftAgents: filteredAgents.filter(a => a.status === 'ON_SHIFT').length,
        offlineAgents: filteredAgents.filter(a => a.status === 'OFFLINE').length,
        agentsWithLocation: filteredAgents.filter(a => a.location).length,
        agentsWithinGeofence: filteredAgents.filter(a => a.location?.withinGeofence === true).length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get agents error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve agents'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Create new agent profile
 */
async function createAgent(req, res) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      userId,
      employeeId,
      certifications = [],
      skills = [],
      hireDate,
      emergencyContactName,
      emergencyContactPhone
    } = req.body;

    // Validate required fields
    if (!userId || !employeeId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields: userId, employeeId'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if user exists and is not already an agent
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { agent: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (user.agent) {
      return res.status(409).json({
        success: false,
        error: {
          code: 'AGENT_EXISTS',
          message: 'User is already an agent'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if employee ID is unique
    const existingAgent = await prisma.agent.findUnique({
      where: { employeeId }
    });

    if (existingAgent) {
      return res.status(409).json({
        success: false,
        error: {
          code: 'EMPLOYEE_ID_EXISTS',
          message: 'Employee ID already exists'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Create agent profile
    const newAgent = await prisma.agent.create({
      data: {
        userId,
        employeeId,
        certifications,
        skills,
        hireDate: hireDate ? new Date(hireDate) : new Date(),
        emergencyContactName,
        emergencyContactPhone,
        isAvailable: true,
        performanceStats: {
          totalShifts: 0,
          onTimePercentage: 100,
          clientSatisfactionScore: 5.0,
          reportQualityScore: 5.0
        }
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true
          }
        }
      }
    });

    return res.status(201).json({
      success: true,
      data: newAgent,
      message: 'Agent profile created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Create agent error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to create agent profile'
      },
      timestamp: new Date().toISOString()
    });
  }
}
