// BahinLink Agent Details API
// ⚠️ CRITICAL: Real agent management with GPS tracking and performance metrics ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');
const { calculateDistance } = require('@bahinlink/shared');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { id } = req.query;

    if (req.method === 'GET') {
      return await getAgentById(req, res, id);
    } else if (req.method === 'PUT') {
      return await updateAgent(req, res, id);
    } else if (req.method === 'DELETE') {
      return await deleteAgent(req, res, id);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Agent details API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get agent by ID with comprehensive data
 */
async function getAgentById(req, res, agentId) {
  try {
    const currentUser = req.user;

    // Check permissions - agents can view their own profile, admins/supervisors can view all
    const agent = await prisma.agent.findUnique({
      where: { id: agentId },
      include: {
        user: true
      }
    });

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (currentUser.id !== agent.userId && !['ADMIN', 'SUPERVISOR'].includes(currentUser.role)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to view this agent'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get comprehensive agent data
    const [shifts, reports, timeEntries, locationHistory] = await Promise.all([
      // Recent shifts (last 30 days)
      prisma.shift.findMany({
        where: {
          agentId,
          shiftDate: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          site: {
            select: {
              id: true,
              name: true,
              address: true,
              latitude: true,
              longitude: true
            }
          },
          timeEntries: true
        },
        orderBy: {
          shiftDate: 'desc'
        },
        take: 20
      }),

      // Recent reports (last 30 days)
      prisma.report.findMany({
        where: {
          agentId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          site: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }),

      // Recent time entries
      prisma.timeEntry.findMany({
        where: {
          shift: {
            agentId
          },
          clockInTime: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          shift: {
            include: {
              site: {
                select: {
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          clockInTime: 'desc'
        },
        take: 10
      }),

      // Location history (last 24 hours)
      prisma.locationUpdate.findMany({
        where: {
          agentId,
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        },
        orderBy: {
          timestamp: 'desc'
        },
        take: 100
      })
    ]);

    // Calculate performance metrics
    const completedShifts = shifts.filter(s => s.status === 'COMPLETED');
    const onTimeShifts = completedShifts.filter(s => 
      s.actualStartTime && new Date(s.actualStartTime) <= new Date(s.startTime)
    );
    
    const performanceMetrics = {
      totalShifts: completedShifts.length,
      onTimePercentage: completedShifts.length > 0 ? 
        Math.round((onTimeShifts.length / completedShifts.length) * 100) : 100,
      averageHoursPerShift: completedShifts.length > 0 ?
        completedShifts.reduce((sum, shift) => {
          const duration = shift.actualEndTime && shift.actualStartTime ?
            (new Date(shift.actualEndTime) - new Date(shift.actualStartTime)) / (1000 * 60 * 60) : 0;
          return sum + duration;
        }, 0) / completedShifts.length : 0,
      reportsSubmitted: reports.length,
      averageReportQuality: reports.length > 0 ?
        reports.reduce((sum, report) => sum + (report.qualityScore || 5), 0) / reports.length : 5
    };

    // Current shift information
    const currentShift = shifts.find(s => s.status === 'IN_PROGRESS');
    let currentShiftDetails = null;
    
    if (currentShift) {
      const activeTimeEntry = currentShift.timeEntries.find(te => te.clockInTime && !te.clockOutTime);
      currentShiftDetails = {
        ...currentShift,
        isActive: !!activeTimeEntry,
        clockInTime: activeTimeEntry?.clockInTime,
        hoursWorked: activeTimeEntry?.clockInTime ?
          (new Date() - new Date(activeTimeEntry.clockInTime)) / (1000 * 60 * 60) : 0
      };
    }

    const agentResponse = {
      id: agent.id,
      employeeId: agent.employeeId,
      user: {
        id: agent.user.id,
        firstName: agent.user.firstName,
        lastName: agent.user.lastName,
        email: agent.user.email,
        phone: agent.user.phone,
        isActive: agent.user.isActive
      },
      
      // Professional information
      certifications: agent.certifications || [],
      skills: agent.skills || [],
      hireDate: agent.hireDate,
      isAvailable: agent.isAvailable,
      
      // Current status
      currentLocation: agent.currentLatitude && agent.currentLongitude ? {
        latitude: agent.currentLatitude,
        longitude: agent.currentLongitude,
        lastUpdate: agent.lastLocationUpdate
      } : null,
      
      currentShift: currentShiftDetails,
      
      // Performance data
      performanceStats: agent.performanceStats || {},
      calculatedMetrics: performanceMetrics,
      
      // Emergency contact
      emergencyContact: {
        name: agent.emergencyContactName,
        phone: agent.emergencyContactPhone
      },
      
      // Historical data
      recentShifts: shifts.map(shift => ({
        id: shift.id,
        site: shift.site,
        shiftDate: shift.shiftDate,
        startTime: shift.startTime,
        endTime: shift.endTime,
        actualStartTime: shift.actualStartTime,
        actualEndTime: shift.actualEndTime,
        status: shift.status,
        hoursWorked: shift.actualStartTime && shift.actualEndTime ?
          (new Date(shift.actualEndTime) - new Date(shift.actualStartTime)) / (1000 * 60 * 60) : 0
      })),
      
      recentReports: reports.map(report => ({
        id: report.id,
        type: report.type,
        title: report.title,
        status: report.status,
        site: report.site,
        createdAt: report.createdAt,
        qualityScore: report.qualityScore
      })),
      
      recentTimeEntries: timeEntries.map(entry => ({
        id: entry.id,
        clockInTime: entry.clockInTime,
        clockOutTime: entry.clockOutTime,
        site: entry.shift.site.name,
        hoursWorked: entry.clockInTime && entry.clockOutTime ?
          (new Date(entry.clockOutTime) - new Date(entry.clockInTime)) / (1000 * 60 * 60) : 0
      })),
      
      locationHistory: locationHistory.map(loc => ({
        latitude: loc.latitude,
        longitude: loc.longitude,
        accuracy: loc.accuracy,
        timestamp: loc.timestamp
      })),
      
      // Timestamps
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt
    };

    return res.json({
      success: true,
      data: agentResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get agent by ID error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve agent'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update agent profile
 */
async function updateAgent(req, res, agentId) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      certifications,
      skills,
      isAvailable,
      emergencyContactName,
      emergencyContactPhone,
      performanceStats
    } = req.body;

    const existingAgent = await prisma.agent.findUnique({
      where: { id: agentId }
    });

    if (!existingAgent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare update data
    const updateData = {};
    
    if (certifications !== undefined) updateData.certifications = certifications;
    if (skills !== undefined) updateData.skills = skills;
    if (isAvailable !== undefined) updateData.isAvailable = isAvailable;
    if (emergencyContactName !== undefined) updateData.emergencyContactName = emergencyContactName;
    if (emergencyContactPhone !== undefined) updateData.emergencyContactPhone = emergencyContactPhone;
    if (performanceStats !== undefined) updateData.performanceStats = performanceStats;

    const updatedAgent = await prisma.agent.update({
      where: { id: agentId },
      data: updateData,
      include: {
        user: true
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'UPDATE_AGENT',
      tableName: 'agents',
      recordId: agentId,
      oldValues: existingAgent,
      newValues: updateData,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: updatedAgent,
      message: 'Agent updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update agent error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update agent'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Soft delete agent (deactivate)
 */
async function deleteAgent(req, res, agentId) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const existingAgent = await prisma.agent.findUnique({
      where: { id: agentId },
      include: { user: true }
    });

    if (!existingAgent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Deactivate agent and user
    await prisma.$transaction([
      prisma.agent.update({
        where: { id: agentId },
        data: { isAvailable: false }
      }),
      prisma.user.update({
        where: { id: existingAgent.userId },
        data: { isActive: false }
      })
    ]);

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'DELETE_AGENT',
      tableName: 'agents',
      recordId: agentId,
      oldValues: existingAgent,
      newValues: { isAvailable: false },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      message: 'Agent deactivated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete agent error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to deactivate agent'
      },
      timestamp: new Date().toISOString()
    });
  }
}
