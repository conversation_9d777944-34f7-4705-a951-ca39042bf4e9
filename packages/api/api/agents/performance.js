// BahinLink Agent Performance API
// ⚠️ CRITICAL: Real performance metrics and analytics ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await getAgentPerformance(req, res);
  } catch (error) {
    console.error('Agent performance API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get agent performance metrics
 */
async function getAgentPerformance(req, res) {
  try {
    const {
      agentId,
      startDate,
      endDate,
      includeComparison = 'false'
    } = req.query;

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Build where clause
    const where = {
      shiftDate: {
        gte: start,
        lte: end
      }
    };

    if (agentId) {
      where.agentId = agentId;
    }

    // Get shifts data
    const shifts = await prisma.shift.findMany({
      where,
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          select: {
            name: true,
            client: {
              select: {
                companyName: true
              }
            }
          }
        },
        timeEntries: true,
        reports: true
      }
    });

    // Get reports data
    const reports = await prisma.report.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end
        },
        ...(agentId && { agentId })
      },
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    // Calculate performance metrics by agent
    const agentMetrics = {};

    shifts.forEach(shift => {
      const agentKey = shift.agentId;
      
      if (!agentMetrics[agentKey]) {
        agentMetrics[agentKey] = {
          agentId: shift.agentId,
          agentName: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
          employeeId: shift.agent.employeeId,
          totalShifts: 0,
          completedShifts: 0,
          onTimeShifts: 0,
          totalHours: 0,
          overtimeHours: 0,
          reportsSubmitted: 0,
          averageReportQuality: 0,
          clientSatisfaction: 0,
          geofenceViolations: 0,
          sites: new Set(),
          clients: new Set()
        };
      }

      const metrics = agentMetrics[agentKey];
      metrics.totalShifts++;
      
      if (shift.status === 'COMPLETED') {
        metrics.completedShifts++;
        
        // Check if on time
        if (shift.actualStartTime && new Date(shift.actualStartTime) <= new Date(shift.startTime)) {
          metrics.onTimeShifts++;
        }
        
        // Calculate hours worked
        if (shift.actualStartTime && shift.actualEndTime) {
          const hoursWorked = (new Date(shift.actualEndTime) - new Date(shift.actualStartTime)) / (1000 * 60 * 60);
          metrics.totalHours += hoursWorked;
          
          // Calculate overtime (assuming 8-hour shifts)
          const scheduledHours = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
          if (hoursWorked > scheduledHours) {
            metrics.overtimeHours += (hoursWorked - scheduledHours);
          }
        }
      }
      
      // Track sites and clients
      metrics.sites.add(shift.site.name);
      metrics.clients.add(shift.site.client.companyName);
    });

    // Process reports
    reports.forEach(report => {
      const agentKey = report.agentId;
      
      if (agentMetrics[agentKey]) {
        agentMetrics[agentKey].reportsSubmitted++;
        
        if (report.qualityScore) {
          agentMetrics[agentKey].averageReportQuality += report.qualityScore;
        }
      }
    });

    // Calculate final metrics
    const performanceData = Object.values(agentMetrics).map(metrics => {
      const onTimePercentage = metrics.completedShifts > 0 ? 
        (metrics.onTimeShifts / metrics.completedShifts) * 100 : 100;
      
      const averageHoursPerShift = metrics.completedShifts > 0 ?
        metrics.totalHours / metrics.completedShifts : 0;
      
      const averageReportQuality = metrics.reportsSubmitted > 0 ?
        metrics.averageReportQuality / metrics.reportsSubmitted : 5;
      
      // Overall performance score calculation
      const performanceScore = (
        (onTimePercentage / 100) * 0.3 +
        (averageReportQuality / 5) * 0.3 +
        (metrics.completedShifts / metrics.totalShifts) * 0.2 +
        (Math.min(averageHoursPerShift / 8, 1)) * 0.2
      ) * 100;

      return {
        ...metrics,
        sites: Array.from(metrics.sites),
        clients: Array.from(metrics.clients),
        onTimePercentage: Math.round(onTimePercentage),
        averageHoursPerShift: Math.round(averageHoursPerShift * 100) / 100,
        averageReportQuality: Math.round(averageReportQuality * 100) / 100,
        performanceScore: Math.round(performanceScore),
        efficiency: metrics.totalShifts > 0 ? 
          Math.round((metrics.completedShifts / metrics.totalShifts) * 100) : 0
      };
    });

    // Sort by performance score
    performanceData.sort((a, b) => b.performanceScore - a.performanceScore);

    // Calculate overall statistics
    const overallStats = {
      totalAgents: performanceData.length,
      totalShifts: performanceData.reduce((sum, agent) => sum + agent.totalShifts, 0),
      totalHours: performanceData.reduce((sum, agent) => sum + agent.totalHours, 0),
      averagePerformanceScore: performanceData.length > 0 ?
        Math.round(performanceData.reduce((sum, agent) => sum + agent.performanceScore, 0) / performanceData.length) : 0,
      averageOnTimePercentage: performanceData.length > 0 ?
        Math.round(performanceData.reduce((sum, agent) => sum + agent.onTimePercentage, 0) / performanceData.length) : 0,
      topPerformer: performanceData[0] || null,
      improvementNeeded: performanceData.filter(agent => agent.performanceScore < 70).length
    };

    // Comparison data (if requested)
    let comparisonData = null;
    if (includeComparison === 'true') {
      // Get previous period data for comparison
      const previousStart = new Date(start.getTime() - (end.getTime() - start.getTime()));
      const previousEnd = start;
      
      // This would involve similar calculations for the previous period
      // Simplified for brevity
      comparisonData = {
        previousPeriod: {
          start: previousStart,
          end: previousEnd,
          averagePerformanceScore: 85, // Would be calculated
          trend: 'improving' // 'improving', 'declining', 'stable'
        }
      };
    }

    return res.json({
      success: true,
      data: {
        agents: performanceData,
        overallStats,
        comparisonData,
        period: {
          start,
          end,
          days: Math.ceil((end - start) / (1000 * 60 * 60 * 24))
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get agent performance error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve performance data'
      },
      timestamp: new Date().toISOString()
    });
  }
}
