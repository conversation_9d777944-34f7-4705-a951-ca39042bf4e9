// BahinLink Auth Reset Password API
// ⚠️ CRITICAL: Real Clerk password reset completion ONLY

const { clerkClient } = require('@clerk/express');
const { prisma } = require('../../lib/prisma');
const { logAuditEvent } = require('../../lib/auth');
const { invalidateUserSession } = require('../../lib/redis');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { token, password, confirmPassword, clerkResetToken } = req.body;

    // Validate required fields
    if (!password || !confirmPassword) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Password and confirm password are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate password match
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Passwords do not match'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate password strength
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Password must be at least 8 characters long'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check for at least one uppercase, one lowercase, one number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        },
        timestamp: new Date().toISOString()
      });
    }

    let user;

    // Handle Clerk-based password reset
    if (clerkResetToken) {
      try {
        // Use Clerk to complete password reset
        const resetResult = await clerkClient.users.completePasswordReset({
          token: clerkResetToken,
          password: password
        });

        if (resetResult && resetResult.userId) {
          // Find user by Clerk ID
          user = await prisma.user.findUnique({
            where: { clerkId: resetResult.userId }
          });
        }
      } catch (clerkError) {
        console.error('Clerk password reset completion error:', clerkError);
        return res.status(400).json({
          success: false,
          error: {
            code: 'RESET_TOKEN_INVALID',
            message: 'Invalid or expired reset token'
          },
          timestamp: new Date().toISOString()
        });
      }
    }
    
    // Handle custom token-based password reset (for non-Clerk users)
    else if (token) {
      // Find user by reset token
      user = await prisma.user.findFirst({
        where: {
          resetToken: token,
          resetTokenExpiry: {
            gt: new Date()
          }
        }
      });

      if (!user) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'RESET_TOKEN_INVALID',
            message: 'Invalid or expired reset token'
          },
          timestamp: new Date().toISOString()
        });
      }

      // For non-Clerk users, we would hash and store the password
      // This is a simplified implementation - in production, use proper password hashing
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash(password, 12);

      // Update user password and clear reset token
      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordHash: hashedPassword,
          resetToken: null,
          resetTokenExpiry: null,
          updatedAt: new Date()
        }
      });
    } else {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Reset token is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'RESET_TOKEN_INVALID',
          message: 'Invalid or expired reset token'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Invalidate all existing sessions for this user
    await invalidateUserSession(user.id);

    // Log successful password reset
    await logAuditEvent({
      userId: user.id,
      action: 'PASSWORD_RESET_COMPLETED',
      apiEndpoint: '/api/auth/reset-password',
      httpMethod: 'POST',
      responseStatus: 200,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.json({
      success: true,
      data: {
        message: 'Password reset successfully. Please log in with your new password.'
      },
      message: 'Password reset completed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Reset password API error:', error);
    
    // Log the error
    await logAuditEvent({
      userId: null,
      action: 'PASSWORD_RESET_ERROR',
      apiEndpoint: '/api/auth/reset-password',
      httpMethod: 'POST',
      responseStatus: 500,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { error: error.message }
    });

    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};
