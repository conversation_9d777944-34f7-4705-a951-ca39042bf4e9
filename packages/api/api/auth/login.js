// BahinLink Auth Login API
// ⚠️ CRITICAL: Real Clerk authentication integration ONLY

const { clerkClient } = require('@clerk/express');
const { prisma } = require('../../lib/prisma');
const { cacheUserSession } = require('../../lib/redis');
const { logAuditEvent } = require('../../lib/auth');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { email, password, sessionToken } = req.body;

    // If sessionToken is provided, validate it directly (for mobile apps)
    if (sessionToken) {
      return await validateSessionToken(req, res, sessionToken);
    }

    // For email/password login, we need to use Clerk's authentication
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Email and password are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid email format'
        },
        timestamp: new Date().toISOString()
      });
    }

    // For production, we rely on Clerk's frontend authentication
    // This endpoint primarily validates existing sessions
    return res.status(400).json({
      success: false,
      error: {
        code: 'AUTH_METHOD_NOT_SUPPORTED',
        message: 'Direct email/password authentication not supported. Use Clerk frontend authentication.'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Login API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Validate session token and return user data
 */
async function validateSessionToken(req, res, sessionToken) {
  try {
    // Verify token with Clerk
    const session = await clerkClient.verifyToken(sessionToken);
    
    if (!session) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_INVALID',
          message: 'Invalid session token'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get user from database using Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: session.sub },
      include: {
        agent: {
          include: {
            shifts: {
              where: {
                shiftDate: {
                  gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
              },
              include: {
                site: {
                  include: {
                    client: true
                  }
                }
              },
              orderBy: {
                startTime: 'asc'
              },
              take: 3
            }
          }
        },
        client: {
          include: {
            sites: {
              where: { isActive: true },
              take: 5
            }
          }
        }
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_INVALID',
          message: 'User not found or inactive'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update last login time
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Cache user session
    const sessionData = {
      id: user.id,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      isActive: user.isActive,
      agent: user.agent,
      client: user.client,
      lastLoginAt: new Date()
    };

    await cacheUserSession(user.id, sessionData);

    // Log audit event
    await logAuditEvent({
      userId: user.id,
      action: 'LOGIN',
      apiEndpoint: '/api/auth/login',
      httpMethod: 'POST',
      responseStatus: 200,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone,
          isActive: user.isActive,
          agent: user.agent,
          client: user.client
        },
        session: {
          token: sessionToken,
          expiresAt: new Date(session.exp * 1000)
        }
      },
      message: 'Login successful',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Session validation error:', error);
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTH_INVALID',
        message: 'Session validation failed'
      },
      timestamp: new Date().toISOString()
    });
  }
}
