// BahinLink Auth Logout API
// ⚠️ CRITICAL: Real Clerk authentication and session cleanup ONLY

const { requireAuth, logAuditEvent } = require('../../lib/auth');
const { invalidateUserSession } = require('../../lib/redis');
const { clerkClient } = require('@clerk/express');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const userId = req.user.id;
    const sessionToken = req.headers.authorization?.substring(7);

    // Log audit event before logout
    await logAuditEvent({
      userId: userId,
      action: 'LOGOUT',
      apiEndpoint: '/api/auth/logout',
      httpMethod: 'POST',
      responseStatus: 200,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Invalidate session in Redis cache
    await invalidateUserSession(userId);

    // For Clerk, we don't need to explicitly invalidate the session on the server
    // as Clerk handles session management on the client side
    // However, we can revoke the session if needed
    try {
      if (req.clerkSession && req.clerkSession.id) {
        // Revoke the session in Clerk (optional)
        await clerkClient.sessions.revokeSession(req.clerkSession.id);
      }
    } catch (clerkError) {
      // Log but don't fail the logout if Clerk revocation fails
      console.warn('Clerk session revocation warning:', clerkError.message);
    }

    return res.json({
      success: true,
      data: {
        message: 'Logged out successfully'
      },
      message: 'Logout successful',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Logout API error:', error);
    
    // Even if there's an error, we should try to clean up
    try {
      if (req.user?.id) {
        await invalidateUserSession(req.user.id);
      }
    } catch (cleanupError) {
      console.error('Cleanup error during logout:', cleanupError);
    }

    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Logout failed'
      },
      timestamp: new Date().toISOString()
    });
  }
};
