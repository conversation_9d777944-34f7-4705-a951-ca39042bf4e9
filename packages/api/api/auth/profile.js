// BahinLink Auth Profile API
// ⚠️ CRITICAL: Real Clerk authentication and database integration ONLY

const { requireAuth } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { getCachedUserSession, cacheUserSession } = require('../../lib/redis');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getUserProfile(req, res);
    } else if (req.method === 'PUT') {
      return await updateUserProfile(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Profile API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get user profile with real data
 */
async function getUserProfile(req, res) {
  try {
    const userId = req.user.id;
    
    // Check cache first
    let cachedProfile = await getCachedUserSession(userId);
    if (cachedProfile) {
      return res.json({
        success: true,
        data: cachedProfile,
        timestamp: new Date().toISOString()
      });
    }

    // Get fresh data from real database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        agent: {
          include: {
            shifts: {
              where: {
                shiftDate: {
                  gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
              },
              include: {
                site: {
                  include: {
                    client: true
                  }
                }
              },
              orderBy: {
                startTime: 'asc'
              },
              take: 5
            },
            timeEntries: {
              where: {
                clockInTime: {
                  gte: new Date(new Date().setDate(new Date().getDate() - 7))
                }
              },
              orderBy: {
                clockInTime: 'desc'
              },
              take: 10
            }
          }
        },
        client: {
          include: {
            sites: {
              where: { isActive: true },
              take: 10
            }
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'User not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare profile data
    const profileData = {
      id: user.id,
      clerkId: user.clerkId,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    // Add role-specific data
    if (user.agent) {
      profileData.agent = {
        id: user.agent.id,
        employeeId: user.agent.employeeId,
        photoUrl: user.agent.photoUrl,
        certifications: user.agent.certifications,
        skills: user.agent.skills,
        availability: user.agent.availability,
        performanceStats: user.agent.performanceStats,
        emergencyContactName: user.agent.emergencyContactName,
        emergencyContactPhone: user.agent.emergencyContactPhone,
        hireDate: user.agent.hireDate,
        hourlyRate: user.agent.hourlyRate,
        isAvailable: user.agent.isAvailable,
        currentLatitude: user.agent.currentLatitude,
        currentLongitude: user.agent.currentLongitude,
        lastLocationUpdate: user.agent.lastLocationUpdate,
        upcomingShifts: user.agent.shifts,
        recentTimeEntries: user.agent.timeEntries
      };
    }

    if (user.client) {
      profileData.client = {
        id: user.client.id,
        companyName: user.client.companyName,
        contactPerson: user.client.contactPerson,
        billingAddress: user.client.billingAddress,
        serviceLevel: user.client.serviceLevel,
        contractStartDate: user.client.contractStartDate,
        contractEndDate: user.client.contractEndDate,
        billingCycle: user.client.billingCycle,
        isActive: user.client.isActive,
        sites: user.client.sites
      };
    }

    // Cache the profile data
    await cacheUserSession(userId, profileData);

    return res.json({
      success: true,
      data: profileData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get profile error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve profile'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update user profile with real data validation
 */
async function updateUserProfile(req, res) {
  try {
    const userId = req.user.id;
    const { firstName, lastName, phone } = req.body;

    // Validate input
    if (!firstName || !lastName) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'First name and last name are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate phone number if provided
    if (phone && !/^\+?[\d\s\-\(\)]{10,}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid phone number format'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update user in real database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        phone: phone ? phone.trim() : null,
        updatedAt: new Date()
      },
      include: {
        agent: true,
        client: true
      }
    });

    // Clear cached session
    const { invalidateUserSession } = require('../../lib/redis');
    await invalidateUserSession(userId);

    return res.json({
      success: true,
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        phone: updatedUser.phone,
        isActive: updatedUser.isActive,
        updatedAt: updatedUser.updatedAt
      },
      message: 'Profile updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update profile'
      },
      timestamp: new Date().toISOString()
    });
  }
}
