// BahinLink Auth Refresh Token API
// ⚠️ CRITICAL: Real Clerk token refresh integration ONLY

const { clerkClient } = require('@clerk/express');
const { prisma } = require('../../lib/prisma');
const { cacheUserSession } = require('../../lib/redis');
const { logAuditEvent } = require('../../lib/auth');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { refreshToken, sessionToken } = req.body;

    if (!refreshToken && !sessionToken) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Refresh token or session token is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    let newSessionToken;
    let session;

    // If we have a session token, verify it's still valid
    if (sessionToken) {
      try {
        session = await clerkClient.verifyToken(sessionToken);
        if (session) {
          // Token is still valid, return it as is
          newSessionToken = sessionToken;
        }
      } catch (error) {
        // Token is invalid, we'll need to use refresh token
        console.log('Session token invalid, attempting refresh');
      }
    }

    // If we don't have a valid session token, try to refresh
    if (!newSessionToken && refreshToken) {
      try {
        // Note: Clerk's refresh mechanism depends on the client SDK
        // For server-side refresh, we need to work with Clerk's session management
        const refreshResult = await clerkClient.sessions.refreshSession(refreshToken);
        
        if (refreshResult && refreshResult.token) {
          newSessionToken = refreshResult.token;
          session = await clerkClient.verifyToken(newSessionToken);
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return res.status(401).json({
          success: false,
          error: {
            code: 'REFRESH_FAILED',
            message: 'Token refresh failed'
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    if (!newSessionToken || !session) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_INVALID',
          message: 'Unable to refresh authentication'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { clerkId: session.sub },
      include: {
        agent: true,
        client: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_INVALID',
          message: 'User not found or inactive'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update session cache
    const sessionData = {
      id: user.id,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      isActive: user.isActive,
      agent: user.agent,
      client: user.client,
      refreshedAt: new Date()
    };

    await cacheUserSession(user.id, sessionData);

    // Log audit event
    await logAuditEvent({
      userId: user.id,
      action: 'TOKEN_REFRESH',
      apiEndpoint: '/api/auth/refresh',
      httpMethod: 'POST',
      responseStatus: 200,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.json({
      success: true,
      data: {
        accessToken: newSessionToken,
        expiresAt: new Date(session.exp * 1000),
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      },
      message: 'Token refreshed successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Refresh API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};
