// BahinLink Sites Management API
// ⚠️ CRITICAL: Real site management with GPS coordinates ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getSites(req, res);
    } else if (req.method === 'POST') {
      return await createSite(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Sites API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get sites with filtering and real-time data
 */
async function getSites(req, res) {
  try {
    const {
      page = 1,
      limit = 50,
      clientId,
      isActive = 'true',
      search,
      sortBy = 'name',
      sortOrder = 'asc',
      includeStats = 'true'
    } = req.query;

    const currentUser = req.user;
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    // Build where clause based on user role
    const where = {};
    
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }
    
    // Role-based filtering
    if (currentUser.role === 'CLIENT') {
      // Clients can only see their own sites
      where.client = {
        userId: currentUser.id
      };
    } else if (clientId) {
      where.clientId = clientId;
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { client: {
          companyName: { contains: search, mode: 'insensitive' }
        }}
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get sites with related data
    const [sites, totalCount] = await Promise.all([
      prisma.site.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        },
        include: {
          client: {
            select: {
              id: true,
              companyName: true,
              contactPerson: true,
              serviceLevel: true
            }
          },
          shifts: includeStats === 'true' ? {
            where: {
              shiftDate: {
                gte: startOfDay,
                lte: endOfDay
              }
            },
            include: {
              agent: {
                include: {
                  user: {
                    select: {
                      firstName: true,
                      lastName: true
                    }
                  }
                }
              },
              timeEntries: {
                where: {
                  clockInTime: {
                    not: null
                  },
                  clockOutTime: null
                }
              }
            }
          } : false,
          reports: includeStats === 'true' ? {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
              }
            },
            select: {
              id: true,
              type: true,
              status: true,
              createdAt: true
            }
          } : false,
          geofenceViolations: includeStats === 'true' ? {
            where: {
              createdAt: {
                gte: startOfDay,
                lte: endOfDay
              },
              isResolved: false
            }
          } : false
        }
      }),
      prisma.site.count({ where })
    ]);

    // Process sites data with real-time statistics
    const processedSites = sites.map(site => {
      let siteStats = {};
      
      if (includeStats === 'true') {
        // Calculate active agents
        const activeShifts = site.shifts?.filter(shift => 
          shift.status === 'IN_PROGRESS' && shift.timeEntries.length > 0
        ) || [];
        
        const activeAgents = activeShifts.length;
        
        // Security status
        let securityStatus = 'protected';
        if (activeAgents === 0) {
          securityStatus = 'unprotected';
        } else if (site.geofenceViolations?.length > 0) {
          securityStatus = 'alert';
        }
        
        // Recent activity
        const recentReports = site.reports?.length || 0;
        const pendingReports = site.reports?.filter(r => r.status === 'SUBMITTED').length || 0;
        
        siteStats = {
          activeAgents,
          securityStatus,
          recentReports,
          pendingReports,
          geofenceViolations: site.geofenceViolations?.length || 0,
          currentShifts: activeShifts.map(shift => ({
            id: shift.id,
            agentName: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
            startTime: shift.startTime,
            endTime: shift.endTime,
            clockedIn: shift.timeEntries.length > 0
          }))
        };
      }

      return {
        id: site.id,
        name: site.name,
        address: site.address,
        latitude: site.latitude,
        longitude: site.longitude,
        geofenceRadius: site.geofenceRadius,
        siteType: site.siteType,
        specialInstructions: site.specialInstructions,
        accessCodes: site.accessCodes,
        emergencyContacts: site.emergencyContacts,
        qrCode: site.qrCode,
        isActive: site.isActive,
        
        // Client information
        client: site.client,
        
        // Real-time statistics
        ...(includeStats === 'true' && { stats: siteStats }),
        
        // Timestamps
        createdAt: site.createdAt,
        updatedAt: site.updatedAt
      };
    });

    return res.json({
      success: true,
      data: processedSites,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary: includeStats === 'true' ? {
        totalSites: totalCount,
        protectedSites: processedSites.filter(s => s.stats?.securityStatus === 'protected').length,
        unprotectedSites: processedSites.filter(s => s.stats?.securityStatus === 'unprotected').length,
        sitesWithAlerts: processedSites.filter(s => s.stats?.securityStatus === 'alert').length,
        totalActiveAgents: processedSites.reduce((sum, s) => sum + (s.stats?.activeAgents || 0), 0)
      } : null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get sites error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve sites'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Create new site
 */
async function createSite(req, res) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      clientId,
      name,
      address,
      latitude,
      longitude,
      geofenceRadius = 100,
      siteType = 'COMMERCIAL',
      specialInstructions,
      accessCodes = [],
      emergencyContacts = []
    } = req.body;

    // Validate required fields
    if (!clientId || !name || !address || !latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields: clientId, name, address, latitude, longitude'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate GPS coordinates
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid GPS coordinates'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if client exists
    const client = await prisma.client.findUnique({
      where: { id: clientId }
    });

    if (!client) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CLIENT_NOT_FOUND',
          message: 'Client not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Generate QR code for site
    const qrCodeData = `BAHINLINK_SITE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create site
    const newSite = await prisma.site.create({
      data: {
        clientId,
        name,
        address,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        geofenceRadius: parseInt(geofenceRadius),
        siteType,
        specialInstructions,
        accessCodes,
        emergencyContacts,
        qrCode: qrCodeData,
        isActive: true
      },
      include: {
        client: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true
          }
        }
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'CREATE_SITE',
      tableName: 'sites',
      recordId: newSite.id,
      newValues: newSite,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.status(201).json({
      success: true,
      data: newSite,
      message: 'Site created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Create site error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to create site'
      },
      timestamp: new Date().toISOString()
    });
  }
}
