// BahinLink Site Details API
// ⚠️ CRITICAL: Real site management with GPS and geofencing ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { id } = req.query;

    if (req.method === 'GET') {
      return await getSiteById(req, res, id);
    } else if (req.method === 'PUT') {
      return await updateSite(req, res, id);
    } else if (req.method === 'DELETE') {
      return await deleteSite(req, res, id);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Site details API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get site by ID with comprehensive data
 */
async function getSiteById(req, res, siteId) {
  try {
    const currentUser = req.user;
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));

    // Build where clause based on user role
    const where = { id: siteId };
    
    if (currentUser.role === 'CLIENT') {
      where.client = {
        userId: currentUser.id
      };
    }

    const site = await prisma.site.findUnique({
      where,
      include: {
        client: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            serviceLevel: true,
            user: {
              select: {
                email: true,
                phone: true
              }
            }
          }
        },
        shifts: {
          where: {
            shiftDate: {
              gte: startOfWeek,
              lte: endOfWeek
            }
          },
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    phone: true
                  }
                }
              }
            },
            timeEntries: {
              orderBy: {
                clockInTime: 'desc'
              }
            }
          },
          orderBy: {
            shiftDate: 'asc'
          }
        },
        reports: {
          where: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          },
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 20
        },
        geofenceViolations: {
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          },
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!site) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Calculate real-time statistics
    const currentShifts = site.shifts.filter(shift => {
      const today = new Date().toDateString();
      return shift.shiftDate.toDateString() === today && shift.status === 'IN_PROGRESS';
    });

    const activeAgents = currentShifts.filter(shift => 
      shift.timeEntries.some(entry => entry.clockInTime && !entry.clockOutTime)
    ).length;

    // Security status calculation
    let securityStatus = 'protected';
    if (activeAgents === 0) {
      securityStatus = 'unprotected';
    } else if (site.geofenceViolations.some(v => !v.isResolved)) {
      securityStatus = 'alert';
    }

    // Performance metrics
    const completedShifts = site.shifts.filter(s => s.status === 'COMPLETED');
    const onTimeShifts = completedShifts.filter(s => 
      s.timeEntries.some(e => e.clockInTime <= s.startTime)
    );
    const onTimePercentage = completedShifts.length > 0 ? 
      (onTimeShifts.length / completedShifts.length) * 100 : 100;

    const responseData = {
      id: site.id,
      name: site.name,
      address: site.address,
      latitude: site.latitude,
      longitude: site.longitude,
      geofenceRadius: site.geofenceRadius,
      siteType: site.siteType,
      specialInstructions: site.specialInstructions,
      accessCodes: site.accessCodes,
      emergencyContacts: site.emergencyContacts,
      qrCode: site.qrCode,
      isActive: site.isActive,
      
      // Client information
      client: site.client,
      
      // Real-time status
      status: {
        securityStatus,
        activeAgents,
        currentShifts: currentShifts.length,
        lastActivity: site.reports[0]?.createdAt || null,
        unresolvedViolations: site.geofenceViolations.filter(v => !v.isResolved).length
      },
      
      // Performance metrics
      performance: {
        onTimePercentage: Math.round(onTimePercentage),
        totalShifts: site.shifts.length,
        completedShifts: completedShifts.length,
        totalReports: site.reports.length,
        averageResponseTime: null // Could be calculated from incident reports
      },
      
      // Recent activity
      recentShifts: site.shifts.slice(0, 10).map(shift => ({
        id: shift.id,
        agentName: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
        shiftDate: shift.shiftDate,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        clockedIn: shift.timeEntries.some(e => e.clockInTime && !e.clockOutTime),
        hoursWorked: shift.timeEntries.reduce((total, entry) => {
          if (entry.clockInTime && entry.clockOutTime) {
            return total + (new Date(entry.clockOutTime) - new Date(entry.clockInTime)) / (1000 * 60 * 60);
          }
          return total;
        }, 0)
      })),
      
      recentReports: site.reports.map(report => ({
        id: report.id,
        type: report.type,
        title: report.title,
        status: report.status,
        priority: report.priority,
        agentName: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
        createdAt: report.createdAt,
        summary: report.description?.substring(0, 100) + (report.description?.length > 100 ? '...' : '')
      })),
      
      geofenceViolations: site.geofenceViolations.map(violation => ({
        id: violation.id,
        agentName: `${violation.agent.user.firstName} ${violation.agent.user.lastName}`,
        violationType: violation.violationType,
        distance: violation.distance,
        isResolved: violation.isResolved,
        createdAt: violation.createdAt,
        resolvedAt: violation.resolvedAt
      })),
      
      // Timestamps
      createdAt: site.createdAt,
      updatedAt: site.updatedAt
    };

    return res.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get site by ID error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve site'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update site information
 */
async function updateSite(req, res, siteId) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      name,
      address,
      latitude,
      longitude,
      geofenceRadius,
      siteType,
      specialInstructions,
      accessCodes,
      emergencyContacts,
      isActive
    } = req.body;

    // Get existing site
    const existingSite = await prisma.site.findUnique({
      where: { id: siteId },
      include: {
        client: true
      }
    });

    if (!existingSite) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare update data
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (address !== undefined) updateData.address = address;
    if (latitude !== undefined) {
      if (latitude < -90 || latitude > 90) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid latitude value'
          },
          timestamp: new Date().toISOString()
        });
      }
      updateData.latitude = parseFloat(latitude);
    }
    if (longitude !== undefined) {
      if (longitude < -180 || longitude > 180) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid longitude value'
          },
          timestamp: new Date().toISOString()
        });
      }
      updateData.longitude = parseFloat(longitude);
    }
    if (geofenceRadius !== undefined) updateData.geofenceRadius = parseInt(geofenceRadius);
    if (siteType !== undefined) updateData.siteType = siteType;
    if (specialInstructions !== undefined) updateData.specialInstructions = specialInstructions;
    if (accessCodes !== undefined) updateData.accessCodes = accessCodes;
    if (emergencyContacts !== undefined) updateData.emergencyContacts = emergencyContacts;
    if (isActive !== undefined) updateData.isActive = isActive;

    updateData.updatedAt = new Date();

    // Update site
    const updatedSite = await prisma.site.update({
      where: { id: siteId },
      data: updateData,
      include: {
        client: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true
          }
        }
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'UPDATE_SITE',
      tableName: 'sites',
      recordId: siteId,
      oldValues: existingSite,
      newValues: updateData,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: updatedSite,
      message: 'Site updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update site error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update site'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Soft delete site (deactivate)
 */
async function deleteSite(req, res, siteId) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const existingSite = await prisma.site.findUnique({
      where: { id: siteId },
      include: {
        shifts: {
          where: {
            status: {
              in: ['SCHEDULED', 'IN_PROGRESS']
            }
          }
        }
      }
    });

    if (!existingSite) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check for active shifts
    if (existingSite.shifts.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'SITE_HAS_ACTIVE_SHIFTS',
          message: 'Cannot delete site with active or scheduled shifts'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Soft delete (deactivate) site
    const deactivatedSite = await prisma.site.update({
      where: { id: siteId },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'DELETE_SITE',
      tableName: 'sites',
      recordId: siteId,
      oldValues: existingSite,
      newValues: { isActive: false },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      message: 'Site deactivated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete site error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to deactivate site'
      },
      timestamp: new Date().toISOString()
    });
  }
}
