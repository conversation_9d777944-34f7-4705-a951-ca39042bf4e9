// BahinLink Site QR Code Generation API
// ⚠️ CRITICAL: Real QR code generation for site check-ins ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');
const QRCode = require('qrcode');
const crypto = require('crypto');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { siteId, regenerate = false, expiryHours = 24 } = req.body;

    if (!siteId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Site ID is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get site
    const site = await prisma.site.findUnique({
      where: { id: siteId },
      include: {
        client: {
          select: {
            id: true,
            companyName: true
          }
        }
      }
    });

    if (!site) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (!site.isActive) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'SITE_INACTIVE',
          message: 'Cannot generate QR code for inactive site'
        },
        timestamp: new Date().toISOString()
      });
    }

    let qrCodeData = site.qrCode;
    let qrCodeExpiry = site.qrCodeExpiry;

    // Generate new QR code if requested or if current one is expired/missing
    if (regenerate || !qrCodeData || (qrCodeExpiry && new Date() > qrCodeExpiry)) {
      // Generate secure QR code data
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(16).toString('hex');
      const siteHash = crypto.createHash('sha256').update(siteId).digest('hex').substring(0, 8);
      
      qrCodeData = `BAHINLINK_CHECKIN_${siteHash}_${timestamp}_${randomString}`;
      qrCodeExpiry = new Date(Date.now() + (expiryHours * 60 * 60 * 1000));

      // Update site with new QR code
      await prisma.site.update({
        where: { id: siteId },
        data: {
          qrCode: qrCodeData,
          qrCodeExpiry: qrCodeExpiry,
          updatedAt: new Date()
        }
      });
    }

    // Generate QR code image
    const qrCodeOptions = {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: 256
    };

    // Create QR code payload with site information
    const qrPayload = {
      type: 'SITE_CHECKIN',
      siteId: site.id,
      siteName: site.name,
      clientName: site.client.companyName,
      code: qrCodeData,
      latitude: site.latitude,
      longitude: site.longitude,
      geofenceRadius: site.geofenceRadius,
      expiresAt: qrCodeExpiry?.toISOString(),
      generatedAt: new Date().toISOString()
    };

    const qrCodeImage = await QRCode.toDataURL(JSON.stringify(qrPayload), qrCodeOptions);

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: regenerate ? 'REGENERATE_QR_CODE' : 'GENERATE_QR_CODE',
      tableName: 'sites',
      recordId: siteId,
      newValues: { qrCode: qrCodeData, qrCodeExpiry },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: {
        siteId: site.id,
        siteName: site.name,
        qrCode: qrCodeData,
        qrCodeImage: qrCodeImage,
        expiresAt: qrCodeExpiry,
        payload: qrPayload,
        instructions: {
          usage: 'Agents scan this QR code to check in at the site',
          validation: 'QR code validates location within geofence radius',
          expiry: `QR code expires in ${expiryHours} hours`,
          security: 'Each QR code is unique and time-limited for security'
        }
      },
      message: regenerate ? 'QR code regenerated successfully' : 'QR code generated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('QR code generation error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to generate QR code'
      },
      timestamp: new Date().toISOString()
    });
  }
};
