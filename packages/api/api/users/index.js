// BahinLink Users Management API
// ⚠️ CRITICAL: Real user management with Clerk integration ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getUsers(req, res);
    } else if (req.method === 'POST') {
      return await createUser(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Users API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get users with filtering and pagination
 */
async function getUsers(req, res) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      page = 1,
      limit = 50,
      role,
      isActive,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build where clause
    const where = {};
    
    if (role) {
      where.role = role;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get users with related data
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        },
        include: {
          agent: {
            select: {
              id: true,
              employeeId: true,
              isAvailable: true,
              currentLatitude: true,
              currentLongitude: true,
              lastLocationUpdate: true
            }
          },
          client: {
            select: {
              id: true,
              companyName: true,
              serviceLevel: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    // Format user data
    const formattedUsers = users.map(user => ({
      id: user.id,
      clerkId: user.clerkId,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      
      // Agent-specific data
      agent: user.agent ? {
        id: user.agent.id,
        employeeId: user.agent.employeeId,
        isAvailable: user.agent.isAvailable,
        currentLocation: user.agent.currentLatitude && user.agent.currentLongitude ? {
          latitude: user.agent.currentLatitude,
          longitude: user.agent.currentLongitude,
          lastUpdate: user.agent.lastLocationUpdate
        } : null
      } : null,
      
      // Client-specific data
      client: user.client ? {
        id: user.client.id,
        companyName: user.client.companyName,
        serviceLevel: user.client.serviceLevel
      } : null
    }));

    return res.json({
      success: true,
      data: formattedUsers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get users error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve users'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Create new user (admin only)
 */
async function createUser(req, res) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      email,
      firstName,
      lastName,
      phone,
      role,
      agentData,
      clientData
    } = req.body;

    // Validate required fields
    if (!email || !firstName || !lastName || !role) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields: email, firstName, lastName, role'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate role
    const validRoles = ['ADMIN', 'SUPERVISOR', 'AGENT', 'CLIENT'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid role. Must be one of: ' + validRoles.join(', ')
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: {
          code: 'USER_EXISTS',
          message: 'User with this email already exists'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Create user with related data
    const userData = {
      email,
      firstName,
      lastName,
      phone,
      role,
      isActive: true
    };

    // Add role-specific data
    if (role === 'AGENT' && agentData) {
      userData.agent = {
        create: {
          employeeId: agentData.employeeId,
          certifications: agentData.certifications || [],
          skills: agentData.skills || [],
          isAvailable: true,
          performanceStats: {
            totalShifts: 0,
            onTimePercentage: 100,
            clientSatisfactionScore: 5.0,
            reportQualityScore: 5.0
          }
        }
      };
    }

    if (role === 'CLIENT' && clientData) {
      userData.client = {
        create: {
          companyName: clientData.companyName,
          contactPerson: clientData.contactPerson,
          billingAddress: clientData.billingAddress,
          serviceLevel: clientData.serviceLevel || 'STANDARD'
        }
      };
    }

    const newUser = await prisma.user.create({
      data: userData,
      include: {
        agent: true,
        client: true
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'CREATE_USER',
      tableName: 'users',
      recordId: newUser.id,
      newValues: userData,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    // Remove sensitive data from response
    const { ...userResponse } = newUser;

    return res.status(201).json({
      success: true,
      data: userResponse,
      message: 'User created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Create user error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to create user'
      },
      timestamp: new Date().toISOString()
    });
  }
}
