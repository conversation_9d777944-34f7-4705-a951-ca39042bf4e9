// BahinLink User Details API
// ⚠️ CRITICAL: Real user management with role-based access ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { id } = req.query;

    if (req.method === 'GET') {
      return await getUserById(req, res, id);
    } else if (req.method === 'PUT') {
      return await updateUser(req, res, id);
    } else if (req.method === 'DELETE') {
      return await deleteUser(req, res, id);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('User details API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get user by ID with role-based access control
 */
async function getUserById(req, res, userId) {
  try {
    const currentUser = req.user;

    // Check permissions - users can view their own profile, admins/supervisors can view all
    if (currentUser.id !== userId && !['ADMIN', 'SUPERVISOR'].includes(currentUser.role)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to view this user'
        },
        timestamp: new Date().toISOString()
      });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        agent: {
          include: {
            shifts: {
              where: {
                shiftDate: {
                  gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
              },
              include: {
                site: {
                  select: {
                    id: true,
                    name: true,
                    address: true
                  }
                }
              },
              orderBy: {
                startTime: 'asc'
              },
              take: 10
            }
          }
        },
        client: {
          include: {
            sites: {
              where: {
                isActive: true
              },
              select: {
                id: true,
                name: true,
                address: true,
                latitude: true,
                longitude: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Format response based on user role
    const userResponse = {
      id: user.id,
      clerkId: user.clerkId,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    // Add role-specific data
    if (user.agent) {
      userResponse.agent = {
        id: user.agent.id,
        employeeId: user.agent.employeeId,
        certifications: user.agent.certifications,
        skills: user.agent.skills,
        isAvailable: user.agent.isAvailable,
        performanceStats: user.agent.performanceStats,
        currentLocation: user.agent.currentLatitude && user.agent.currentLongitude ? {
          latitude: user.agent.currentLatitude,
          longitude: user.agent.currentLongitude,
          lastUpdate: user.agent.lastLocationUpdate
        } : null,
        upcomingShifts: user.agent.shifts.map(shift => ({
          id: shift.id,
          site: shift.site,
          shiftDate: shift.shiftDate,
          startTime: shift.startTime,
          endTime: shift.endTime,
          status: shift.status
        }))
      };
    }

    if (user.client) {
      userResponse.client = {
        id: user.client.id,
        companyName: user.client.companyName,
        contactPerson: user.client.contactPerson,
        billingAddress: user.client.billingAddress,
        serviceLevel: user.client.serviceLevel,
        sites: user.client.sites
      };
    }

    return res.json({
      success: true,
      data: userResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve user'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update user profile
 */
async function updateUser(req, res, userId) {
  try {
    const currentUser = req.user;

    // Check permissions
    if (currentUser.id !== userId && !['ADMIN', 'SUPERVISOR'].includes(currentUser.role)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to update this user'
        },
        timestamp: new Date().toISOString()
      });
    }

    const {
      firstName,
      lastName,
      phone,
      role,
      isActive,
      agentData,
      clientData
    } = req.body;

    // Get existing user
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        agent: true,
        client: true
      }
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare update data
    const updateData = {};
    
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (phone !== undefined) updateData.phone = phone;
    
    // Only admins can change role and active status
    if (['ADMIN'].includes(currentUser.role)) {
      if (role !== undefined) updateData.role = role;
      if (isActive !== undefined) updateData.isActive = isActive;
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        agent: true,
        client: true
      }
    });

    // Update role-specific data
    if (agentData && existingUser.agent) {
      await prisma.agent.update({
        where: { id: existingUser.agent.id },
        data: {
          certifications: agentData.certifications,
          skills: agentData.skills,
          isAvailable: agentData.isAvailable
        }
      });
    }

    if (clientData && existingUser.client) {
      await prisma.client.update({
        where: { id: existingUser.client.id },
        data: {
          companyName: clientData.companyName,
          contactPerson: clientData.contactPerson,
          billingAddress: clientData.billingAddress,
          serviceLevel: clientData.serviceLevel
        }
      });
    }

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'UPDATE_USER',
      tableName: 'users',
      recordId: userId,
      oldValues: existingUser,
      newValues: updateData,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update user error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update user'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Soft delete user (deactivate)
 */
async function deleteUser(req, res, userId) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Soft delete (deactivate) user
    const deactivatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'DELETE_USER',
      tableName: 'users',
      recordId: userId,
      oldValues: existingUser,
      newValues: { isActive: false },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      message: 'User deactivated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to deactivate user'
      },
      timestamp: new Date().toISOString()
    });
  }
}
