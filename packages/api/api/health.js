// BahinLink Health Check API
// ⚠️ CRITICAL: Real production health monitoring ONLY

const { prisma } = require('../lib/prisma');
const { redis } = require('../lib/redis');

module.exports = async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {}
    };

    // Check database connection
    try {
      await prisma.$queryRaw`SELECT 1`;
      healthCheck.services.database = {
        status: 'healthy',
        type: 'PostgreSQL',
        provider: 'Neon'
      };
    } catch (error) {
      healthCheck.services.database = {
        status: 'unhealthy',
        error: error.message
      };
      healthCheck.status = 'degraded';
    }

    // Check Redis connection
    try {
      await redis.ping();
      healthCheck.services.redis = {
        status: 'healthy',
        type: 'Redis',
        provider: 'Upstash'
      };
    } catch (error) {
      healthCheck.services.redis = {
        status: 'unhealthy',
        error: error.message
      };
      healthCheck.status = 'degraded';
    }

    // Check environment variables
    const requiredEnvVars = [
      'DATABASE_URL',
      'CLERK_SECRET_KEY',
      'UPSTASH_REDIS_REST_URL',
      'BLOB_READ_WRITE_TOKEN'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    healthCheck.services.environment = {
      status: missingEnvVars.length === 0 ? 'healthy' : 'unhealthy',
      missingVariables: missingEnvVars
    };

    if (missingEnvVars.length > 0) {
      healthCheck.status = 'degraded';
    }

    // System metrics
    healthCheck.system = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      nodeVersion: process.version,
      platform: process.platform
    };

    // API endpoints status
    healthCheck.endpoints = {
      auth: '/api/auth/profile',
      agents: '/api/agents/location',
      timeTracking: '/api/time/clock-in',
      analytics: '/api/analytics/dashboard'
    };

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;

    return res.status(statusCode).json({
      success: healthCheck.status === 'healthy',
      data: healthCheck,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Health check error:', error);
    return res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Health check failed'
      },
      timestamp: new Date().toISOString()
    });
  }
};
