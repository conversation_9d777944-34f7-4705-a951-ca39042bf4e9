// BahinLink Time Clock-Out API
// ⚠️ CRITICAL: Real time tracking with GPS verification and database integration ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { calculateDistance } = require('@bahinlink/shared');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication and agent role
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    await new Promise((resolve, reject) => {
      requireRole(['AGENT'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await clockOut(req, res);
  } catch (error) {
    console.error('Clock-out API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Clock out agent with GPS verification
 */
async function clockOut(req, res) {
  try {
    const {
      shiftId,
      timeEntryId,
      latitude,
      longitude,
      accuracy,
      notes,
      forceClockOut = false
    } = req.body;

    const currentUser = req.user;
    const clockOutTime = new Date();

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'GPS coordinates are required for clock-out'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate GPS accuracy
    if (accuracy && accuracy > 50) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'GPS_ACCURACY_LOW',
          message: 'GPS accuracy is too low. Please wait for better signal.'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get agent
    const agent = await prisma.agent.findUnique({
      where: { userId: currentUser.id },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent profile not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    let timeEntry;
    let shift;

    // Find active time entry
    if (timeEntryId) {
      timeEntry = await prisma.timeEntry.findUnique({
        where: { id: timeEntryId },
        include: {
          shift: {
            include: {
              site: true
            }
          }
        }
      });
    } else if (shiftId) {
      // Find active time entry for the shift
      timeEntry = await prisma.timeEntry.findFirst({
        where: {
          shiftId,
          clockOutTime: null
        },
        include: {
          shift: {
            include: {
              site: true
            }
          }
        },
        orderBy: {
          clockInTime: 'desc'
        }
      });
    } else {
      // Find any active time entry for this agent
      timeEntry = await prisma.timeEntry.findFirst({
        where: {
          shift: {
            agentId: agent.id
          },
          clockOutTime: null
        },
        include: {
          shift: {
            include: {
              site: true
            }
          }
        },
        orderBy: {
          clockInTime: 'desc'
        }
      });
    }

    if (!timeEntry) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NO_ACTIVE_TIME_ENTRY',
          message: 'No active time entry found to clock out'
        },
        timestamp: new Date().toISOString()
      });
    }

    shift = timeEntry.shift;

    // Verify agent owns this time entry
    if (shift.agentId !== agent.id) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Cannot clock out for another agent'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if already clocked out
    if (timeEntry.clockOutTime) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'ALREADY_CLOCKED_OUT',
          message: 'Already clocked out for this time entry'
        },
        timestamp: new Date().toISOString()
      });
    }

    // GPS geofence validation
    const site = shift.site;
    const distance = calculateDistance(
      parseFloat(latitude),
      parseFloat(longitude),
      site.latitude,
      site.longitude
    );

    let geofenceViolation = null;
    if (distance > site.geofenceRadius && !forceClockOut) {
      // Create geofence violation record
      geofenceViolation = await prisma.geofenceViolation.create({
        data: {
          agentId: agent.id,
          siteId: site.id,
          shiftId: shift.id,
          violationType: 'CLOCK_OUT_OUTSIDE_GEOFENCE',
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          distance: Math.round(distance),
          isResolved: false
        }
      });

      return res.status(400).json({
        success: false,
        error: {
          code: 'GEOFENCE_VIOLATION',
          message: `Clock-out location is ${Math.round(distance)}m from site (allowed: ${site.geofenceRadius}m)`,
          details: {
            distance: Math.round(distance),
            allowedRadius: site.geofenceRadius,
            violationId: geofenceViolation.id,
            canForceClockOut: true
          }
        },
        timestamp: new Date().toISOString()
      });
    }

    // Calculate work duration
    const workDuration = (clockOutTime - new Date(timeEntry.clockInTime)) / (1000 * 60 * 60);

    // Update time entry with clock-out information
    const updatedTimeEntry = await prisma.timeEntry.update({
      where: { id: timeEntry.id },
      data: {
        clockOutTime,
        clockOutLatitude: parseFloat(latitude),
        clockOutLongitude: parseFloat(longitude),
        clockOutAccuracy: accuracy ? parseFloat(accuracy) : null,
        notes: notes || null,
        hoursWorked: Math.round(workDuration * 100) / 100
      }
    });

    // Update agent's current location
    await prisma.agent.update({
      where: { id: agent.id },
      data: {
        currentLatitude: parseFloat(latitude),
        currentLongitude: parseFloat(longitude),
        lastLocationUpdate: clockOutTime
      }
    });

    // Check if this was the last active time entry for the shift
    const remainingActiveEntries = await prisma.timeEntry.count({
      where: {
        shiftId: shift.id,
        clockOutTime: null
      }
    });

    // Update shift status if no more active time entries
    if (remainingActiveEntries === 0) {
      await prisma.shift.update({
        where: { id: shift.id },
        data: {
          actualEndTime: clockOutTime,
          status: 'COMPLETED'
        }
      });
    }

    // Create audit log
    await createAuditLog({
      userId: currentUser.id,
      action: 'CLOCK_OUT',
      tableName: 'time_entries',
      recordId: timeEntry.id,
      newValues: {
        clockOutTime,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        hoursWorked: Math.round(workDuration * 100) / 100
      },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: {
        timeEntry: {
          id: updatedTimeEntry.id,
          clockInTime: updatedTimeEntry.clockInTime,
          clockOutTime: updatedTimeEntry.clockOutTime,
          hoursWorked: updatedTimeEntry.hoursWorked,
          notes: updatedTimeEntry.notes
        },
        shift: {
          id: shift.id,
          status: remainingActiveEntries === 0 ? 'COMPLETED' : shift.status,
          site: {
            name: site.name,
            address: site.address
          }
        },
        location: {
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          accuracy: accuracy ? parseFloat(accuracy) : null,
          distanceFromSite: Math.round(distance),
          withinGeofence: distance <= site.geofenceRadius
        },
        summary: {
          workDuration: Math.round(workDuration * 100) / 100,
          clockOutTime: clockOutTime.toISOString(),
          agentName: `${agent.user.firstName} ${agent.user.lastName}`
        }
      },
      message: 'Clocked out successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Clock-out error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to clock out'
      },
      timestamp: new Date().toISOString()
    });
  }
}
