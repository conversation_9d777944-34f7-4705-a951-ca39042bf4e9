// BahinLink Time Clock-In API
// ⚠️ CRITICAL: Real time tracking with GPS verification and database integration ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { calculateDistance, parseQRCodeData } = require('@bahinlink/shared');
const { cacheGeofenceCheck } = require('../../lib/redis');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication and agent role
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    await new Promise((resolve, reject) => {
      requireRole(['AGENT'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await clockIn(req, res);
  } catch (error) {
    console.error('Clock-in API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Clock in agent with real GPS verification
 */
async function clockIn(req, res) {
  try {
    const user = req.user;
    const { shiftId, location, method, qrCode } = req.body;

    // Validate required fields
    if (!shiftId || !location || !location.latitude || !location.longitude) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Shift ID and location (latitude, longitude) are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate GPS coordinates
    if (location.latitude < -90 || location.latitude > 90 || 
        location.longitude < -180 || location.longitude > 180) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid GPS coordinates'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate clock-in method
    const validMethods = ['GPS', 'QR_CODE', 'MANUAL', 'NFC'];
    if (method && !validMethods.includes(method)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid clock-in method'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get shift from real database
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        site: {
          include: {
            client: true
          }
        },
        agent: {
          include: {
            user: true
          }
        },
        timeEntries: true
      }
    });

    if (!shift) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Shift not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify agent is assigned to this shift
    if (!shift.agentId || shift.agent.userId !== user.id) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You are not assigned to this shift'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if shift is in correct status
    if (shift.status !== 'SCHEDULED') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SHIFT_STATUS',
          message: `Cannot clock in to shift with status: ${shift.status}`
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if already clocked in
    const existingTimeEntry = shift.timeEntries.find(entry => 
      entry.clockInTime && !entry.clockOutTime
    );
    
    if (existingTimeEntry) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'ALREADY_CLOCKED_IN',
          message: 'Already clocked in to this shift'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify QR code if provided
    if (method === 'QR_CODE' && qrCode) {
      const qrData = parseQRCodeData(qrCode);
      if (!qrData || qrData.siteId !== shift.siteId) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_QR_CODE',
            message: 'Invalid or mismatched QR code'
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    // Calculate distance from site using real GPS coordinates
    const site = shift.site;
    const distanceFromSite = calculateDistance(
      location.latitude,
      location.longitude,
      site.latitude,
      site.longitude
    );

    const withinGeofence = distanceFromSite <= site.geofenceRadius;
    const clockInTime = new Date();

    // Allow clock-in with warning if outside geofence but within reasonable distance
    const maxAllowedDistance = site.geofenceRadius * 2; // 2x geofence radius
    if (distanceFromSite > maxAllowedDistance) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'GEOFENCE_VIOLATION',
          message: `Too far from site. Distance: ${Math.round(distanceFromSite)}m, Maximum allowed: ${maxAllowedDistance}m`
        },
        timestamp: new Date().toISOString()
      });
    }

    // Create time entry in real database
    const timeEntry = await prisma.timeEntry.create({
      data: {
        shiftId,
        agentId: shift.agentId,
        clockInTime,
        clockInLatitude: location.latitude,
        clockInLongitude: location.longitude,
        clockInMethod: method || 'GPS',
        clockInAccuracy: location.accuracy || null,
        isVerified: false
      }
    });

    // Update shift status to IN_PROGRESS
    await prisma.shift.update({
      where: { id: shiftId },
      data: {
        status: 'IN_PROGRESS',
        actualStartTime: clockInTime
      }
    });

    // Update agent location
    await prisma.agent.update({
      where: { id: shift.agentId },
      data: {
        currentLatitude: location.latitude,
        currentLongitude: location.longitude,
        lastLocationUpdate: clockInTime
      }
    });

    // Log geofence violation if outside but allowed
    if (!withinGeofence) {
      await prisma.geofenceViolation.create({
        data: {
          agentId: shift.agentId,
          siteId: shift.siteId,
          shiftId,
          violationType: 'CLOCK_IN_OUTSIDE_GEOFENCE',
          agentLatitude: location.latitude,
          agentLongitude: location.longitude,
          distanceFromSite,
          durationMinutes: 0
        }
      });
    }

    // Cache geofence check result
    await cacheGeofenceCheck(shift.agentId, shift.siteId, {
      withinGeofence,
      distance: distanceFromSite,
      geofenceRadius: site.geofenceRadius,
      timestamp: clockInTime.toISOString()
    });

    // Store real-time event for notifications
    const { storeOfflineEvent } = require('../../lib/redis');
    await storeOfflineEvent(user.id, {
      type: 'shift_started',
      data: {
        shiftId,
        agentId: shift.agentId,
        siteId: shift.siteId,
        siteName: site.name,
        clientName: site.client.companyName,
        clockInTime,
        withinGeofence,
        distanceFromSite
      }
    });

    return res.json({
      success: true,
      data: {
        timeEntryId: timeEntry.id,
        shiftId,
        clockInTime,
        location: {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy
        },
        geofenceCheck: {
          withinGeofence,
          distanceFromSite: Math.round(distanceFromSite),
          geofenceRadius: site.geofenceRadius
        },
        site: {
          id: site.id,
          name: site.name,
          address: site.address
        },
        shift: {
          id: shift.id,
          status: 'IN_PROGRESS',
          startTime: shift.startTime,
          endTime: shift.endTime
        }
      },
      message: withinGeofence ? 
        'Clocked in successfully' : 
        `Clocked in with warning: ${Math.round(distanceFromSite)}m from site`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Clock-in error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to clock in'
      },
      timestamp: new Date().toISOString()
    });
  }
}
