// BahinLink Time Entries API
// ⚠️ CRITICAL: Real time entry management and verification ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getTimeEntries(req, res);
    } else if (req.method === 'PUT') {
      return await verifyTimeEntry(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Time entries API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get time entries with filtering
 */
async function getTimeEntries(req, res) {
  try {
    const {
      shiftId,
      agentId,
      siteId,
      startDate,
      endDate,
      status,
      page = 1,
      limit = 50,
      includeLocation = 'true'
    } = req.query;

    const currentUser = req.user;

    // Build where clause based on user role and filters
    const where = {};

    // Role-based filtering
    if (currentUser.role === 'AGENT') {
      // Agents can only see their own time entries
      where.shift = {
        agent: {
          userId: currentUser.id
        }
      };
    } else if (currentUser.role === 'CLIENT') {
      // Clients can only see time entries for their sites
      where.shift = {
        site: {
          client: {
            userId: currentUser.id
          }
        }
      };
    } else {
      // Admin/Supervisor can see all with optional filtering
      if (agentId) {
        where.shift = {
          agentId
        };
      }
      if (siteId) {
        where.shift = {
          ...where.shift,
          siteId
        };
      }
    }

    if (shiftId) {
      where.shiftId = shiftId;
    }

    // Date filtering
    if (startDate || endDate) {
      where.clockInTime = {};
      if (startDate) where.clockInTime.gte = new Date(startDate);
      if (endDate) where.clockInTime.lte = new Date(endDate);
    }

    // Status filtering
    if (status === 'active') {
      where.clockOutTime = null;
    } else if (status === 'completed') {
      where.clockOutTime = { not: null };
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get time entries with related data
    const [timeEntries, totalCount] = await Promise.all([
      prisma.timeEntry.findMany({
        where,
        skip,
        take,
        orderBy: {
          clockInTime: 'desc'
        },
        include: {
          shift: {
            include: {
              agent: {
                include: {
                  user: {
                    select: {
                      firstName: true,
                      lastName: true,
                      phone: true
                    }
                  }
                }
              },
              site: {
                include: {
                  client: {
                    select: {
                      companyName: true
                    }
                  }
                }
              }
            }
          }
        }
      }),
      prisma.timeEntry.count({ where })
    ]);

    // Process time entries data
    const processedEntries = timeEntries.map(entry => {
      const clockInTime = new Date(entry.clockInTime);
      const clockOutTime = entry.clockOutTime ? new Date(entry.clockOutTime) : null;
      
      // Calculate duration
      let duration = 0;
      let isActive = false;
      
      if (clockOutTime) {
        duration = (clockOutTime - clockInTime) / (1000 * 60 * 60);
      } else {
        duration = (new Date() - clockInTime) / (1000 * 60 * 60);
        isActive = true;
      }

      // Calculate break time
      let breakDuration = 0;
      if (entry.breakStartTime && entry.breakEndTime) {
        breakDuration = (new Date(entry.breakEndTime) - new Date(entry.breakStartTime)) / (1000 * 60 * 60);
      }

      const result = {
        id: entry.id,
        clockInTime: entry.clockInTime,
        clockOutTime: entry.clockOutTime,
        breakStartTime: entry.breakStartTime,
        breakEndTime: entry.breakEndTime,
        hoursWorked: entry.hoursWorked || Math.round(duration * 100) / 100,
        breakDuration: Math.round(breakDuration * 100) / 100,
        notes: entry.notes,
        isActive,
        isVerified: entry.isVerified,
        verifiedBy: entry.verifiedBy,
        verifiedAt: entry.verifiedAt,
        
        // Shift information
        shift: {
          id: entry.shift.id,
          shiftDate: entry.shift.shiftDate,
          startTime: entry.shift.startTime,
          endTime: entry.shift.endTime,
          status: entry.shift.status,
          
          // Agent information
          agent: {
            id: entry.shift.agent.id,
            employeeId: entry.shift.agent.employeeId,
            name: `${entry.shift.agent.user.firstName} ${entry.shift.agent.user.lastName}`,
            phone: entry.shift.agent.user.phone
          },
          
          // Site information
          site: {
            id: entry.shift.site.id,
            name: entry.shift.site.name,
            address: entry.shift.site.address,
            clientName: entry.shift.site.client.companyName
          }
        },
        
        // Timestamps
        createdAt: entry.createdAt,
        updatedAt: entry.updatedAt
      };

      // Include location data if requested
      if (includeLocation === 'true') {
        result.clockInLocation = entry.clockInLatitude && entry.clockInLongitude ? {
          latitude: entry.clockInLatitude,
          longitude: entry.clockInLongitude,
          accuracy: entry.clockInAccuracy
        } : null;

        result.clockOutLocation = entry.clockOutLatitude && entry.clockOutLongitude ? {
          latitude: entry.clockOutLatitude,
          longitude: entry.clockOutLongitude,
          accuracy: entry.clockOutAccuracy
        } : null;
      }

      return result;
    });

    // Calculate summary statistics
    const summary = {
      totalEntries: totalCount,
      activeEntries: processedEntries.filter(e => e.isActive).length,
      completedEntries: processedEntries.filter(e => !e.isActive).length,
      verifiedEntries: processedEntries.filter(e => e.isVerified).length,
      totalHours: processedEntries.reduce((sum, e) => sum + (e.hoursWorked || 0), 0),
      totalBreakHours: processedEntries.reduce((sum, e) => sum + e.breakDuration, 0),
      averageHoursPerEntry: processedEntries.length > 0 ? 
        Math.round((processedEntries.reduce((sum, e) => sum + (e.hoursWorked || 0), 0) / processedEntries.length) * 100) / 100 : 0
    };

    return res.json({
      success: true,
      data: processedEntries,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get time entries error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve time entries'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Verify time entry (admin/supervisor only)
 */
async function verifyTimeEntry(req, res) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { timeEntryId, isVerified, verificationNotes } = req.body;

    if (!timeEntryId || isVerified === undefined) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Time entry ID and verification status are required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get existing time entry
    const existingEntry = await prisma.timeEntry.findUnique({
      where: { id: timeEntryId },
      include: {
        shift: {
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            site: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!existingEntry) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'TIME_ENTRY_NOT_FOUND',
          message: 'Time entry not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Update verification status
    const updatedEntry = await prisma.timeEntry.update({
      where: { id: timeEntryId },
      data: {
        isVerified,
        verifiedBy: req.user.id,
        verifiedAt: new Date(),
        verificationNotes
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: isVerified ? 'VERIFY_TIME_ENTRY' : 'REJECT_TIME_ENTRY',
      tableName: 'time_entries',
      recordId: timeEntryId,
      oldValues: { isVerified: existingEntry.isVerified },
      newValues: { isVerified, verificationNotes },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: {
        timeEntryId,
        isVerified,
        verifiedBy: req.user.id,
        verifiedAt: updatedEntry.verifiedAt,
        verificationNotes,
        agentName: `${existingEntry.shift.agent.user.firstName} ${existingEntry.shift.agent.user.lastName}`,
        siteName: existingEntry.shift.site.name,
        shiftDate: existingEntry.shift.shiftDate
      },
      message: `Time entry ${isVerified ? 'verified' : 'rejected'} successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Verify time entry error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to verify time entry'
      },
      timestamp: new Date().toISOString()
    });
  }
}
