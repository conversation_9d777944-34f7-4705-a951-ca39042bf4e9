// BahinLink Shifts Management API
// ⚠️ CRITICAL: Real shift scheduling and management ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'GET') {
      return await getShifts(req, res);
    } else if (req.method === 'POST') {
      return await createShift(req, res);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Shifts API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get shifts with filtering and real-time data
 */
async function getShifts(req, res) {
  try {
    const {
      page = 1,
      limit = 50,
      agentId,
      siteId,
      status,
      startDate,
      endDate,
      sortBy = 'shiftDate',
      sortOrder = 'desc'
    } = req.query;

    const currentUser = req.user;

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    // Build where clause based on user role
    const where = {
      shiftDate: {
        gte: start,
        lte: end
      }
    };

    // Role-based filtering
    if (currentUser.role === 'AGENT') {
      // Agents can only see their own shifts
      where.agent = {
        userId: currentUser.id
      };
    } else if (currentUser.role === 'CLIENT') {
      // Clients can only see shifts at their sites
      where.site = {
        client: {
          userId: currentUser.id
        }
      };
    } else {
      // Admin/Supervisor can see all shifts with optional filtering
      if (agentId) where.agentId = agentId;
      if (siteId) where.siteId = siteId;
    }

    if (status) {
      where.status = status;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get shifts with related data
    const [shifts, totalCount] = await Promise.all([
      prisma.shift.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        },
        include: {
          agent: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  phone: true
                }
              }
            }
          },
          site: {
            include: {
              client: {
                select: {
                  companyName: true
                }
              }
            }
          },
          timeEntries: {
            orderBy: {
              clockInTime: 'asc'
            }
          },
          reports: {
            select: {
              id: true,
              type: true,
              status: true,
              createdAt: true
            }
          }
        }
      }),
      prisma.shift.count({ where })
    ]);

    // Process shifts data with calculations
    const processedShifts = shifts.map(shift => {
      // Calculate shift duration and hours worked
      const scheduledDuration = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
      
      let actualDuration = 0;
      let hoursWorked = 0;
      let isCurrentlyActive = false;
      
      if (shift.actualStartTime) {
        const endTime = shift.actualEndTime || new Date();
        actualDuration = (endTime - new Date(shift.actualStartTime)) / (1000 * 60 * 60);
        
        if (!shift.actualEndTime) {
          isCurrentlyActive = true;
        }
      }

      // Calculate total hours from time entries
      shift.timeEntries.forEach(entry => {
        if (entry.clockInTime && entry.clockOutTime) {
          hoursWorked += (new Date(entry.clockOutTime) - new Date(entry.clockInTime)) / (1000 * 60 * 60);
        } else if (entry.clockInTime && !entry.clockOutTime) {
          // Currently clocked in
          hoursWorked += (new Date() - new Date(entry.clockInTime)) / (1000 * 60 * 60);
          isCurrentlyActive = true;
        }
      });

      // Calculate overtime
      const overtimeHours = Math.max(0, hoursWorked - scheduledDuration);

      // Determine shift status
      let shiftStatus = shift.status;
      if (isCurrentlyActive && shift.status === 'SCHEDULED') {
        shiftStatus = 'IN_PROGRESS';
      }

      return {
        id: shift.id,
        shiftDate: shift.shiftDate,
        startTime: shift.startTime,
        endTime: shift.endTime,
        actualStartTime: shift.actualStartTime,
        actualEndTime: shift.actualEndTime,
        status: shiftStatus,
        
        // Agent information
        agent: shift.agent ? {
          id: shift.agent.id,
          employeeId: shift.agent.employeeId,
          name: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
          phone: shift.agent.user.phone
        } : null,
        
        // Site information
        site: {
          id: shift.site.id,
          name: shift.site.name,
          address: shift.site.address,
          clientName: shift.site.client.companyName
        },
        
        // Time calculations
        scheduledDuration: Math.round(scheduledDuration * 100) / 100,
        actualDuration: Math.round(actualDuration * 100) / 100,
        hoursWorked: Math.round(hoursWorked * 100) / 100,
        overtimeHours: Math.round(overtimeHours * 100) / 100,
        isCurrentlyActive,
        
        // Performance indicators
        isOnTime: shift.actualStartTime ? 
          new Date(shift.actualStartTime) <= new Date(shift.startTime) : null,
        
        // Related data
        timeEntries: shift.timeEntries.map(entry => ({
          id: entry.id,
          clockInTime: entry.clockInTime,
          clockOutTime: entry.clockOutTime,
          clockInLatitude: entry.clockInLatitude,
          clockInLongitude: entry.clockInLongitude,
          clockOutLatitude: entry.clockOutLatitude,
          clockOutLongitude: entry.clockOutLongitude
        })),
        
        reports: shift.reports,
        
        // Timestamps
        createdAt: shift.createdAt,
        updatedAt: shift.updatedAt
      };
    });

    // Calculate summary statistics
    const summary = {
      totalShifts: totalCount,
      scheduledShifts: processedShifts.filter(s => s.status === 'SCHEDULED').length,
      inProgressShifts: processedShifts.filter(s => s.status === 'IN_PROGRESS').length,
      completedShifts: processedShifts.filter(s => s.status === 'COMPLETED').length,
      cancelledShifts: processedShifts.filter(s => s.status === 'CANCELLED').length,
      totalHoursScheduled: processedShifts.reduce((sum, s) => sum + s.scheduledDuration, 0),
      totalHoursWorked: processedShifts.reduce((sum, s) => sum + s.hoursWorked, 0),
      totalOvertimeHours: processedShifts.reduce((sum, s) => sum + s.overtimeHours, 0),
      onTimePercentage: processedShifts.filter(s => s.isOnTime !== null).length > 0 ?
        Math.round((processedShifts.filter(s => s.isOnTime === true).length / 
        processedShifts.filter(s => s.isOnTime !== null).length) * 100) : 0
    };

    return res.json({
      success: true,
      data: processedShifts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      summary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get shifts error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve shifts'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Create new shift
 */
async function createShift(req, res) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      agentId,
      siteId,
      shiftDate,
      startTime,
      endTime,
      specialInstructions,
      isRecurring = false,
      recurringPattern
    } = req.body;

    // Validate required fields
    if (!siteId || !shiftDate || !startTime || !endTime) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields: siteId, shiftDate, startTime, endTime'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Validate times
    const shiftStart = new Date(`${shiftDate}T${startTime}`);
    const shiftEnd = new Date(`${shiftDate}T${endTime}`);
    
    if (shiftEnd <= shiftStart) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'End time must be after start time'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if site exists
    const site = await prisma.site.findUnique({
      where: { id: siteId }
    });

    if (!site) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check agent availability if assigned
    if (agentId) {
      const agent = await prisma.agent.findUnique({
        where: { id: agentId }
      });

      if (!agent) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent not found'
          },
          timestamp: new Date().toISOString()
        });
      }

      if (!agent.isAvailable) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'AGENT_UNAVAILABLE',
            message: 'Agent is not available'
          },
          timestamp: new Date().toISOString()
        });
      }

      // Check for scheduling conflicts
      const conflictingShifts = await prisma.shift.findMany({
        where: {
          agentId,
          shiftDate: new Date(shiftDate),
          status: {
            in: ['SCHEDULED', 'IN_PROGRESS']
          },
          OR: [
            {
              startTime: {
                lt: shiftEnd
              },
              endTime: {
                gt: shiftStart
              }
            }
          ]
        }
      });

      if (conflictingShifts.length > 0) {
        return res.status(409).json({
          success: false,
          error: {
            code: 'SCHEDULING_CONFLICT',
            message: 'Agent has conflicting shifts at this time'
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    // Create shift(s)
    const shiftsToCreate = [];
    
    if (isRecurring && recurringPattern) {
      // Handle recurring shifts
      const { frequency, endDate, daysOfWeek } = recurringPattern;
      const currentDate = new Date(shiftDate);
      const recurringEndDate = new Date(endDate);
      
      while (currentDate <= recurringEndDate) {
        if (!daysOfWeek || daysOfWeek.includes(currentDate.getDay())) {
          shiftsToCreate.push({
            agentId,
            siteId,
            shiftDate: new Date(currentDate),
            startTime: shiftStart,
            endTime: shiftEnd,
            specialInstructions,
            status: 'SCHEDULED'
          });
        }
        
        // Increment date based on frequency
        if (frequency === 'DAILY') {
          currentDate.setDate(currentDate.getDate() + 1);
        } else if (frequency === 'WEEKLY') {
          currentDate.setDate(currentDate.getDate() + 7);
        }
      }
    } else {
      // Single shift
      shiftsToCreate.push({
        agentId,
        siteId,
        shiftDate: new Date(shiftDate),
        startTime: shiftStart,
        endTime: shiftEnd,
        specialInstructions,
        status: 'SCHEDULED'
      });
    }

    // Create shifts in database
    const createdShifts = await prisma.$transaction(
      shiftsToCreate.map(shiftData => 
        prisma.shift.create({
          data: shiftData,
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            site: {
              include: {
                client: {
                  select: {
                    companyName: true
                  }
                }
              }
            }
          }
        })
      )
    );

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'CREATE_SHIFT',
      tableName: 'shifts',
      recordId: createdShifts[0].id,
      newValues: { shiftsCreated: createdShifts.length, isRecurring },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.status(201).json({
      success: true,
      data: createdShifts,
      message: `${createdShifts.length} shift(s) created successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Create shift error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to create shift'
      },
      timestamp: new Date().toISOString()
    });
  }
}
