// BahinLink Site Shifts API
// ⚠️ CRITICAL: Real site shift management ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      siteId,
      page = 1,
      limit = 50,
      status,
      startDate,
      endDate,
      includeStats = 'true'
    } = req.query;

    const currentUser = req.user;

    // Validate siteId parameter
    if (!siteId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Site ID is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify site exists and check permissions
    const siteWhere = { id: siteId };
    if (currentUser.role === 'CLIENT') {
      siteWhere.client = {
        userId: currentUser.id
      };
    }

    const site = await prisma.site.findUnique({
      where: siteWhere,
      include: {
        client: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            serviceLevel: true
          }
        }
      }
    });

    if (!site) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found or access denied'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    // Build where clause
    const where = {
      siteId,
      shiftDate: {
        gte: start,
        lte: end
      }
    };

    if (status) {
      where.status = status;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get shifts with related data
    const [shifts, totalCount] = await Promise.all([
      prisma.shift.findMany({
        where,
        skip,
        take,
        orderBy: {
          shiftDate: 'desc'
        },
        include: {
          agent: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  phone: true
                }
              }
            }
          },
          timeEntries: {
            orderBy: {
              clockInTime: 'asc'
            }
          },
          reports: {
            select: {
              id: true,
              type: true,
              status: true,
              priority: true,
              title: true,
              createdAt: true
            }
          },
          geofenceViolations: {
            where: {
              isResolved: false
            },
            select: {
              id: true,
              violationType: true,
              distance: true,
              createdAt: true
            }
          }
        }
      }),
      prisma.shift.count({ where })
    ]);

    // Process shifts data with calculations
    const processedShifts = shifts.map(shift => {
      const scheduledDuration = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
      
      let hoursWorked = 0;
      let isCurrentlyActive = false;
      let totalBreakTime = 0;
      
      shift.timeEntries.forEach(entry => {
        if (entry.clockInTime && entry.clockOutTime) {
          hoursWorked += (new Date(entry.clockOutTime) - new Date(entry.clockInTime)) / (1000 * 60 * 60);
        } else if (entry.clockInTime && !entry.clockOutTime) {
          hoursWorked += (new Date() - new Date(entry.clockInTime)) / (1000 * 60 * 60);
          isCurrentlyActive = true;
        }
        
        if (entry.breakStartTime && entry.breakEndTime) {
          totalBreakTime += (new Date(entry.breakEndTime) - new Date(entry.breakStartTime)) / (1000 * 60 * 60);
        }
      });

      const overtimeHours = Math.max(0, hoursWorked - scheduledDuration);
      const isOnTime = shift.actualStartTime ? 
        new Date(shift.actualStartTime) <= new Date(shift.startTime) : null;

      // Security status calculation
      let securityStatus = 'protected';
      if (!isCurrentlyActive && shift.status === 'SCHEDULED') {
        securityStatus = 'unprotected';
      } else if (shift.geofenceViolations.length > 0) {
        securityStatus = 'alert';
      }

      return {
        id: shift.id,
        shiftDate: shift.shiftDate,
        startTime: shift.startTime,
        endTime: shift.endTime,
        actualStartTime: shift.actualStartTime,
        actualEndTime: shift.actualEndTime,
        status: isCurrentlyActive && shift.status === 'SCHEDULED' ? 'IN_PROGRESS' : shift.status,
        specialInstructions: shift.specialInstructions,
        
        // Agent information
        agent: shift.agent ? {
          id: shift.agent.id,
          employeeId: shift.agent.employeeId,
          name: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
          phone: shift.agent.user.phone,
          isAvailable: shift.agent.isAvailable
        } : null,
        
        // Time calculations
        scheduledDuration: Math.round(scheduledDuration * 100) / 100,
        hoursWorked: Math.round(hoursWorked * 100) / 100,
        overtimeHours: Math.round(overtimeHours * 100) / 100,
        totalBreakTime: Math.round(totalBreakTime * 100) / 100,
        isCurrentlyActive,
        isOnTime,
        
        // Security status
        securityStatus,
        
        // Activity summary
        timeEntriesCount: shift.timeEntries.length,
        reportsCount: shift.reports.length,
        urgentReportsCount: shift.reports.filter(r => r.priority === 'HIGH' || r.priority === 'URGENT').length,
        geofenceViolationsCount: shift.geofenceViolations.length,
        
        // Recent reports (limited for overview)
        recentReports: shift.reports.slice(0, 3).map(report => ({
          id: report.id,
          type: report.type,
          title: report.title,
          status: report.status,
          priority: report.priority,
          createdAt: report.createdAt
        })),
        
        // Timestamps
        createdAt: shift.createdAt,
        updatedAt: shift.updatedAt
      };
    });

    // Calculate statistics if requested
    let statistics = null;
    if (includeStats === 'true') {
      const completedShifts = processedShifts.filter(s => s.status === 'COMPLETED');
      const onTimeShifts = completedShifts.filter(s => s.isOnTime === true);
      const currentlyProtected = processedShifts.filter(s => s.securityStatus === 'protected').length;
      
      statistics = {
        totalShifts: totalCount,
        scheduledShifts: processedShifts.filter(s => s.status === 'SCHEDULED').length,
        inProgressShifts: processedShifts.filter(s => s.status === 'IN_PROGRESS').length,
        completedShifts: completedShifts.length,
        cancelledShifts: processedShifts.filter(s => s.status === 'CANCELLED').length,
        totalHoursScheduled: processedShifts.reduce((sum, s) => sum + s.scheduledDuration, 0),
        totalHoursWorked: processedShifts.reduce((sum, s) => sum + s.hoursWorked, 0),
        totalOvertimeHours: processedShifts.reduce((sum, s) => sum + s.overtimeHours, 0),
        onTimePercentage: completedShifts.length > 0 ? 
          Math.round((onTimeShifts.length / completedShifts.length) * 100) : 100,
        averageHoursPerShift: completedShifts.length > 0 ?
          Math.round((completedShifts.reduce((sum, s) => sum + s.hoursWorked, 0) / completedShifts.length) * 100) / 100 : 0,
        totalReports: processedShifts.reduce((sum, s) => sum + s.reportsCount, 0),
        totalUrgentReports: processedShifts.reduce((sum, s) => sum + s.urgentReportsCount, 0),
        totalGeofenceViolations: processedShifts.reduce((sum, s) => sum + s.geofenceViolationsCount, 0),
        securityCoverage: {
          protected: currentlyProtected,
          unprotected: processedShifts.filter(s => s.securityStatus === 'unprotected').length,
          alerts: processedShifts.filter(s => s.securityStatus === 'alert').length
        }
      };
    }

    return res.json({
      success: true,
      data: processedShifts,
      site: {
        id: site.id,
        name: site.name,
        address: site.address,
        siteType: site.siteType,
        client: site.client
      },
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      ...(statistics && { statistics }),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Site shifts API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to retrieve site shifts'
      },
      timestamp: new Date().toISOString()
    });
  }
};
