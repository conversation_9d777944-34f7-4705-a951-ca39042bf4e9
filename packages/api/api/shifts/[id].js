// BahinLink Shift Details API
// ⚠️ CRITICAL: Real shift management with time tracking ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { createAuditLog } = require('../../lib/audit');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const { id } = req.query;

    if (req.method === 'GET') {
      return await getShiftById(req, res, id);
    } else if (req.method === 'PUT') {
      return await updateShift(req, res, id);
    } else if (req.method === 'DELETE') {
      return await deleteShift(req, res, id);
    } else {
      return res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed'
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Shift details API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get shift by ID with comprehensive data
 */
async function getShiftById(req, res, shiftId) {
  try {
    const currentUser = req.user;

    // Build where clause based on user role
    const where = { id: shiftId };
    
    if (currentUser.role === 'AGENT') {
      where.agent = {
        userId: currentUser.id
      };
    } else if (currentUser.role === 'CLIENT') {
      where.site = {
        client: {
          userId: currentUser.id
        }
      };
    }

    const shift = await prisma.shift.findUnique({
      where,
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                phone: true,
                email: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                id: true,
                companyName: true,
                contactPerson: true,
                serviceLevel: true,
                user: {
                  select: {
                    email: true,
                    phone: true
                  }
                }
              }
            }
          }
        },
        timeEntries: {
          orderBy: {
            clockInTime: 'asc'
          }
        },
        reports: {
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        geofenceViolations: {
          include: {
            agent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!shift) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SHIFT_NOT_FOUND',
          message: 'Shift not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Calculate shift metrics
    const scheduledDuration = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
    
    let actualDuration = 0;
    let hoursWorked = 0;
    let isCurrentlyActive = false;
    let totalBreakTime = 0;
    
    if (shift.actualStartTime) {
      const endTime = shift.actualEndTime || new Date();
      actualDuration = (endTime - new Date(shift.actualStartTime)) / (1000 * 60 * 60);
      
      if (!shift.actualEndTime) {
        isCurrentlyActive = true;
      }
    }

    // Calculate hours from time entries
    shift.timeEntries.forEach(entry => {
      if (entry.clockInTime && entry.clockOutTime) {
        const entryDuration = (new Date(entry.clockOutTime) - new Date(entry.clockInTime)) / (1000 * 60 * 60);
        hoursWorked += entryDuration;
      } else if (entry.clockInTime && !entry.clockOutTime) {
        const entryDuration = (new Date() - new Date(entry.clockInTime)) / (1000 * 60 * 60);
        hoursWorked += entryDuration;
        isCurrentlyActive = true;
      }
      
      if (entry.breakStartTime && entry.breakEndTime) {
        totalBreakTime += (new Date(entry.breakEndTime) - new Date(entry.breakStartTime)) / (1000 * 60 * 60);
      }
    });

    // Calculate performance metrics
    const overtimeHours = Math.max(0, hoursWorked - scheduledDuration);
    const isOnTime = shift.actualStartTime ? 
      new Date(shift.actualStartTime) <= new Date(shift.startTime) : null;
    const isEarlyDeparture = shift.actualEndTime ? 
      new Date(shift.actualEndTime) < new Date(shift.endTime) : null;

    // Determine current status
    let currentStatus = shift.status;
    if (isCurrentlyActive && shift.status === 'SCHEDULED') {
      currentStatus = 'IN_PROGRESS';
    }

    const responseData = {
      id: shift.id,
      shiftDate: shift.shiftDate,
      startTime: shift.startTime,
      endTime: shift.endTime,
      actualStartTime: shift.actualStartTime,
      actualEndTime: shift.actualEndTime,
      status: currentStatus,
      specialInstructions: shift.specialInstructions,
      
      // Agent information
      agent: shift.agent ? {
        id: shift.agent.id,
        employeeId: shift.agent.employeeId,
        name: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
        phone: shift.agent.user.phone,
        email: shift.agent.user.email,
        isAvailable: shift.agent.isAvailable
      } : null,
      
      // Site information
      site: {
        id: shift.site.id,
        name: shift.site.name,
        address: shift.site.address,
        latitude: shift.site.latitude,
        longitude: shift.site.longitude,
        geofenceRadius: shift.site.geofenceRadius,
        client: shift.site.client
      },
      
      // Time calculations
      metrics: {
        scheduledDuration: Math.round(scheduledDuration * 100) / 100,
        actualDuration: Math.round(actualDuration * 100) / 100,
        hoursWorked: Math.round(hoursWorked * 100) / 100,
        overtimeHours: Math.round(overtimeHours * 100) / 100,
        totalBreakTime: Math.round(totalBreakTime * 100) / 100,
        isCurrentlyActive,
        isOnTime,
        isEarlyDeparture,
        efficiency: scheduledDuration > 0 ? Math.round((hoursWorked / scheduledDuration) * 100) : 0
      },
      
      // Time entries with location data
      timeEntries: shift.timeEntries.map(entry => ({
        id: entry.id,
        clockInTime: entry.clockInTime,
        clockOutTime: entry.clockOutTime,
        clockInLocation: entry.clockInLatitude && entry.clockInLongitude ? {
          latitude: entry.clockInLatitude,
          longitude: entry.clockInLongitude,
          accuracy: entry.clockInAccuracy
        } : null,
        clockOutLocation: entry.clockOutLatitude && entry.clockOutLongitude ? {
          latitude: entry.clockOutLatitude,
          longitude: entry.clockOutLongitude,
          accuracy: entry.clockOutAccuracy
        } : null,
        breakStartTime: entry.breakStartTime,
        breakEndTime: entry.breakEndTime,
        notes: entry.notes
      })),
      
      // Reports submitted during shift
      reports: shift.reports.map(report => ({
        id: report.id,
        type: report.type,
        title: report.title,
        status: report.status,
        priority: report.priority,
        description: report.description,
        agentName: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
        createdAt: report.createdAt,
        mediaCount: report.mediaUrls ? report.mediaUrls.length : 0
      })),
      
      // Geofence violations
      geofenceViolations: shift.geofenceViolations.map(violation => ({
        id: violation.id,
        violationType: violation.violationType,
        distance: violation.distance,
        latitude: violation.latitude,
        longitude: violation.longitude,
        isResolved: violation.isResolved,
        createdAt: violation.createdAt,
        resolvedAt: violation.resolvedAt,
        notes: violation.notes
      })),
      
      // Timestamps
      createdAt: shift.createdAt,
      updatedAt: shift.updatedAt
    };

    return res.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get shift by ID error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve shift'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Update shift information
 */
async function updateShift(req, res, shiftId) {
  try {
    // Require admin or supervisor role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN', 'SUPERVISOR'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      agentId,
      shiftDate,
      startTime,
      endTime,
      specialInstructions,
      status,
      actualStartTime,
      actualEndTime
    } = req.body;

    // Get existing shift
    const existingShift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        agent: true,
        site: true,
        timeEntries: true
      }
    });

    if (!existingShift) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SHIFT_NOT_FOUND',
          message: 'Shift not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Prepare update data
    const updateData = {};

    if (agentId !== undefined) {
      // Check if new agent exists and is available
      if (agentId) {
        const agent = await prisma.agent.findUnique({
          where: { id: agentId }
        });

        if (!agent) {
          return res.status(404).json({
            success: false,
            error: {
              code: 'AGENT_NOT_FOUND',
              message: 'Agent not found'
            },
            timestamp: new Date().toISOString()
          });
        }

        if (!agent.isAvailable) {
          return res.status(400).json({
            success: false,
            error: {
              code: 'AGENT_UNAVAILABLE',
              message: 'Agent is not available'
            },
            timestamp: new Date().toISOString()
          });
        }

        // Check for scheduling conflicts (excluding current shift)
        const conflictingShifts = await prisma.shift.findMany({
          where: {
            agentId,
            id: { not: shiftId },
            shiftDate: shiftDate ? new Date(shiftDate) : existingShift.shiftDate,
            status: {
              in: ['SCHEDULED', 'IN_PROGRESS']
            }
          }
        });

        if (conflictingShifts.length > 0) {
          return res.status(409).json({
            success: false,
            error: {
              code: 'SCHEDULING_CONFLICT',
              message: 'Agent has conflicting shifts at this time'
            },
            timestamp: new Date().toISOString()
          });
        }
      }
      updateData.agentId = agentId;
    }

    if (shiftDate !== undefined) updateData.shiftDate = new Date(shiftDate);
    if (startTime !== undefined) updateData.startTime = new Date(startTime);
    if (endTime !== undefined) updateData.endTime = new Date(endTime);
    if (specialInstructions !== undefined) updateData.specialInstructions = specialInstructions;
    if (status !== undefined) updateData.status = status;
    if (actualStartTime !== undefined) updateData.actualStartTime = actualStartTime ? new Date(actualStartTime) : null;
    if (actualEndTime !== undefined) updateData.actualEndTime = actualEndTime ? new Date(actualEndTime) : null;

    // Validate time logic
    if (updateData.startTime && updateData.endTime && updateData.endTime <= updateData.startTime) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'End time must be after start time'
        },
        timestamp: new Date().toISOString()
      });
    }

    updateData.updatedAt = new Date();

    // Update shift
    const updatedShift = await prisma.shift.update({
      where: { id: shiftId },
      data: updateData,
      include: {
        agent: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          include: {
            client: {
              select: {
                companyName: true
              }
            }
          }
        }
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'UPDATE_SHIFT',
      tableName: 'shifts',
      recordId: shiftId,
      oldValues: existingShift,
      newValues: updateData,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      data: updatedShift,
      message: 'Shift updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Update shift error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to update shift'
      },
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Cancel/Delete shift
 */
async function deleteShift(req, res, shiftId) {
  try {
    // Require admin role
    await new Promise((resolve, reject) => {
      requireRole(['ADMIN'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const existingShift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        timeEntries: true,
        reports: true
      }
    });

    if (!existingShift) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SHIFT_NOT_FOUND',
          message: 'Shift not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if shift can be cancelled
    if (existingShift.status === 'IN_PROGRESS') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'SHIFT_IN_PROGRESS',
          message: 'Cannot cancel shift that is currently in progress'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (existingShift.status === 'COMPLETED') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'SHIFT_COMPLETED',
          message: 'Cannot cancel completed shift'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check if there are time entries or reports
    if (existingShift.timeEntries.length > 0 || existingShift.reports.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'SHIFT_HAS_DATA',
          message: 'Cannot cancel shift with existing time entries or reports'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Cancel shift (soft delete)
    const cancelledShift = await prisma.shift.update({
      where: { id: shiftId },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date()
      }
    });

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'CANCEL_SHIFT',
      tableName: 'shifts',
      recordId: shiftId,
      oldValues: existingShift,
      newValues: { status: 'CANCELLED' },
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    return res.json({
      success: true,
      message: 'Shift cancelled successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete shift error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to cancel shift'
      },
      timestamp: new Date().toISOString()
    });
  }
}
