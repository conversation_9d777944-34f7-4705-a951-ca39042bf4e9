// BahinLink Agent Shifts API
// ⚠️ CRITICAL: Real agent shift management ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const {
      agentId,
      page = 1,
      limit = 50,
      status,
      startDate,
      endDate,
      includeStats = 'true'
    } = req.query;

    const currentUser = req.user;

    // Validate agentId parameter
    if (!agentId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Agent ID is required'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Check permissions
    if (currentUser.role === 'AGENT') {
      // Agents can only see their own shifts
      const userAgent = await prisma.agent.findUnique({
        where: { userId: currentUser.id }
      });

      if (!userAgent || userAgent.id !== agentId) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied to this agent\'s shifts'
          },
          timestamp: new Date().toISOString()
        });
      }
    } else if (currentUser.role === 'CLIENT') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Clients cannot access agent-specific shift data'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify agent exists
    const agent = await prisma.agent.findUnique({
      where: { id: agentId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true
          }
        }
      }
    });

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Date range setup
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    // Build where clause
    const where = {
      agentId,
      shiftDate: {
        gte: start,
        lte: end
      }
    };

    if (status) {
      where.status = status;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get shifts with related data
    const [shifts, totalCount] = await Promise.all([
      prisma.shift.findMany({
        where,
        skip,
        take,
        orderBy: {
          shiftDate: 'desc'
        },
        include: {
          site: {
            include: {
              client: {
                select: {
                  companyName: true
                }
              }
            }
          },
          timeEntries: {
            orderBy: {
              clockInTime: 'asc'
            }
          },
          reports: {
            select: {
              id: true,
              type: true,
              status: true,
              priority: true,
              createdAt: true
            }
          }
        }
      }),
      prisma.shift.count({ where })
    ]);

    // Process shifts data with calculations
    const processedShifts = shifts.map(shift => {
      const scheduledDuration = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
      
      let hoursWorked = 0;
      let isCurrentlyActive = false;
      let totalBreakTime = 0;
      
      shift.timeEntries.forEach(entry => {
        if (entry.clockInTime && entry.clockOutTime) {
          hoursWorked += (new Date(entry.clockOutTime) - new Date(entry.clockInTime)) / (1000 * 60 * 60);
        } else if (entry.clockInTime && !entry.clockOutTime) {
          hoursWorked += (new Date() - new Date(entry.clockInTime)) / (1000 * 60 * 60);
          isCurrentlyActive = true;
        }
        
        if (entry.breakStartTime && entry.breakEndTime) {
          totalBreakTime += (new Date(entry.breakEndTime) - new Date(entry.breakStartTime)) / (1000 * 60 * 60);
        }
      });

      const overtimeHours = Math.max(0, hoursWorked - scheduledDuration);
      const isOnTime = shift.actualStartTime ? 
        new Date(shift.actualStartTime) <= new Date(shift.startTime) : null;

      return {
        id: shift.id,
        shiftDate: shift.shiftDate,
        startTime: shift.startTime,
        endTime: shift.endTime,
        actualStartTime: shift.actualStartTime,
        actualEndTime: shift.actualEndTime,
        status: isCurrentlyActive && shift.status === 'SCHEDULED' ? 'IN_PROGRESS' : shift.status,
        specialInstructions: shift.specialInstructions,
        
        // Site information
        site: {
          id: shift.site.id,
          name: shift.site.name,
          address: shift.site.address,
          clientName: shift.site.client.companyName
        },
        
        // Time calculations
        scheduledDuration: Math.round(scheduledDuration * 100) / 100,
        hoursWorked: Math.round(hoursWorked * 100) / 100,
        overtimeHours: Math.round(overtimeHours * 100) / 100,
        totalBreakTime: Math.round(totalBreakTime * 100) / 100,
        isCurrentlyActive,
        isOnTime,
        
        // Activity summary
        timeEntriesCount: shift.timeEntries.length,
        reportsCount: shift.reports.length,
        urgentReportsCount: shift.reports.filter(r => r.priority === 'HIGH' || r.priority === 'URGENT').length,
        
        // Timestamps
        createdAt: shift.createdAt,
        updatedAt: shift.updatedAt
      };
    });

    // Calculate statistics if requested
    let statistics = null;
    if (includeStats === 'true') {
      const completedShifts = processedShifts.filter(s => s.status === 'COMPLETED');
      const onTimeShifts = completedShifts.filter(s => s.isOnTime === true);
      
      statistics = {
        totalShifts: totalCount,
        scheduledShifts: processedShifts.filter(s => s.status === 'SCHEDULED').length,
        inProgressShifts: processedShifts.filter(s => s.status === 'IN_PROGRESS').length,
        completedShifts: completedShifts.length,
        cancelledShifts: processedShifts.filter(s => s.status === 'CANCELLED').length,
        totalHoursScheduled: processedShifts.reduce((sum, s) => sum + s.scheduledDuration, 0),
        totalHoursWorked: processedShifts.reduce((sum, s) => sum + s.hoursWorked, 0),
        totalOvertimeHours: processedShifts.reduce((sum, s) => sum + s.overtimeHours, 0),
        onTimePercentage: completedShifts.length > 0 ? 
          Math.round((onTimeShifts.length / completedShifts.length) * 100) : 100,
        averageHoursPerShift: completedShifts.length > 0 ?
          Math.round((completedShifts.reduce((sum, s) => sum + s.hoursWorked, 0) / completedShifts.length) * 100) / 100 : 0,
        totalReports: processedShifts.reduce((sum, s) => sum + s.reportsCount, 0),
        totalUrgentReports: processedShifts.reduce((sum, s) => sum + s.urgentReportsCount, 0)
      };
    }

    return res.json({
      success: true,
      data: processedShifts,
      agent: {
        id: agent.id,
        employeeId: agent.employeeId,
        name: `${agent.user.firstName} ${agent.user.lastName}`,
        email: agent.user.email,
        phone: agent.user.phone,
        isAvailable: agent.isAvailable
      },
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      ...(statistics && { statistics }),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Agent shifts API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to retrieve agent shifts'
      },
      timestamp: new Date().toISOString()
    });
  }
};
