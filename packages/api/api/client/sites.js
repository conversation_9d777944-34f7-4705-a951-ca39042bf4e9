// BahinLink Client Sites API
// ⚠️ CRITICAL: Real client site data with live security status ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { getCachedSite, cacheSite } = require('../../lib/redis');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication and client role
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    await new Promise((resolve, reject) => {
      requireRole(['CLIENT'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await getClientSites(req, res);
  } catch (error) {
    console.error('Client sites API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get client's sites with real security status
 */
async function getClientSites(req, res) {
  try {
    const user = req.user;
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    // Get client record
    const client = await prisma.client.findUnique({
      where: { userId: user.id }
    });

    if (!client) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Client profile not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get client's sites with real security data
    const sites = await prisma.site.findMany({
      where: {
        clientId: client.id,
        isActive: true
      },
      include: {
        shifts: {
          where: {
            shiftDate: {
              gte: startOfDay,
              lte: endOfDay
            }
          },
          include: {
            agent: {
              include: {
                user: true
              }
            },
            timeEntries: {
              where: {
                clockInTime: {
                  not: null
                },
                clockOutTime: null
              }
            }
          }
        },
        reports: {
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        geofenceViolations: {
          where: {
            createdAt: {
              gte: startOfDay,
              lte: endOfDay
            },
            isResolved: false
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Process sites data with real security metrics
    const sitesData = sites.map(site => {
      // Calculate active agents
      const activeShifts = site.shifts.filter(shift => 
        shift.status === 'IN_PROGRESS' && 
        shift.timeEntries.length > 0
      );
      
      const activeAgents = activeShifts.length;
      
      // Get latest report
      const latestReport = site.reports[0];
      
      // Calculate security score based on real metrics
      let securityScore = 100;
      if (activeAgents === 0) securityScore -= 30; // No active guards
      if (site.geofenceViolations.length > 0) securityScore -= 20; // Geofence violations
      if (!latestReport || (Date.now() - new Date(latestReport.createdAt).getTime()) > 24 * 60 * 60 * 1000) {
        securityScore -= 15; // No recent reports
      }
      
      // Determine security status
      let securityStatus = 'protected';
      if (activeAgents === 0) securityStatus = 'unprotected';
      else if (site.geofenceViolations.length > 0) securityStatus = 'alert';
      
      return {
        id: site.id,
        name: site.name,
        address: site.address,
        latitude: site.latitude,
        longitude: site.longitude,
        geofenceRadius: site.geofenceRadius,
        siteType: site.siteType,
        specialInstructions: site.specialInstructions,
        
        // Real security metrics
        activeAgents,
        securityStatus,
        securityScore: Math.max(0, securityScore),
        
        // Current shifts
        currentShifts: activeShifts.map(shift => ({
          id: shift.id,
          agentName: `${shift.agent.user.firstName} ${shift.agent.user.lastName}`,
          employeeId: shift.agent.employeeId,
          startTime: shift.startTime,
          endTime: shift.endTime,
          actualStartTime: shift.actualStartTime,
          status: shift.status,
          clockedIn: shift.timeEntries.length > 0,
          clockInTime: shift.timeEntries[0]?.clockInTime
        })),
        
        // Recent reports
        recentReports: site.reports.map(report => ({
          id: report.id,
          type: report.type,
          title: report.title,
          status: report.status,
          priority: report.priority,
          createdAt: report.createdAt,
          submittedAt: report.submittedAt
        })),
        
        // Security alerts
        alerts: site.geofenceViolations.map(violation => ({
          id: violation.id,
          type: 'geofence_violation',
          message: `Agent ${violation.distanceFromSite}m away from site`,
          severity: violation.distanceFromSite > site.geofenceRadius * 2 ? 'high' : 'medium',
          createdAt: violation.createdAt
        })),
        
        // Access information
        accessCodes: site.accessCodes,
        emergencyContacts: site.emergencyContacts,
        
        // Timestamps
        createdAt: site.createdAt,
        updatedAt: site.updatedAt
      };
    });

    // Cache the sites data
    for (const siteData of sitesData) {
      await cacheSite(siteData.id, siteData);
    }

    return res.json({
      success: true,
      data: sitesData,
      summary: {
        totalSites: sitesData.length,
        protectedSites: sitesData.filter(s => s.securityStatus === 'protected').length,
        unprotectedSites: sitesData.filter(s => s.securityStatus === 'unprotected').length,
        sitesWithAlerts: sitesData.filter(s => s.alerts.length > 0).length,
        averageSecurityScore: sitesData.reduce((sum, s) => sum + s.securityScore, 0) / sitesData.length || 0,
        totalActiveAgents: sitesData.reduce((sum, s) => sum + s.activeAgents, 0)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get client sites error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve client sites'
      },
      timestamp: new Date().toISOString()
    });
  }
}
