// BahinLink Client Agents API
// ⚠️ CRITICAL: Real agent data for client sites with live GPS tracking ONLY

const { requireAuth, requireRole } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');
const { getCachedAgentLocation } = require('../../lib/redis');
const { calculateDistance } = require('@bahinlink/shared');

module.exports = async function handler(req, res) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Require authentication and client role
    await new Promise((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    await new Promise((resolve, reject) => {
      requireRole(['CLIENT'])(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    return await getClientAgents(req, res);
  } catch (error) {
    console.error('Client agents API error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get agents assigned to client's sites with real GPS data
 */
async function getClientAgents(req, res) {
  try {
    const user = req.user;
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    // Get client record
    const client = await prisma.client.findUnique({
      where: { userId: user.id }
    });

    if (!client) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Client profile not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get agents assigned to client's sites
    const agents = await prisma.agent.findMany({
      where: {
        shifts: {
          some: {
            site: {
              clientId: client.id
            },
            shiftDate: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        }
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            phone: true,
            email: true
          }
        },
        shifts: {
          where: {
            site: {
              clientId: client.id
            },
            shiftDate: {
              gte: startOfDay,
              lte: endOfDay
            }
          },
          include: {
            site: true,
            timeEntries: {
              where: {
                clockInTime: {
                  not: null
                },
                clockOutTime: null
              }
            }
          }
        }
      }
    });

    // Process agents data with real GPS and status information
    const agentsData = await Promise.all(agents.map(async (agent) => {
      // Get current shift
      const currentShift = agent.shifts.find(shift => 
        shift.status === 'IN_PROGRESS' || 
        (shift.status === 'SCHEDULED' && shift.timeEntries.length > 0)
      );

      // Get cached location data for more recent updates
      const cachedLocation = await getCachedAgentLocation(agent.id);
      
      // Use cached location if more recent, otherwise use database location
      const location = cachedLocation && 
        new Date(cachedLocation.timestamp) > (agent.lastLocationUpdate || new Date(0))
        ? cachedLocation
        : {
            latitude: agent.currentLatitude,
            longitude: agent.currentLongitude,
            accuracy: 10, // Default accuracy
            timestamp: agent.lastLocationUpdate?.toISOString()
          };

      // Calculate distance from assigned site if on shift
      let distanceFromSite = null;
      let withinGeofence = null;
      let siteInfo = null;

      if (currentShift && location.latitude && location.longitude) {
        const site = currentShift.site;
        distanceFromSite = calculateDistance(
          location.latitude,
          location.longitude,
          site.latitude,
          site.longitude
        );
        withinGeofence = distanceFromSite <= site.geofenceRadius;
        
        siteInfo = {
          id: site.id,
          name: site.name,
          address: site.address,
          geofenceRadius: site.geofenceRadius
        };
      }

      // Determine agent status
      let status = 'offline';
      if (agent.isAvailable) {
        if (currentShift) {
          if (currentShift.timeEntries.length > 0) {
            status = 'active'; // Clocked in and on duty
          } else {
            status = 'scheduled'; // Scheduled but not clocked in
          }
        } else {
          status = 'available'; // Available but not on shift
        }
      }

      // Calculate performance metrics
      const totalShifts = agent.performanceStats?.totalShifts || 0;
      const onTimePercentage = agent.performanceStats?.onTimePercentage || 0;
      const clientSatisfactionScore = agent.performanceStats?.clientSatisfactionScore || 0;

      return {
        id: agent.id,
        employeeId: agent.employeeId,
        
        // Personal information (limited for client view)
        name: `${agent.user.firstName} ${agent.user.lastName}`,
        firstName: agent.user.firstName,
        lastName: agent.user.lastName,
        phone: agent.user.phone, // Clients can contact agents
        
        // Current status
        status,
        isAvailable: agent.isAvailable,
        
        // Location data (real GPS coordinates)
        location: location.latitude && location.longitude ? {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy,
          lastUpdate: location.timestamp,
          distanceFromSite: distanceFromSite ? Math.round(distanceFromSite) : null,
          withinGeofence
        } : null,
        
        // Current assignment
        currentShift: currentShift ? {
          id: currentShift.id,
          site: siteInfo,
          startTime: currentShift.startTime,
          endTime: currentShift.endTime,
          actualStartTime: currentShift.actualStartTime,
          status: currentShift.status,
          clockedIn: currentShift.timeEntries.length > 0,
          clockInTime: currentShift.timeEntries[0]?.clockInTime
        } : null,
        
        // Professional information
        certifications: agent.certifications,
        skills: agent.skills,
        hireDate: agent.hireDate,
        
        // Performance metrics (relevant for client)
        performanceMetrics: {
          totalShifts,
          onTimePercentage,
          clientSatisfactionScore,
          reportQualityScore: agent.performanceStats?.reportQualityScore || 0
        },
        
        // Emergency contact (for client safety)
        emergencyContact: {
          name: agent.emergencyContactName,
          phone: agent.emergencyContactPhone
        },
        
        // Timestamps
        lastLocationUpdate: agent.lastLocationUpdate,
        createdAt: agent.createdAt
      };
    }));

    // Sort agents by status priority (active first, then scheduled, etc.)
    const statusPriority = { active: 1, scheduled: 2, available: 3, offline: 4 };
    agentsData.sort((a, b) => statusPriority[a.status] - statusPriority[b.status]);

    return res.json({
      success: true,
      data: agentsData,
      summary: {
        totalAgents: agentsData.length,
        activeAgents: agentsData.filter(a => a.status === 'active').length,
        scheduledAgents: agentsData.filter(a => a.status === 'scheduled').length,
        availableAgents: agentsData.filter(a => a.status === 'available').length,
        offlineAgents: agentsData.filter(a => a.status === 'offline').length,
        agentsWithLocation: agentsData.filter(a => a.location).length,
        agentsWithinGeofence: agentsData.filter(a => a.location?.withinGeofence === true).length,
        averageClientSatisfaction: agentsData.reduce((sum, a) => 
          sum + a.performanceMetrics.clientSatisfactionScore, 0) / agentsData.length || 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get client agents error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve client agents'
      },
      timestamp: new Date().toISOString()
    });
  }
}
