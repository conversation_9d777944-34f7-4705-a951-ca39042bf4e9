// BahinLink Clerk Webhook Handler
// ⚠️ CRITICAL: Real Clerk webhook integration for user management ONLY

const { verifyClerkWebhook, syncUserFrom<PERSON>lerk, deleteUserFromClerk } = require('../../lib/auth');
const { prisma } = require('../../lib/prisma');

module.exports = async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Get raw body for signature verification
    const rawBody = JSON.stringify(req.body);
    const signature = req.headers['svix-signature'] || req.headers['clerk-signature'];

    if (!signature) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_SIGNATURE',
          message: 'Missing webhook signature'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify webhook signature with real Clerk secret
    const isValid = verifyClerkWebhook(rawBody, signature);
    if (!isValid) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_SIGNATURE',
          message: 'Invalid webhook signature'
        },
        timestamp: new Date().toISOString()
      });
    }

    const { type, data } = req.body;

    console.log(`Processing Clerk webhook: ${type}`);

    switch (type) {
      case 'user.created':
        await handleUserCreated(data);
        break;
      
      case 'user.updated':
        await handleUserUpdated(data);
        break;
      
      case 'user.deleted':
        await handleUserDeleted(data);
        break;
      
      case 'session.created':
        await handleSessionCreated(data);
        break;
      
      case 'session.ended':
        await handleSessionEnded(data);
        break;
      
      default:
        console.log(`Unhandled webhook type: ${type}`);
    }

    return res.status(200).json({
      success: true,
      message: `Webhook ${type} processed successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Clerk webhook error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'WEBHOOK_PROCESSING_ERROR',
        message: 'Failed to process webhook'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Handle user created webhook from Clerk
 */
async function handleUserCreated(clerkUser) {
  try {
    console.log(`Creating user for Clerk ID: ${clerkUser.id}`);
    
    // Extract user data from Clerk
    const email = clerkUser.email_addresses?.[0]?.email_address;
    const phone = clerkUser.phone_numbers?.[0]?.phone_number;
    
    if (!email) {
      throw new Error('No email address found in Clerk user data');
    }

    // Determine role from email domain or metadata
    let role = 'AGENT'; // Default role
    
    if (clerkUser.public_metadata?.role) {
      role = clerkUser.public_metadata.role;
    } else if (email.includes('@bahinsarl.com')) {
      // Bahin SARL employees
      if (email.includes('admin')) {
        role = 'ADMIN';
      } else if (email.includes('supervisor')) {
        role = 'SUPERVISOR';
      } else {
        role = 'AGENT';
      }
    } else {
      // External clients
      role = 'CLIENT';
    }

    // Create user in real database
    const user = await syncUserFromClerk({
      ...clerkUser,
      public_metadata: { role }
    });

    console.log(`User created successfully: ${user.email} (${user.role})`);

    // Send welcome notification based on role
    await sendWelcomeNotification(user);

  } catch (error) {
    console.error('Error handling user created:', error);
    throw error;
  }
}

/**
 * Handle user updated webhook from Clerk
 */
async function handleUserUpdated(clerkUser) {
  try {
    console.log(`Updating user for Clerk ID: ${clerkUser.id}`);
    
    // Update user in real database
    const user = await syncUserFromClerk(clerkUser);
    
    console.log(`User updated successfully: ${user.email}`);

  } catch (error) {
    console.error('Error handling user updated:', error);
    throw error;
  }
}

/**
 * Handle user deleted webhook from Clerk
 */
async function handleUserDeleted(clerkUser) {
  try {
    console.log(`Deleting user for Clerk ID: ${clerkUser.id}`);
    
    // Soft delete user in real database (set isActive to false)
    await prisma.user.update({
      where: { clerkId: clerkUser.id },
      data: { 
        isActive: false,
        updatedAt: new Date()
      }
    });
    
    console.log(`User deactivated successfully: ${clerkUser.id}`);

  } catch (error) {
    console.error('Error handling user deleted:', error);
    throw error;
  }
}

/**
 * Handle session created webhook from Clerk
 */
async function handleSessionCreated(sessionData) {
  try {
    const userId = sessionData.user_id;
    
    // Update last login time in real database
    await prisma.user.update({
      where: { clerkId: userId },
      data: { 
        updatedAt: new Date() // Track last activity
      }
    });

    console.log(`Session created for user: ${userId}`);

  } catch (error) {
    console.error('Error handling session created:', error);
    // Don't throw error for session events to avoid blocking
  }
}

/**
 * Handle session ended webhook from Clerk
 */
async function handleSessionEnded(sessionData) {
  try {
    const userId = sessionData.user_id;
    
    // Clear cached user session
    const { invalidateUserSession } = require('../../lib/redis');
    await invalidateUserSession(userId);

    console.log(`Session ended for user: ${userId}`);

  } catch (error) {
    console.error('Error handling session ended:', error);
    // Don't throw error for session events to avoid blocking
  }
}

/**
 * Send welcome notification to new user
 */
async function sendWelcomeNotification(user) {
  try {
    let welcomeMessage = '';
    let notificationType = 'welcome';

    switch (user.role) {
      case 'ADMIN':
        welcomeMessage = `Welcome to BahinLink, ${user.firstName}! You have administrator access to manage the entire security system.`;
        break;
      case 'SUPERVISOR':
        welcomeMessage = `Welcome to BahinLink, ${user.firstName}! You can now supervise security operations and manage field agents.`;
        break;
      case 'AGENT':
        welcomeMessage = `Welcome to BahinLink, ${user.firstName}! Download the mobile app to start your security shifts and submit reports.`;
        break;
      case 'CLIENT':
        welcomeMessage = `Welcome to BahinLink, ${user.firstName}! You can now monitor your security services and view real-time reports.`;
        break;
    }

    // Create welcome notification in real database
    await prisma.notification.create({
      data: {
        recipientId: user.id,
        type: notificationType,
        title: 'Welcome to BahinLink',
        message: welcomeMessage,
        priority: 'NORMAL',
        deliveryMethod: 'APP',
        data: {
          userRole: user.role,
          isWelcome: true
        }
      }
    });

    console.log(`Welcome notification sent to: ${user.email}`);

  } catch (error) {
    console.error('Error sending welcome notification:', error);
    // Don't throw error to avoid blocking user creation
  }
}
