{"name": "@bahinlink/web-admin", "version": "1.0.0", "description": "BahinLink Web Admin Dashboard - Real-time monitoring and management", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "vercel --prod"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.0.4", "@clerk/nextjs": "^4.29.1", "@mui/material": "^5.15.3", "@mui/icons-material": "^5.15.3", "@mui/x-data-grid": "^6.18.3", "@mui/x-date-pickers": "^6.18.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "react-leaflet": "^4.2.1", "leaflet": "^1.9.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "date-fns": "^3.0.6", "lodash": "^4.17.21", "react-beautiful-dnd": "^13.1.1", "react-dropzone": "^14.2.3", "react-csv": "^2.2.2", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "react-signature-canvas": "^1.0.6", "@vercel/analytics": "^1.1.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4"}}