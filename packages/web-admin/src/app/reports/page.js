// BahinLink Web Admin Reports Management Page
// ⚠️ CRITICAL: Real report management with approval workflow ONLY

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Rating
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Assignment as AssignmentIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ReportTable from '../../components/ReportTable';
import { ApiService } from '../../services/ApiService';

const ReportsPage = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    priority: '',
    agentId: '',
    siteId: '',
    startDate: null,
    endDate: null
  });
  const [agents, setAgents] = useState([]);
  const [sites, setSites] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    overdue: 0
  });
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reviewAction, setReviewAction] = useState('');
  const [reviewData, setReviewData] = useState({
    notes: '',
    qualityScore: 5
  });

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = {};
      if (filters.status) params.status = filters.status;
      if (filters.type) params.type = filters.type;
      if (filters.priority) params.priority = filters.priority;
      if (filters.agentId) params.agentId = filters.agentId;
      if (filters.siteId) params.siteId = filters.siteId;
      if (filters.startDate) params.startDate = filters.startDate.toISOString();
      if (filters.endDate) params.endDate = filters.endDate.toISOString();

      const [reportsResponse, agentsResponse, sitesResponse] = await Promise.all([
        ApiService.get('/reports', params),
        ApiService.get('/agents'),
        ApiService.get('/sites')
      ]);

      if (reportsResponse.success) {
        setReports(reportsResponse.data);
        
        // Calculate stats
        const now = new Date();
        const newStats = {
          total: reportsResponse.data.length,
          pending: reportsResponse.data.filter(r => r.status === 'SUBMITTED').length,
          approved: reportsResponse.data.filter(r => r.status === 'APPROVED').length,
          rejected: reportsResponse.data.filter(r => r.status === 'REJECTED').length,
          overdue: reportsResponse.data.filter(r => {
            if (r.status !== 'SUBMITTED') return false;
            const submittedAt = new Date(r.submittedAt);
            const hoursSinceSubmission = (now - submittedAt) / (1000 * 60 * 60);
            return hoursSinceSubmission > 24;
          }).length
        };
        setStats(newStats);
      }

      if (agentsResponse.success) {
        setAgents(agentsResponse.data);
      }

      if (sitesResponse.success) {
        setSites(sitesResponse.data);
      }
    } catch (error) {
      console.error('Error loading reports data:', error);
      setError('Failed to load reports data');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (reportId, reviewData) => {
    try {
      const response = await ApiService.put(`/reports/${reportId}`, {
        status: 'APPROVED',
        reviewNotes: reviewData.notes,
        qualityScore: reviewData.qualityScore,
        reviewedAt: new Date().toISOString()
      });
      
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error approving report:', error);
      setError('Failed to approve report');
    }
  };

  const handleReject = async (reportId, reviewData) => {
    try {
      const response = await ApiService.put(`/reports/${reportId}`, {
        status: 'REJECTED',
        reviewNotes: reviewData.notes,
        qualityScore: reviewData.qualityScore,
        reviewedAt: new Date().toISOString()
      });
      
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error rejecting report:', error);
      setError('Failed to reject report');
    }
  };

  const handleEdit = async (reportId) => {
    // Navigate to edit page or open edit dialog
    console.log('Edit report:', reportId);
  };

  const handleDelete = async (reportId) => {
    try {
      const response = await ApiService.delete(`/reports/${reportId}`);
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error deleting report:', error);
      setError('Failed to delete report');
    }
  };

  const handleView = (reportId) => {
    // Navigate to report details page
    console.log('View report:', reportId);
  };

  const handleExport = async () => {
    try {
      // In a real app, this would generate and download a CSV/Excel file
      console.log('Exporting reports data...');
      alert('Export functionality would be implemented here');
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {title}
            </Typography>
          </Box>
          <Icon sx={{ fontSize: 40, color: `${color}.main`, opacity: 0.7 }} />
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" fontWeight="bold">
            Reports Management
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Total Reports"
              value={stats.total}
              icon={AssignmentIcon}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Pending Review"
              value={stats.pending}
              icon={ScheduleIcon}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Approved"
              value={stats.approved}
              icon={ApproveIcon}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Rejected"
              value={stats.rejected}
              icon={RejectIcon}
              color="error"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Overdue"
              value={stats.overdue}
              icon={WarningIcon}
              color="error"
            />
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="DRAFT">Draft</MenuItem>
                  <MenuItem value="SUBMITTED">Submitted</MenuItem>
                  <MenuItem value="APPROVED">Approved</MenuItem>
                  <MenuItem value="REJECTED">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={filters.type}
                  label="Type"
                  onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="INCIDENT">Incident</MenuItem>
                  <MenuItem value="MAINTENANCE">Maintenance</MenuItem>
                  <MenuItem value="SECURITY">Security</MenuItem>
                  <MenuItem value="SAFETY">Safety</MenuItem>
                  <MenuItem value="GENERAL">General</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  label="Priority"
                  onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
                >
                  <MenuItem value="">All Priorities</MenuItem>
                  <MenuItem value="LOW">Low</MenuItem>
                  <MenuItem value="MEDIUM">Medium</MenuItem>
                  <MenuItem value="HIGH">High</MenuItem>
                  <MenuItem value="URGENT">Urgent</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Agent</InputLabel>
                <Select
                  value={filters.agentId}
                  label="Agent"
                  onChange={(e) => setFilters(prev => ({ ...prev, agentId: e.target.value }))}
                >
                  <MenuItem value="">All Agents</MenuItem>
                  {agents.map((agent) => (
                    <MenuItem key={agent.id} value={agent.id}>
                      {agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : 'Unknown'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="Start Date"
                value={filters.startDate}
                onChange={(date) => setFilters(prev => ({ ...prev, startDate: date }))}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({
                  status: '',
                  type: '',
                  priority: '',
                  agentId: '',
                  siteId: '',
                  startDate: null,
                  endDate: null
                })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Reports Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <ReportTable
            reports={reports}
            loading={loading}
            onView={handleView}
            onApprove={handleApprove}
            onReject={handleReject}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default ReportsPage;
