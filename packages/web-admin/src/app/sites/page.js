// BahinLink Web Admin - Sites Management Page
// ⚠️ CRITICAL: Real site management with GPS coordinates ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Fab
} from '@mui/material';
import {
  Add,
  Edit,
  LocationOn,
  Security,
  Business,
  Map,
  Refresh,
  Download,
  QrCode,
  Warning
} from '@mui/icons-material';

import ApiService from '../../services/ApiService';
import SiteTable from '../../components/SiteTable';
import SiteMap from '../../components/SiteMap';
import { formatTime, formatDate } from '@bahinlink/shared';

const SITE_TYPES = [
  { value: 'COMMERCIAL', label: 'Commercial' },
  { value: 'RESIDENTIAL', label: 'Residential' },
  { value: 'INDUSTRIAL', label: 'Industrial' },
  { value: 'GOVERNMENT', label: 'Government' },
  { value: 'EDUCATIONAL', label: 'Educational' },
  { value: 'HEALTHCARE', label: 'Healthcare' }
];

export default function SitesPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [sites, setSites] = useState([]);
  const [clients, setClients] = useState([]);
  const [filteredSites, setFilteredSites] = useState([]);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filters and pagination
  const [filters, setFilters] = useState({
    clientId: '',
    isActive: 'true',
    search: '',
    siteType: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0
  });

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedSite, setSelectedSite] = useState(null);
  const [mapViewOpen, setMapViewOpen] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    clientId: '',
    name: '',
    address: '',
    latitude: '',
    longitude: '',
    geofenceRadius: 100,
    siteType: 'COMMERCIAL',
    specialInstructions: '',
    accessCodes: [],
    emergencyContacts: []
  });

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadSites();
      loadClients();
      
      // Auto-refresh every 60 seconds
      const interval = setInterval(loadSites, 60000);
      return () => clearInterval(interval);
    }
  }, [isLoaded, isSignedIn, filters, pagination.page]);

  const loadSites = async () => {
    try {
      setError(null);
      
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        includeStats: 'true',
        ...filters
      };

      const response = await ApiService.get('/sites', params);
      
      if (response.success) {
        setSites(response.data);
        setFilteredSites(response.data);
        setPagination(prev => ({
          ...prev,
          total: response.pagination.total
        }));
      } else {
        throw new Error(response.error?.message || 'Failed to load sites');
      }
    } catch (error) {
      console.error('Load sites error:', error);
      setError(error.message || 'Failed to load sites');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadClients = async () => {
    try {
      const response = await ApiService.get('/clients');
      if (response.success) {
        setClients(response.data);
      }
    } catch (error) {
      console.error('Load clients error:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSites();
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreateSite = async () => {
    try {
      // Validate required fields
      if (!formData.clientId || !formData.name || !formData.address || 
          !formData.latitude || !formData.longitude) {
        setError('Please fill in all required fields');
        return;
      }

      // Validate GPS coordinates
      const lat = parseFloat(formData.latitude);
      const lng = parseFloat(formData.longitude);
      
      if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        setError('Please enter valid GPS coordinates');
        return;
      }

      const response = await ApiService.post('/sites', {
        ...formData,
        latitude: lat,
        longitude: lng,
        geofenceRadius: parseInt(formData.geofenceRadius)
      });

      if (response.success) {
        setCreateDialogOpen(false);
        resetForm();
        await loadSites();
      } else {
        throw new Error(response.error?.message || 'Failed to create site');
      }
    } catch (error) {
      console.error('Create site error:', error);
      setError(error.message || 'Failed to create site');
    }
  };

  const handleEditSite = (site) => {
    setSelectedSite(site);
    setFormData({
      clientId: site.client.id,
      name: site.name,
      address: site.address,
      latitude: site.latitude.toString(),
      longitude: site.longitude.toString(),
      geofenceRadius: site.geofenceRadius,
      siteType: site.siteType,
      specialInstructions: site.specialInstructions || '',
      accessCodes: site.accessCodes || [],
      emergencyContacts: site.emergencyContacts || []
    });
    setEditDialogOpen(true);
  };

  const handleUpdateSite = async () => {
    try {
      const lat = parseFloat(formData.latitude);
      const lng = parseFloat(formData.longitude);
      
      if (isNaN(lat) || isNaN(lng)) {
        setError('Please enter valid GPS coordinates');
        return;
      }

      const response = await ApiService.put(`/sites/${selectedSite.id}`, {
        ...formData,
        latitude: lat,
        longitude: lng,
        geofenceRadius: parseInt(formData.geofenceRadius)
      });

      if (response.success) {
        setEditDialogOpen(false);
        setSelectedSite(null);
        resetForm();
        await loadSites();
      } else {
        throw new Error(response.error?.message || 'Failed to update site');
      }
    } catch (error) {
      console.error('Update site error:', error);
      setError(error.message || 'Failed to update site');
    }
  };

  const resetForm = () => {
    setFormData({
      clientId: '',
      name: '',
      address: '',
      latitude: '',
      longitude: '',
      geofenceRadius: 100,
      siteType: 'COMMERCIAL',
      specialInstructions: '',
      accessCodes: [],
      emergencyContacts: []
    });
  };

  const getSecurityStatusColor = (status) => {
    switch (status) {
      case 'protected': return 'success';
      case 'alert': return 'warning';
      case 'unprotected': return 'error';
      default: return 'default';
    }
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography variant="h6">Loading sites...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Site Management
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Map />}
            onClick={() => setMapViewOpen(true)}
          >
            Map View
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Add Site
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Sites
              </Typography>
              <Typography variant="h4" component="div">
                {sites.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Protected Sites
              </Typography>
              <Typography variant="h4" component="div" color="success.main">
                {sites.filter(s => s.stats?.securityStatus === 'protected').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Active Agents
              </Typography>
              <Typography variant="h4" component="div" color="info.main">
                {sites.reduce((sum, s) => sum + (s.stats?.activeAgents || 0), 0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Alerts
              </Typography>
              <Typography variant="h4" component="div" color="warning.main">
                {sites.filter(s => s.stats?.securityStatus === 'alert').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Site name, address..."
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Client</InputLabel>
                <Select
                  value={filters.clientId}
                  onChange={(e) => handleFilterChange('clientId', e.target.value)}
                  label="Client"
                >
                  <MenuItem value="">All Clients</MenuItem>
                  {clients.map((client) => (
                    <MenuItem key={client.id} value={client.id}>
                      {client.companyName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Site Type</InputLabel>
                <Select
                  value={filters.siteType}
                  onChange={(e) => handleFilterChange('siteType', e.target.value)}
                  label="Site Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {SITE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Download />}
                onClick={() => {/* Export functionality */}}
              >
                Export
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Sites Table */}
      <SiteTable
        sites={filteredSites}
        onEdit={handleEditSite}
        onRefresh={loadSites}
        pagination={pagination}
        onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
      />

      {/* Create Site Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Site</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Client</InputLabel>
                <Select
                  value={formData.clientId}
                  onChange={(e) => setFormData(prev => ({ ...prev, clientId: e.target.value }))}
                  label="Client"
                >
                  {clients.map((client) => (
                    <MenuItem key={client.id} value={client.id}>
                      {client.companyName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Site Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Site Type</InputLabel>
                <Select
                  value={formData.siteType}
                  onChange={(e) => setFormData(prev => ({ ...prev, siteType: e.target.value }))}
                  label="Site Type"
                >
                  {SITE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Latitude"
                type="number"
                value={formData.latitude}
                onChange={(e) => setFormData(prev => ({ ...prev, latitude: e.target.value }))}
                required
                inputProps={{ step: 'any' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Longitude"
                type="number"
                value={formData.longitude}
                onChange={(e) => setFormData(prev => ({ ...prev, longitude: e.target.value }))}
                required
                inputProps={{ step: 'any' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Geofence Radius (meters)"
                type="number"
                value={formData.geofenceRadius}
                onChange={(e) => setFormData(prev => ({ ...prev, geofenceRadius: e.target.value }))}
                inputProps={{ min: 10, max: 1000 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Special Instructions"
                value={formData.specialInstructions}
                onChange={(e) => setFormData(prev => ({ ...prev, specialInstructions: e.target.value }))}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateSite} variant="contained">Create Site</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Site Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Site</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Client</InputLabel>
                <Select
                  value={formData.clientId}
                  onChange={(e) => setFormData(prev => ({ ...prev, clientId: e.target.value }))}
                  label="Client"
                >
                  {clients.map((client) => (
                    <MenuItem key={client.id} value={client.id}>
                      {client.companyName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Site Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Site Type</InputLabel>
                <Select
                  value={formData.siteType}
                  onChange={(e) => setFormData(prev => ({ ...prev, siteType: e.target.value }))}
                  label="Site Type"
                >
                  {SITE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Latitude"
                type="number"
                value={formData.latitude}
                onChange={(e) => setFormData(prev => ({ ...prev, latitude: e.target.value }))}
                required
                inputProps={{ step: 'any' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Longitude"
                type="number"
                value={formData.longitude}
                onChange={(e) => setFormData(prev => ({ ...prev, longitude: e.target.value }))}
                required
                inputProps={{ step: 'any' }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Geofence Radius (meters)"
                type="number"
                value={formData.geofenceRadius}
                onChange={(e) => setFormData(prev => ({ ...prev, geofenceRadius: e.target.value }))}
                inputProps={{ min: 10, max: 1000 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Special Instructions"
                value={formData.specialInstructions}
                onChange={(e) => setFormData(prev => ({ ...prev, specialInstructions: e.target.value }))}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateSite} variant="contained">Update Site</Button>
        </DialogActions>
      </Dialog>

      {/* Map View Dialog */}
      <Dialog open={mapViewOpen} onClose={() => setMapViewOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Sites Map View</DialogTitle>
        <DialogContent>
          <Box height={600}>
            <SiteMap sites={sites} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMapViewOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
