// BahinLink Web Admin Layout
// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY

import { Clerk<PERSON>rovider } from '@clerk/nextjs';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

// Real Clerk publishable key - MUST be production key
const CLERK_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

export const metadata = {
  title: 'BahinLink Admin - Security Workforce Management',
  description: 'Real-time security workforce management for Bahin SARL',
  keywords: 'security, workforce, management, GPS tracking, Senegal, Bahin SARL',
  authors: [{ name: 'Bahin SARL' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({ children }) {
  return (
    <ClerkProvider publishableKey={CLERK_PUBLISHABLE_KEY}>
      <html lang="en">
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </head>
        <body className={inter.className}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
