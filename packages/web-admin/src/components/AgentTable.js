// BahinLink Agent Table Component
// ⚠️ CRITICAL: Real agent data table with live GPS tracking ONLY

'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Avatar,
  Box,
  Typography,
  Tooltip,
  LinearProgress,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Edit,
  MoreVert,
  LocationOn,
  Phone,
  Email,
  Badge,
  TrendingUp,
  Schedule,
  Warning,
  CheckCircle,
  Block
} from '@mui/icons-material';

import { formatTime, formatDate } from '@bahinlink/shared';

const AgentTable = ({ 
  agents = [], 
  onEdit, 
  onRefresh, 
  pagination, 
  onPageChange 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedAgent, setSelectedAgent] = useState(null);

  const handleMenuOpen = (event, agent) => {
    setAnchorEl(event.currentTarget);
    setSelectedAgent(agent);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedAgent(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ON_SHIFT': return 'success';
      case 'AVAILABLE': return 'info';
      case 'OFFLINE': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ON_SHIFT': return <CheckCircle />;
      case 'AVAILABLE': return <Schedule />;
      case 'OFFLINE': return <Block />;
      default: return <Block />;
    }
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  const formatLocation = (location) => {
    if (!location) return 'No GPS';
    
    const { latitude, longitude, lastUpdate, withinGeofence } = location;
    const timeAgo = new Date() - new Date(lastUpdate);
    const minutesAgo = Math.floor(timeAgo / (1000 * 60));
    
    return (
      <Box>
        <Typography variant="body2">
          {latitude.toFixed(4)}, {longitude.toFixed(4)}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {minutesAgo < 1 ? 'Just now' : `${minutesAgo}m ago`}
          {withinGeofence !== undefined && (
            <Chip
              size="small"
              label={withinGeofence ? 'In Zone' : 'Out of Zone'}
              color={withinGeofence ? 'success' : 'warning'}
              sx={{ ml: 1, height: 16 }}
            />
          )}
        </Typography>
      </Box>
    );
  };

  const formatCurrentShift = (shift) => {
    if (!shift) return 'No active shift';
    
    return (
      <Box>
        <Typography variant="body2" fontWeight="bold">
          {shift.site.name}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
        </Typography>
        {shift.clockedIn && (
          <Chip
            size="small"
            label="Clocked In"
            color="success"
            sx={{ ml: 1, height: 16 }}
          />
        )}
      </Box>
    );
  };

  const handleChangePage = (event, newPage) => {
    onPageChange(newPage + 1);
  };

  const handleChangeRowsPerPage = (event) => {
    // Handle rows per page change if needed
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Agent</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Current Location</TableCell>
              <TableCell>Current Shift</TableCell>
              <TableCell>Performance</TableCell>
              <TableCell>Contact</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {agents.map((agent) => (
              <TableRow key={agent.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center">
                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                      {agent.user.firstName.charAt(0)}{agent.user.lastName.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {agent.user.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ID: {agent.employeeId}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Chip
                    icon={getStatusIcon(agent.status)}
                    label={agent.status.replace('_', ' ')}
                    color={getStatusColor(agent.status)}
                    size="small"
                  />
                  {!agent.isAvailable && (
                    <Chip
                      label="Unavailable"
                      color="error"
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  )}
                </TableCell>
                
                <TableCell>
                  {formatLocation(agent.location)}
                </TableCell>
                
                <TableCell>
                  {formatCurrentShift(agent.currentShift)}
                </TableCell>
                
                <TableCell>
                  {agent.performance ? (
                    <Box>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Typography variant="body2" sx={{ minWidth: 40 }}>
                          {Math.round(agent.performance.overallScore)}%
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={agent.performance.overallScore}
                          color={getPerformanceColor(agent.performance.overallScore)}
                          sx={{ flexGrow: 1, ml: 1 }}
                        />
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {agent.performance.totalShifts} shifts, {agent.performance.onTimePercentage}% on-time
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No data
                    </Typography>
                  )}
                </TableCell>
                
                <TableCell>
                  <Box>
                    <Box display="flex" alignItems="center" mb={0.5}>
                      <Phone sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {agent.user.phone || 'N/A'}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center">
                      <Email sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {agent.user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                
                <TableCell align="center">
                  <Tooltip title="Edit Agent">
                    <IconButton
                      size="small"
                      onClick={() => onEdit(agent)}
                      color="primary"
                    >
                      <Edit />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="More Actions">
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, agent)}
                    >
                      <MoreVert />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[25, 50, 100]}
        component="div"
        count={pagination.total}
        rowsPerPage={pagination.limit}
        page={pagination.page - 1}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          onEdit(selectedAgent);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Agent</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => {
          // View agent details
          handleMenuClose();
        }}>
          <ListItemIcon>
            <Badge fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => {
          // View location history
          handleMenuClose();
        }}>
          <ListItemIcon>
            <LocationOn fontSize="small" />
          </ListItemIcon>
          <ListItemText>Location History</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => {
          // View performance
          handleMenuClose();
        }}>
          <ListItemIcon>
            <TrendingUp fontSize="small" />
          </ListItemIcon>
          <ListItemText>Performance Report</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default AgentTable;
