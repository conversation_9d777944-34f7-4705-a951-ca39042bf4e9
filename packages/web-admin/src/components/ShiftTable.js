// BahinLink Web Admin Shift Table Component
// ⚠️ CRITICAL: Real shift management with live data ONLY

import React, { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Tooltip,
  Avatar,
  LinearProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { format, isToday, isTomorrow, isYesterday } from 'date-fns';

const ShiftTable = ({ 
  shifts = [], 
  loading = false, 
  onEdit, 
  onDelete, 
  onView,
  onStatusChange,
  showActions = true,
  compact = false 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedShift, setSelectedShift] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({});

  const handleMenuOpen = (event, shift) => {
    setAnchorEl(event.currentTarget);
    setSelectedShift(shift);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedShift(null);
  };

  const handleEdit = () => {
    if (selectedShift) {
      setEditFormData({
        id: selectedShift.id,
        status: selectedShift.status,
        specialInstructions: selectedShift.specialInstructions || ''
      });
      setEditDialogOpen(true);
    }
    handleMenuClose();
  };

  const handleEditSave = () => {
    if (onEdit && editFormData.id) {
      onEdit(editFormData.id, editFormData);
      setEditDialogOpen(false);
      setEditFormData({});
    }
  };

  const handleStatusChange = (shiftId, newStatus) => {
    if (onStatusChange) {
      onStatusChange(shiftId, newStatus);
    }
    handleMenuClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'SCHEDULED': return 'primary';
      case 'IN_PROGRESS': return 'success';
      case 'COMPLETED': return 'default';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'SCHEDULED': return <ScheduleIcon fontSize="small" />;
      case 'IN_PROGRESS': return <PlayIcon fontSize="small" />;
      case 'COMPLETED': return <StopIcon fontSize="small" />;
      case 'CANCELLED': return <DeleteIcon fontSize="small" />;
      default: return <ScheduleIcon fontSize="small" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'HH:mm');
  };

  const calculateProgress = (shift) => {
    if (shift.status !== 'IN_PROGRESS') return 0;
    
    const now = new Date();
    const start = new Date(shift.startTime);
    const end = new Date(shift.endTime);
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    const total = end - start;
    const elapsed = now - start;
    return Math.round((elapsed / total) * 100);
  };

  const getAgentInitials = (agent) => {
    if (!agent || !agent.user) return '?';
    const { firstName, lastName } = agent.user;
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  if (loading) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
          Loading shifts...
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <TableContainer component={Paper} elevation={1}>
        <Table size={compact ? 'small' : 'medium'}>
          <TableHead>
            <TableRow>
              <TableCell>Agent</TableCell>
              <TableCell>Site</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Time</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Progress</TableCell>
              {showActions && <TableCell align="right">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {shifts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showActions ? 8 : 7} align="center">
                  <Typography variant="body2" color="textSecondary" sx={{ py: 4 }}>
                    No shifts found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              shifts.map((shift) => {
                const progress = calculateProgress(shift);
                const isActive = shift.status === 'IN_PROGRESS';
                
                return (
                  <TableRow 
                    key={shift.id}
                    sx={{ 
                      '&:hover': { backgroundColor: 'action.hover' },
                      backgroundColor: isActive ? 'success.light' : 'inherit',
                      opacity: shift.status === 'CANCELLED' ? 0.6 : 1
                    }}
                  >
                    {/* Agent */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar 
                          sx={{ 
                            width: 32, 
                            height: 32, 
                            fontSize: '0.875rem',
                            bgcolor: isActive ? 'success.main' : 'primary.main'
                          }}
                        >
                          {getAgentInitials(shift.agent)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {shift.agent?.user ? 
                              `${shift.agent.user.firstName} ${shift.agent.user.lastName}` : 
                              'Unassigned'
                            }
                          </Typography>
                          {shift.agent?.employeeId && (
                            <Typography variant="caption" color="textSecondary">
                              ID: {shift.agent.employeeId}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>

                    {/* Site */}
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {shift.site?.name || 'Unknown Site'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {shift.site?.client?.companyName || 'Unknown Client'}
                        </Typography>
                      </Box>
                    </TableCell>

                    {/* Date */}
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(shift.shiftDate)}
                      </Typography>
                    </TableCell>

                    {/* Time */}
                    <TableCell>
                      <Typography variant="body2">
                        {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
                      </Typography>
                      {shift.actualStartTime && (
                        <Typography variant="caption" color="textSecondary" display="block">
                          Started: {formatTime(shift.actualStartTime)}
                        </Typography>
                      )}
                    </TableCell>

                    {/* Duration */}
                    <TableCell>
                      <Typography variant="body2">
                        {shift.scheduledDuration?.toFixed(1) || 0}h
                      </Typography>
                      {shift.hoursWorked > 0 && (
                        <Typography variant="caption" color="textSecondary" display="block">
                          Worked: {shift.hoursWorked.toFixed(1)}h
                        </Typography>
                      )}
                    </TableCell>

                    {/* Status */}
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(shift.status)}
                        label={shift.status}
                        color={getStatusColor(shift.status)}
                        size="small"
                        variant={isActive ? 'filled' : 'outlined'}
                      />
                    </TableCell>

                    {/* Progress */}
                    <TableCell>
                      {isActive ? (
                        <Box sx={{ width: '100%' }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={progress} 
                            sx={{ mb: 0.5 }}
                          />
                          <Typography variant="caption" color="textSecondary">
                            {progress}%
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="caption" color="textSecondary">
                          {shift.status === 'COMPLETED' ? '100%' : '-'}
                        </Typography>
                      )}
                    </TableCell>

                    {/* Actions */}
                    {showActions && (
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, shift)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => onView && onView(selectedShift?.id)}>
          <ViewIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={handleEdit}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit Shift
        </MenuItem>
        {selectedShift?.status === 'SCHEDULED' && (
          <MenuItem onClick={() => handleStatusChange(selectedShift.id, 'IN_PROGRESS')}>
            <PlayIcon fontSize="small" sx={{ mr: 1 }} />
            Start Shift
          </MenuItem>
        )}
        {selectedShift?.status === 'IN_PROGRESS' && (
          <MenuItem onClick={() => handleStatusChange(selectedShift.id, 'COMPLETED')}>
            <StopIcon fontSize="small" sx={{ mr: 1 }} />
            Complete Shift
          </MenuItem>
        )}
        <MenuItem 
          onClick={() => onDelete && onDelete(selectedShift?.id)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Cancel Shift
        </MenuItem>
      </Menu>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Shift</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={editFormData.status || ''}
                label="Status"
                onChange={(e) => setEditFormData(prev => ({ ...prev, status: e.target.value }))}
              >
                <MenuItem value="SCHEDULED">Scheduled</MenuItem>
                <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                <MenuItem value="COMPLETED">Completed</MenuItem>
                <MenuItem value="CANCELLED">Cancelled</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              fullWidth
              label="Special Instructions"
              multiline
              rows={3}
              value={editFormData.specialInstructions || ''}
              onChange={(e) => setEditFormData(prev => ({ ...prev, specialInstructions: e.target.value }))}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleEditSave} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ShiftTable;
