// BahinLink Web Admin Report Table Component
// ⚠️ CRITICAL: Real report management with approval workflow ONLY

import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Avatar,
  LinearProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Rating,
  Tooltip,
  Badge
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Attachment as AttachmentIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';

const ReportTable = ({ 
  reports = [], 
  loading = false, 
  onView, 
  onApprove, 
  onReject, 
  onEdit,
  onDelete,
  showActions = true,
  compact = false 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewAction, setReviewAction] = useState('');
  const [reviewData, setReviewData] = useState({
    notes: '',
    qualityScore: 5
  });

  const handleMenuOpen = (event, report) => {
    setAnchorEl(event.currentTarget);
    setSelectedReport(report);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedReport(null);
  };

  const handleReviewOpen = (action) => {
    setReviewAction(action);
    setReviewDialogOpen(true);
    handleMenuClose();
  };

  const handleReviewSubmit = () => {
    if (selectedReport) {
      if (reviewAction === 'approve' && onApprove) {
        onApprove(selectedReport.id, reviewData);
      } else if (reviewAction === 'reject' && onReject) {
        onReject(selectedReport.id, reviewData);
      }
    }
    setReviewDialogOpen(false);
    setReviewData({ notes: '', qualityScore: 5 });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'DRAFT': return 'default';
      case 'SUBMITTED': return 'warning';
      case 'APPROVED': return 'success';
      case 'REJECTED': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'LOW': return 'default';
      case 'MEDIUM': return 'primary';
      case 'HIGH': return 'warning';
      case 'URGENT': return 'error';
      default: return 'default';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'INCIDENT': return '🚨';
      case 'MAINTENANCE': return '🔧';
      case 'SECURITY': return '🛡️';
      case 'SAFETY': return '⚠️';
      case 'GENERAL': return '📝';
      default: return '📄';
    }
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, HH:mm');
  };

  const getAgentInitials = (agent) => {
    if (!agent || !agent.user) return '?';
    const { firstName, lastName } = agent.user;
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const isOverdue = (report) => {
    if (report.status !== 'SUBMITTED') return false;
    const submittedAt = new Date(report.submittedAt);
    const now = new Date();
    const hoursSinceSubmission = (now - submittedAt) / (1000 * 60 * 60);
    return hoursSinceSubmission > 24; // Overdue after 24 hours
  };

  if (loading) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
          Loading reports...
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <TableContainer component={Paper} elevation={1}>
        <Table size={compact ? 'small' : 'medium'}>
          <TableHead>
            <TableRow>
              <TableCell>Report</TableCell>
              <TableCell>Agent</TableCell>
              <TableCell>Site</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Quality</TableCell>
              {showActions && <TableCell align="right">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {reports.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showActions ? 9 : 8} align="center">
                  <Typography variant="body2" color="textSecondary" sx={{ py: 4 }}>
                    No reports found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              reports.map((report) => {
                const overdue = isOverdue(report);
                
                return (
                  <TableRow 
                    key={report.id}
                    sx={{ 
                      '&:hover': { backgroundColor: 'action.hover' },
                      backgroundColor: overdue ? 'error.light' : 'inherit',
                      opacity: report.status === 'REJECTED' ? 0.7 : 1
                    }}
                  >
                    {/* Report */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" component="span">
                          {getTypeIcon(report.type)}
                        </Typography>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {report.title}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {report.description?.substring(0, 50)}
                            {report.description?.length > 50 ? '...' : ''}
                          </Typography>
                          {report.attachments?.length > 0 && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                              <AttachmentIcon fontSize="small" color="action" />
                              <Typography variant="caption" color="textSecondary" sx={{ ml: 0.5 }}>
                                {report.attachments.length} attachment(s)
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </TableCell>

                    {/* Agent */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ width: 32, height: 32, fontSize: '0.875rem' }}>
                          {getAgentInitials(report.agent)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {report.agent?.user ? 
                              `${report.agent.user.firstName} ${report.agent.user.lastName}` : 
                              'Unknown'
                            }
                          </Typography>
                          {report.agent?.employeeId && (
                            <Typography variant="caption" color="textSecondary">
                              ID: {report.agent.employeeId}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>

                    {/* Site */}
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {report.site?.name || 'Unknown Site'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {report.site?.client?.companyName || 'Unknown Client'}
                        </Typography>
                      </Box>
                    </TableCell>

                    {/* Type */}
                    <TableCell>
                      <Chip
                        label={report.type}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>

                    {/* Priority */}
                    <TableCell>
                      <Chip
                        label={report.priority}
                        color={getPriorityColor(report.priority)}
                        size="small"
                        variant={report.priority === 'URGENT' ? 'filled' : 'outlined'}
                      />
                    </TableCell>

                    {/* Status */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={report.status}
                          color={getStatusColor(report.status)}
                          size="small"
                          variant={report.status === 'APPROVED' ? 'filled' : 'outlined'}
                        />
                        {overdue && (
                          <Tooltip title="Overdue for review">
                            <WarningIcon color="error" fontSize="small" />
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>

                    {/* Created */}
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(report.createdAt)}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {formatDistanceToNow(new Date(report.createdAt), { addSuffix: true })}
                      </Typography>
                    </TableCell>

                    {/* Quality */}
                    <TableCell>
                      {report.qualityScore ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Rating 
                            value={report.qualityScore} 
                            readOnly 
                            size="small" 
                            precision={0.5}
                          />
                          <Typography variant="caption" color="textSecondary">
                            ({report.qualityScore}/5)
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="caption" color="textSecondary">
                          Not rated
                        </Typography>
                      )}
                    </TableCell>

                    {/* Actions */}
                    {showActions && (
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, report)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => onView && onView(selectedReport?.id)}>
          <ViewIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        {selectedReport?.status === 'SUBMITTED' && (
          <>
            <MenuItem onClick={() => handleReviewOpen('approve')}>
              <ApproveIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
              Approve Report
            </MenuItem>
            <MenuItem onClick={() => handleReviewOpen('reject')}>
              <RejectIcon fontSize="small" sx={{ mr: 1, color: 'error.main' }} />
              Reject Report
            </MenuItem>
          </>
        )}
        <MenuItem onClick={() => onEdit && onEdit(selectedReport?.id)}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit Report
        </MenuItem>
        <MenuItem 
          onClick={() => onDelete && onDelete(selectedReport?.id)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete Report
        </MenuItem>
      </Menu>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {reviewAction === 'approve' ? 'Approve Report' : 'Reject Report'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography variant="body2" color="textSecondary">
              Report: {selectedReport?.title}
            </Typography>
            
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Quality Score
              </Typography>
              <Rating
                value={reviewData.qualityScore}
                onChange={(event, newValue) => {
                  setReviewData(prev => ({ ...prev, qualityScore: newValue }));
                }}
                precision={0.5}
              />
            </Box>
            
            <TextField
              fullWidth
              label={reviewAction === 'approve' ? 'Review Notes (Optional)' : 'Rejection Reason'}
              multiline
              rows={3}
              value={reviewData.notes}
              onChange={(e) => setReviewData(prev => ({ ...prev, notes: e.target.value }))}
              required={reviewAction === 'reject'}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleReviewSubmit} 
            variant="contained"
            color={reviewAction === 'approve' ? 'success' : 'error'}
          >
            {reviewAction === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ReportTable;
