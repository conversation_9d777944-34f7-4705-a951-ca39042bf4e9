// BahinLink Web Admin API Service
// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY

import axios from 'axios';
import { auth } from '@clerk/nextjs';

// Real production API base URL
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:3000/api' 
  : 'https://api.bahinlink.com/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for real authentication
   */
  setupInterceptors() {
    // Request interceptor to add real Clerk token
    axios.interceptors.request.use(
      async (config) => {
        try {
          // Get real Clerk token from server-side auth
          const { getToken } = auth();
          const token = await getToken();
          
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          
          config.headers['Content-Type'] = 'application/json';
          config.baseURL = this.baseURL;
          
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
          return config;
        } catch (error) {
          console.error('Request interceptor error:', error);
          return config;
        }
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    axios.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response.data;
      },
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        
        if (error.response?.status === 401) {
          // Redirect to sign-in
          window.location.href = '/sign-in';
        }
        
        return Promise.reject(error.response?.data || error);
      }
    );
  }

  /**
   * GET request to real API
   */
  async get(endpoint, params = {}) {
    try {
      const response = await axios.get(endpoint, { params });
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST request to real API
   */
  async post(endpoint, data = {}) {
    try {
      const response = await axios.post(endpoint, data);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT request to real API
   */
  async put(endpoint, data = {}) {
    try {
      const response = await axios.put(endpoint, data);
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * DELETE request to real API
   */
  async delete(endpoint) {
    try {
      const response = await axios.delete(endpoint);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // Dashboard Analytics
  async getDashboardAnalytics() {
    return this.get('/analytics/dashboard');
  }

  async getPerformanceMetrics(params = {}) {
    return this.get('/analytics/performance', params);
  }

  async getAgentLocations() {
    return this.get('/analytics/locations');
  }

  // User Management
  async getUsers(params = {}) {
    return this.get('/users', params);
  }

  async getUser(userId) {
    return this.get(`/users/${userId}`);
  }

  async updateUser(userId, data) {
    return this.put(`/users/${userId}`, data);
  }

  // Agent Management
  async getAgents(params = {}) {
    return this.get('/agents', params);
  }

  async getAgent(agentId) {
    return this.get(`/agents/${agentId}`);
  }

  async updateAgent(agentId, data) {
    return this.put(`/agents/${agentId}`, data);
  }

  async getNearbyAgents(latitude, longitude, radius) {
    return this.get('/agents/nearby', { latitude, longitude, radius });
  }

  // Site Management
  async getSites(params = {}) {
    return this.get('/sites', params);
  }

  async createSite(data) {
    return this.post('/sites', data);
  }

  async getSite(siteId) {
    return this.get(`/sites/${siteId}`);
  }

  async updateSite(siteId, data) {
    return this.put(`/sites/${siteId}`, data);
  }

  async generateSiteQR(siteId) {
    return this.post(`/sites/${siteId}/generate-qr`);
  }

  // Shift Management
  async getShifts(params = {}) {
    return this.get('/shifts', params);
  }

  async createShift(data) {
    return this.post('/shifts', data);
  }

  async getShift(shiftId) {
    return this.get(`/shifts/${shiftId}`);
  }

  async updateShift(shiftId, data) {
    return this.put(`/shifts/${shiftId}`, data);
  }

  async assignShift(shiftId, agentId) {
    return this.put(`/shifts/${shiftId}`, { agentId });
  }

  // Time Tracking
  async getTimeEntries(params = {}) {
    return this.get('/time/entries', params);
  }

  async verifyTimeEntry(timeEntryId, data) {
    return this.put(`/time/entries/${timeEntryId}/verify`, data);
  }

  // Reports Management
  async getReports(params = {}) {
    return this.get('/reports', params);
  }

  async getReport(reportId) {
    return this.get(`/reports/${reportId}`);
  }

  async approveReport(reportId, data = {}) {
    return this.post(`/reports/${reportId}/approve`, data);
  }

  async rejectReport(reportId, data) {
    return this.post(`/reports/${reportId}/reject`, data);
  }

  // Notifications
  async getNotifications(params = {}) {
    return this.get('/notifications', params);
  }

  async sendNotification(data) {
    return this.post('/notifications', data);
  }

  async broadcastNotification(data) {
    return this.post('/notifications/broadcast', data);
  }

  // Communications
  async getMessages(params = {}) {
    return this.get('/communications', params);
  }

  async sendMessage(data) {
    return this.post('/communications', data);
  }

  async getMessageThreads(params = {}) {
    return this.get('/communications/threads', params);
  }

  // Client Management
  async getClients(params = {}) {
    return this.get('/clients', params);
  }

  async getClientRequests(params = {}) {
    return this.get('/client/requests', params);
  }

  async updateClientRequest(requestId, data) {
    return this.put(`/client/requests/${requestId}`, data);
  }

  // Geofencing
  async checkGeofence(data) {
    return this.post('/geofence/check', data);
  }

  async getGeofenceViolations(params = {}) {
    return this.get('/geofence/violations', params);
  }

  async resolveGeofenceViolation(violationId, data) {
    return this.put(`/geofence/violations/${violationId}/resolve`, data);
  }

  // File Management
  async uploadFile(file, type = 'document') {
    const formData = new FormData();
    formData.append('file', file);

    const endpoint = `/upload/${type}`;
    
    try {
      const response = await axios.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        baseURL: this.baseURL
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Bulk Operations
  async bulkUpdateShifts(shiftIds, data) {
    return this.put('/shifts/bulk', { shiftIds, ...data });
  }

  async bulkNotifyAgents(agentIds, notification) {
    return this.post('/notifications/bulk', { agentIds, ...notification });
  }

  // Export Functions
  async exportReports(params = {}) {
    return this.get('/reports/export', params);
  }

  async exportTimeEntries(params = {}) {
    return this.get('/time/entries/export', params);
  }

  async exportAgentPerformance(params = {}) {
    return this.get('/analytics/performance/export', params);
  }
}

export default new ApiService();
